<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kbc-elms</artifactId>
        <groupId>com.kbao</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-elms-entity</artifactId>
    <name>kbc-elms-entity</name>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>
    <dependencies>
        <!-- API文档配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
            <scope>compile</scope>
        </dependency>
        <!-- API文档配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bsc-entity</artifactId>
        </dependency>

    </dependencies>

</project>
