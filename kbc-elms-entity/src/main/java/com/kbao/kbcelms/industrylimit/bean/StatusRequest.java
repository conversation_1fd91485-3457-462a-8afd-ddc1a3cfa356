package com.kbao.kbcelms.industrylimit.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 状态切换请求参数
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@ApiModel(value = "StatusRequest", description = "状态切换请求参数")
public class StatusRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态：0-禁用，1-启用", required = true, example = "1")
    private Integer status;
}