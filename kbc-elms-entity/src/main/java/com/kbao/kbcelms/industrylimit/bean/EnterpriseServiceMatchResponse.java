package com.kbao.kbcelms.industrylimit.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 企业服务匹配响应结果
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@ApiModel(value = "EnterpriseServiceMatchResponse", description = "企业服务匹配响应结果")
public class EnterpriseServiceMatchResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    /**
     * 企业统一社会信用代码
     */
    @ApiModelProperty(value = "企业统一社会信用代码")
    private String creditCode;

    /**
     * 企业行业分类代码
     */
    @ApiModelProperty(value = "企业行业分类代码")
    private String industryCode;

    /**
     * 企业行业分类名称
     */
    @ApiModelProperty(value = "企业行业分类名称")
    private String industryName;

    /**
     * 企业规模
     */
    @ApiModelProperty(value = "企业规模")
    private String enterpriseScale;

    /**
     * 可启动的服务ID列表
     */
    @ApiModelProperty(value = "可启动的服务ID列表")
    private List<String> availableServiceIds;

    /**
     * 匹配的规则列表（当includeDetails=true时返回）
     */
    @ApiModelProperty(value = "匹配的规则列表")
    private List<MatchedRuleInfo> matchedRules;

    /**
     * 匹配规则信息
     */
    @Data
    @ApiModel(value = "MatchedRuleInfo", description = "匹配规则信息")
    public static class MatchedRuleInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 规则ID
         */
        @ApiModelProperty(value = "规则ID")
        private Long ruleId;

        /**
         * 规则名称
         */
        @ApiModelProperty(value = "规则名称")
        private String ruleName;

        /**
         * 规则编码
         */
        @ApiModelProperty(value = "规则编码")
        private String ruleCode;

        /**
         * 规则描述
         */
        @ApiModelProperty(value = "规则描述")
        private String ruleDescription;

        /**
         * 匹配的服务ID列表
         */
        @ApiModelProperty(value = "匹配的服务ID列表")
        private List<String> serviceIds;

        /**
         * 匹配结果（true-匹配成功，false-匹配失败）
         */
        @ApiModelProperty(value = "匹配结果")
        private Boolean matched;

        /**
         * 匹配说明
         */
        @ApiModelProperty(value = "匹配说明")
        private String matchDescription;
    }
}