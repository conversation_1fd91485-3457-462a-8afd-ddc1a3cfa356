package com.kbao.kbcelms.industrylimit.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 企业服务匹配请求参数
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@ApiModel(value = "EnterpriseServiceMatchRequest", description = "企业服务匹配请求参数")
public class EnterpriseServiceMatchRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @NotBlank(message = "企业ID不能为空")
    @ApiModelProperty(value = "企业ID", required = true)
    private String enterpriseId;

    /**
     * 是否返回详细信息（默认false）
     */
    @ApiModelProperty(value = "是否返回详细信息", notes = "true-返回匹配规则详情，false-只返回服务ID列表")
    private Boolean includeDetails = false;

    /**
     * 是否只返回启用的规则（默认true）
     */
    @ApiModelProperty(value = "是否只返回启用的规则")
    private Boolean onlyEnabled = true;
}