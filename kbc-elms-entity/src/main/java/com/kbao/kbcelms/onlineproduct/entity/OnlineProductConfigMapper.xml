<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.onlineproduct.dao.OnlineProductConfigMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="industry_code" property="industryCode" jdbcType="VARCHAR"/>
        <result column="industry_name" property="industryName" jdbcType="VARCHAR"/>
        <result column="probability" property="probability" jdbcType="VARCHAR"/>
        <result column="impact" property="impact" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="VARCHAR"/>
        <result column="insurance_types" property="insuranceTypes" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="INTEGER"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- VO结果映射 -->
    <resultMap id="VOResultMap" type="com.kbao.kbcelms.onlineproduct.vo.OnlineProductConfigVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="industry_code" property="industryCodeStr" jdbcType="VARCHAR"/>
        <result column="industry_name" property="industryName" jdbcType="VARCHAR"/>
        <result column="probability" property="probability" jdbcType="VARCHAR"/>
        <result column="impact" property="impact" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="VARCHAR"/>
        <result column="insurance_types" property="insuranceTypesStr" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, industry_code, industry_name, probability, impact, level, 
        insurance_types, enabled, description, create_time, update_time, 
        create_user, update_user, tenant_id, is_deleted
    </sql>

    <!-- 查询字段 -->
    <sql id="Select_Column_List">
        id, industry_code, industry_name, probability, impact, level, 
        insurance_types, enabled, description, create_time, update_time, 
        create_user, update_user
    </sql>

    <!-- 查询条件 -->
    <sql id="Where_Clause">
        <where>
            is_deleted = 0
            <if test="industryCode != null and industryCode.size() > 0">
                AND (
                <foreach collection="industryCode" item="code" separator=" OR ">
                    <if test="code != null and code != ''">
                        industry_code LIKE CONCAT('%', #{code}, '%')
                    </if>
                </foreach>
                )
            </if>
            <if test="industryName != null and industryName != ''">
                AND industry_name LIKE CONCAT('%', #{industryName}, '%')
            </if>
            <if test="probability != null and probability != ''">
                AND probability = #{probability}
            </if>
            <if test="impact != null and impact != ''">
                AND impact = #{impact}
            </if>
            <if test="level != null and level != ''">
                AND level = #{level}
            </if>
            <if test="enabled != null">
                AND enabled = #{enabled}
            </if>
            <if test="createUser != null and createUser != ''">
                AND create_user = #{createUser}
            </if>
        </where>
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_online_product_config
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <!-- 查询线上产品配置列表 -->
    <select id="selectOnlineProductConfigList" resultMap="VOResultMap">
        SELECT <include refid="Select_Column_List"/>
        FROM t_online_product_config
        <include refid="Where_Clause"/>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询线上产品配置详情 -->
    <select id="selectOnlineProductConfigById" parameterType="java.lang.Long" resultMap="VOResultMap">
        SELECT <include refid="Select_Column_List"/>
        FROM t_online_product_config
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <!-- 检查行业代码和风险等级组合是否存在 -->
    <select id="checkConfigExists" resultType="int">
        SELECT COUNT(1)
        FROM t_online_product_config
        WHERE industry_code in
        <foreach collection="industryCode" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND probability = #{probability}
        AND impact = #{impact}
        AND level = #{level}
        AND is_deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <!-- 根据参数查询 -->
    <select id="selectByParam" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_online_product_config
        <include refid="Where_Clause"/>
        ORDER BY create_time DESC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_online_product_config (
            industry_code, industry_name, probability, impact, level, 
            insurance_types, enabled, description, create_time, update_time,
            create_user, update_user, tenant_id, is_deleted
        ) VALUES (
            #{industryCode}, #{industryName}, #{probability}, #{impact}, #{level},
            #{insuranceTypes}, #{enabled}, #{description}, #{createTime}, #{updateTime},
            #{createUser}, #{updateUser}, #{tenantId}, #{isDeleted}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_online_product_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryCode != null">industry_code,</if>
            <if test="industryName != null">industry_name,</if>
            <if test="probability != null">probability,</if>
            <if test="impact != null">impact,</if>
            <if test="level != null">level,</if>
            <if test="insuranceTypes != null">insurance_types,</if>
            <if test="enabled != null">enabled,</if>
            <if test="description != null">description,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="isDeleted != null">is_deleted,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="industryCode != null">#{industryCode},</if>
            <if test="industryName != null">#{industryName},</if>
            <if test="probability != null">#{probability},</if>
            <if test="impact != null">#{impact},</if>
            <if test="level != null">#{level},</if>
            <if test="insuranceTypes != null">#{insuranceTypes},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="description != null">#{description},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
        </trim>
    </insert>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.onlineproduct.entity.OnlineProductConfig">
        UPDATE t_online_product_config
        <set>
            <if test="industryCode != null">industry_code = #{industryCode},</if>
            <if test="industryName != null">industry_name = #{industryName},</if>
            <if test="probability != null">probability = #{probability},</if>
            <if test="impact != null">impact = #{impact},</if>
            <if test="level != null">level = #{level},</if>
            <if test="insuranceTypes != null">insurance_types = #{insuranceTypes},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        UPDATE t_online_product_config SET is_deleted = 1 WHERE id = #{id}
    </update>

    <!-- 根据行业代码查询线上产品配置 -->
    <select id="selectByIndustryCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_online_product_config
        WHERE is_deleted = 0 
        AND enabled = 1
        AND (
            industry_code LIKE CONCAT('%,', #{industryCode}, ',%')
            OR industry_code LIKE CONCAT(#{industryCode}, ',%')
            OR industry_code LIKE CONCAT('%,', #{industryCode})
            OR industry_code = #{industryCode}
        )
        ORDER BY create_time DESC
    </select>

</mapper>
