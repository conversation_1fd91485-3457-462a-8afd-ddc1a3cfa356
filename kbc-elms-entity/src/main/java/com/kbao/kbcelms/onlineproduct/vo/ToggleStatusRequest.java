package com.kbao.kbcelms.onlineproduct.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 线上产品配置状态切换请求VO
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@ApiModel(value = "ToggleStatusRequest", description = "线上产品配置状态切换请求")
public class ToggleStatusRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空")
    @ApiModelProperty(value = "配置ID", required = true, example = "1")
    private Long id;

    /**
     * 是否启用
     */
    @NotNull(message = "启用状态不能为空")
    @ApiModelProperty(value = "是否启用", required = true, example = "true")
    private Boolean enabled;
}
