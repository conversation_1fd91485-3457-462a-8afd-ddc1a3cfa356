package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: OpportunityDetailVO
 * @Description: 整合机会主表、公司表及详情表数据，用于列表展示或详情页展示 - 用于项目变更列表展示
 * @Author: luobb
 * @Date: 2025/7/30 09:16
 * @Version: 1.0
 */
@Data
@ApiModel(description = "机会详情VO")
public class OpportunityDetailVO {
    /** 机会ID */
    @ApiModelProperty("机会ID")
    private Integer id;
    
    /** 机会名称 */
    @ApiModelProperty("机会名称")
    private String opportunityName;
    
    /** 代理人编码 */
    @ApiModelProperty("代理人编码")
    private String agentCode;
    
    /** 代理人姓名 */
    @ApiModelProperty("代理人姓名")
    private String agentName;
    
    /** 公司编码 */
    @ApiModelProperty("公司编码")
    private String companyCode;
    
    /** 公司名称 */
    @ApiModelProperty("公司名称")
    private String companyName;
    
    /** 机会类型（如：个人/企业） */
    @ApiModelProperty("机会类型（如：个人/企业）")
    private String opportunityType;
    
    /** 交易中心名称 */
    @ApiModelProperty("交易中心名称")
    private String tradingCenterName;
    
    /** 销售中心名称 */
    @ApiModelProperty("销售中心名称")
    private String salesCenterName;
    
    /** 行业编码 */
    @ApiModelProperty("行业编码")
    private String industryCode;
    
    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    /** 机会状态（1：进行中，2：已完成，3：已终止等） */
    @ApiModelProperty("机会状态（1：进行中，2：已完成，3：已终止等）")
    private Integer status;
    
    /** 客户需求 */
    @ApiModelProperty("客户需求")
    private String generalInsuranceType;
    
    /** 投保人数 */
    @ApiModelProperty("投保人数")
    private Integer insureNum;
    
    /** 保费预算 */
    @ApiModelProperty("保费预算")
    private BigDecimal premiumBudget;
    
    /** 是否投标（0：否，1：是） */
    @ApiModelProperty("是否投标（0：否，1：是）")
    private Integer isBid;
    
    /** 企业ID */
    @ApiModelProperty("企业ID")
    private Integer enterpriseId;
    
    /** 企业名称 */
    @ApiModelProperty("企业名称")
    private String enterpriseName;
    
    /** 社会信用代码 */
    @ApiModelProperty("社会信用代码")
    private String creditCode;
    
    /** 企业类型（A-央企，B-上市公司，C-大型企业，D-中小企业） */
    @ApiModelProperty("企业类型（A-央企，B-上市公司，C-大型企业，D-中小企业）")
    private String dtType;
    
    /** 提交时间 */
    @ApiModelProperty("提交时间")
    private Date submitTime;
    
    /** 组队时间 */
    @ApiModelProperty("组队时间")
    private Date teamTime;
    
    /** 日志时间 */
    @ApiModelProperty("日志时间")
    private Date logTime;

    @ApiModelProperty("第三方报告查询时间,验证时间")
    private Date verifyTime;

    @ApiModelProperty("KYC报告时间")
    private Date kycReportTime;
    
    /** KYC报告URL */
    @ApiModelProperty("KYC报告URL")
    private String kycReportUrl;

    
    /** 风险报告URL */
    @ApiModelProperty("风险报告URL")
    private String riskReportUrl;
    
    /** 机会总结时间 */
    @ApiModelProperty("机会总结时间")
    private Date summaryTime;
    
    /** 项目负责人 */
    @ApiModelProperty("项目负责人")
    private String projectManagerName;
    
    /** 是否添加健康服务产品 */
    @ApiModelProperty("是否添加健康服务产品")
    private String addHealthService;
    
    /** 健康服务编码 */
    @ApiModelProperty("健康服务编码")
    private String healthServiceCode;
    
    /** 健康服务名称 */
    @ApiModelProperty("健康服务名称")
    private String healthServiceName;
    
    /** 是否添加救援服务产品 */
    @ApiModelProperty("是否添加救援服务产品")
    private String addRescueService;
    
    /** 救援服务编码 */
    @ApiModelProperty("救援服务编码")
    private String rescueServiceCode;
    
    /** 救援服务名称 */
    @ApiModelProperty("救援服务名称")
    private String rescueServiceName;
}