package com.kbao.kbcelms.industry.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "IndustryTreeVO", description = "行业树形结构VO")
@Data
public class IndustryTreeVO{
    @ApiModelProperty(value = "节点值（code）")
    private String value;
    @ApiModelProperty(value = "节点名称（name）")
    private String label;
    @ApiModelProperty(value = "行业级别")
    private Integer level;
    @ApiModelProperty(value = "完整路径")
    private String fullPath;
    @ApiModelProperty(value = "完整名称")
    private String fullName;
    @ApiModelProperty(value = "子节点")
    private List<IndustryTreeVO> children = new ArrayList<>();

    // 默认构造函数，用于Jackson反序列化
    public IndustryTreeVO() {
    }

    public IndustryTreeVO(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public IndustryTreeVO(String value, String label, Integer level, String fullPath, String fullName) {
        this.value = value;
        this.label = label;
        this.level = level;
        this.fullPath = fullPath;
        this.fullName = fullName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public List<IndustryTreeVO> getChildren() {
        return children;
    }

    public void setChildren(List<IndustryTreeVO> children) {
        this.children = children;
    }
}