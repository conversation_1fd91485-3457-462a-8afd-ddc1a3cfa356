package com.kbao.kbcelms.enterprise.query.record.model;

import com.alibaba.fastjson.JSONObject;import io.swagger.annotations.ApiModelProperty;import org.springframework.data.annotation.Id;import org.springframework.data.mongodb.core.index.Indexed;import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;import java.util.Date;

/**
 * 企业查询记录MongoDB实体
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@Document(collection = "enterprise_query_record")
public class EnterpriseQueryRecord implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * MongoDB主键
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 查询人姓名
     */
    private String agentName;

    /**
     * 查询人工号
     */
    @Indexed
    private String agentCode;

    /**
     * 机构名称
     */
    private String legalName;

    /**
     * 机构编码
     */
    private String legalCode;

    /**
     * 营业部名称
     */
    private String tradingCenterName;


    /**
     * 营业部编码
     */
    private String tradingCenterCode;

    /**
     * 输入的企业名称
     */
    private String inputEnterpriseName;

    /**
     * 是否已有数据
     */
    private Boolean hasData;

    /**
     * 实际企业名称
     */
    private String enterpriseName;

    /**
     * 验真返回结果
     */
    private JSONObject resultData;

    /**
     * 是否拦截
     */
    private Boolean isBlocked;

    /**
     * 验真时间
     */
    private Date verifyTime;

    private String tenantId;
}
