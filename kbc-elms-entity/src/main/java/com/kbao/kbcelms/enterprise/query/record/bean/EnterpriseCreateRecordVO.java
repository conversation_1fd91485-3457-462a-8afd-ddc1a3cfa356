package com.kbao.kbcelms.enterprise.query.record.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 企业创建记录查询VO
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@ApiModel(value = "EnterpriseCreateRecordVO", description = "企业创建记录查询参数")
public class EnterpriseCreateRecordVO {

    @ApiModelProperty(value = "查询人姓名")
    private String agentName;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;
}
