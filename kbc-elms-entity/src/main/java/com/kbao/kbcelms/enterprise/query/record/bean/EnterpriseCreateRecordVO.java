package com.kbao.kbcelms.enterprise.query.record.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 企业创建记录查询VO
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@ApiModel(value = "EnterpriseCreateRecordVO", description = "企业创建记录查询参数")
public class EnterpriseCreateRecordVO {

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "代理人编码")
    private String agentCode;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "所属行业")
    private String categoryName;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
