package com.kbao.kbcelms.riskconfig.util;

import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 富文本内容处理工具类
 * <AUTHOR>
 * @date 2025-08-21
 */
public class RichTextContentUtils {

    /**
     * 最大内容长度
     */
    public static final int MAX_CONTENT_LENGTH = 10000;

    /**
     * 危险脚本模式
     */
    private static final Pattern[] DANGEROUS_PATTERNS = {
            Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
            Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
            Pattern.compile("on\\w+\\s*=", Pattern.CASE_INSENSITIVE),
            Pattern.compile("expression\\s*\\(", Pattern.CASE_INSENSITIVE),
            Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE),
            Pattern.compile("data:text/html", Pattern.CASE_INSENSITIVE)
    };

    /**
     * 验证富文本内容
     * @param content 富文本内容
     * @throws RuntimeException 验证失败时抛出异常
     */
    public static void validateContent(String content) {
        if (!StringUtils.hasText(content)) {
            return;
        }

        // 检查内容长度
        if (content.length() > MAX_CONTENT_LENGTH) {
            throw new RuntimeException("富文本详细说明内容不能超过" + MAX_CONTENT_LENGTH + "个字符");
        }

        // 检查是否包含危险脚本
        for (Pattern pattern : DANGEROUS_PATTERNS) {
            if (pattern.matcher(content).find()) {
                throw new RuntimeException("富文本内容包含不安全的脚本代码");
            }
        }
    }

    /**
     * 清理富文本内容中的危险脚本
     * @param content 原始内容
     * @return 清理后的内容
     */
    public static String sanitizeContent(String content) {
        if (!StringUtils.hasText(content)) {
            return content;
        }

        String sanitized = content;
        
        // 移除危险脚本
        for (Pattern pattern : DANGEROUS_PATTERNS) {
            sanitized = pattern.matcher(sanitized).replaceAll("");
        }

        return sanitized;
    }

    /**
     * 提取纯文本内容（移除HTML标签）
     * @param htmlContent HTML内容
     * @return 纯文本内容
     */
    public static String extractPlainText(String htmlContent) {
        if (!StringUtils.hasText(htmlContent)) {
            return "";
        }

        // 简单的HTML标签移除（生产环境建议使用专业的HTML解析库如Jsoup）
        return htmlContent.replaceAll("<[^>]+>", "").trim();
    }

    /**
     * 计算纯文本长度
     * @param htmlContent HTML内容
     * @return 纯文本长度
     */
    public static int getPlainTextLength(String htmlContent) {
        return extractPlainText(htmlContent).length();
    }

    /**
     * 截取富文本内容摘要
     * @param htmlContent HTML内容
     * @param maxLength 最大长度
     * @return 摘要内容
     */
    public static String extractSummary(String htmlContent, int maxLength) {
        String plainText = extractPlainText(htmlContent);
        if (plainText.length() <= maxLength) {
            return plainText;
        }
        return plainText.substring(0, maxLength) + "...";
    }

    /**
     * 检查内容是否为空（忽略HTML标签）
     * @param htmlContent HTML内容
     * @return 是否为空
     */
    public static boolean isEmpty(String htmlContent) {
        return !StringUtils.hasText(extractPlainText(htmlContent));
    }

    /**
     * 格式化内容用于日志记录
     * @param content 内容
     * @return 格式化后的内容
     */
    public static String formatForLog(String content) {
        if (!StringUtils.hasText(content)) {
            return "[空内容]";
        }

        String plainText = extractPlainText(content);
        if (plainText.length() > 100) {
            return plainText.substring(0, 100) + "... (总长度: " + plainText.length() + ")";
        }
        return plainText;
    }

    /**
     * 比较两个富文本内容是否相同（忽略格式差异）
     * @param content1 内容1
     * @param content2 内容2
     * @return 是否相同
     */
    public static boolean contentEquals(String content1, String content2) {
        String plain1 = extractPlainText(content1);
        String plain2 = extractPlainText(content2);
        return plain1.equals(plain2);
    }

    /**
     * 获取内容统计信息
     * @param htmlContent HTML内容
     * @return 统计信息
     */
    public static ContentStats getContentStats(String htmlContent) {
        ContentStats stats = new ContentStats();
        
        if (!StringUtils.hasText(htmlContent)) {
            return stats;
        }

        stats.setHtmlLength(htmlContent.length());
        
        String plainText = extractPlainText(htmlContent);
        stats.setPlainTextLength(plainText.length());
        
        // 统计段落数（简单统计<p>标签）
        stats.setParagraphCount(countOccurrences(htmlContent, "<p"));
        
        // 统计图片数
        stats.setImageCount(countOccurrences(htmlContent, "<img"));
        
        // 统计链接数
        stats.setLinkCount(countOccurrences(htmlContent, "<a"));
        
        return stats;
    }

    /**
     * 统计字符串中子串出现次数
     */
    private static int countOccurrences(String text, String substring) {
        int count = 0;
        int index = 0;
        while ((index = text.toLowerCase().indexOf(substring.toLowerCase(), index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }

    /**
     * 内容统计信息
     */
    public static class ContentStats {
        private int htmlLength = 0;
        private int plainTextLength = 0;
        private int paragraphCount = 0;
        private int imageCount = 0;
        private int linkCount = 0;

        // Getters and Setters
        public int getHtmlLength() { return htmlLength; }
        public void setHtmlLength(int htmlLength) { this.htmlLength = htmlLength; }

        public int getPlainTextLength() { return plainTextLength; }
        public void setPlainTextLength(int plainTextLength) { this.plainTextLength = plainTextLength; }

        public int getParagraphCount() { return paragraphCount; }
        public void setParagraphCount(int paragraphCount) { this.paragraphCount = paragraphCount; }

        public int getImageCount() { return imageCount; }
        public void setImageCount(int imageCount) { this.imageCount = imageCount; }

        public int getLinkCount() { return linkCount; }
        public void setLinkCount(int linkCount) { this.linkCount = linkCount; }

        @Override
        public String toString() {
            return String.format("ContentStats{htmlLength=%d, plainTextLength=%d, paragraphCount=%d, imageCount=%d, linkCount=%d}",
                    htmlLength, plainTextLength, paragraphCount, imageCount, linkCount);
        }
    }
}
