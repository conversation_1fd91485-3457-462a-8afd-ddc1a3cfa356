package com.kbao.kbcelms.riskconfig.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kbao.kbcelms.riskconfig.entity.RiskMatrixConfig;
import com.kbao.kbcelms.riskconfig.entity.RiskToolsConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 行业风险配置视图对象
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel(value = "IndustryRiskConfigVO", description = "行业风险配置视图对象")
public class IndustryRiskConfigVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "一级行业编码")
    private String industryLevel1Code;

    @ApiModelProperty(value = "一级行业名称")
    private String industryLevel1Name;

    @ApiModelProperty(value = "二级行业编码")
    private String industryLevel2Code;

    @ApiModelProperty(value = "二级行业名称")
    private String industryLevel2Name;

    @ApiModelProperty(value = "风险矩阵说明描述")
    private String matrixDesc;

    @ApiModelProperty(value = "整体风险等级")
    private String riskLevel;

    @ApiModelProperty(value = "配置版本号")
    private String configVersion;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "风险矩阵配置")
    private RiskMatrixConfig matrixConfig;

//    @ApiModelProperty(value = "防控工具配置")
//    private RiskToolsConfig toolsConfig;

    @ApiModelProperty(value = "富文本详细说明内容")
    private String richTextContent;

    @ApiModelProperty(value = "风险矩阵统计信息")
    private MatrixStatsVO matrixStats;

//    @ApiModelProperty(value = "防控工具统计信息")
//    private ToolsStatsVO toolsStats;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;

    /**
     * 风险矩阵统计信息
     */
    @Data
    @ApiModel(value = "MatrixStatsVO", description = "风险矩阵统计信息")
    public static class MatrixStatsVO implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "总行数")
        private Integer totalRows;

        @ApiModelProperty(value = "总列数")
        private Integer totalColumns;

        @ApiModelProperty(value = "高风险项数量")
        private Integer highRiskCount;

        @ApiModelProperty(value = "中风险项数量")
        private Integer mediumRiskCount;

        @ApiModelProperty(value = "低风险项数量")
        private Integer lowRiskCount;

        @ApiModelProperty(value = "极高风险项数量")
        private Integer criticalRiskCount;
    }

    /**
     * 防控工具统计信息
     */
//    @Data
//    @ApiModel(value = "ToolsStatsVO", description = "防控工具统计信息")
//    public static class ToolsStatsVO implements Serializable {
//
//        private static final long serialVersionUID = 1L;
//
//        @ApiModelProperty(value = "总行数")
//        private Integer totalRows;
//
//        @ApiModelProperty(value = "总列数")
//        private Integer totalColumns;
//
//        @ApiModelProperty(value = "高效果工具数量")
//        private Integer highEffectCount;
//
//        @ApiModelProperty(value = "中效果工具数量")
//        private Integer mediumEffectCount;
//
//        @ApiModelProperty(value = "低效果工具数量")
//        private Integer lowEffectCount;
//    }
}
