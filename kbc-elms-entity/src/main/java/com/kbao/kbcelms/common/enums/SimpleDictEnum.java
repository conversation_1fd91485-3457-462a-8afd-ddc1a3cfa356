package com.kbao.kbcelms.common.enums;

/**
 * 普通字典枚举
 * <AUTHOR>
 */
public enum SimpleDictEnum {
    
    EMPLOYEE_SCALES("employeeScales", "员工规模"),
    REVENUE_SCALES("revenueScales", "营收规模");
    
    private final String code;
    private final String desc;
    
    SimpleDictEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static SimpleDictEnum getByCode(String code) {
        for (SimpleDictEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    public static boolean isSupported(String code) {
        return getByCode(code) != null;
    }
}
