package com.kbao.kbcelms.genAgentEnterprise.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AgentEnterpriseEditVo {
    /**
         * 对应表中id
         * 主键
         */
        @ApiModelProperty(value = "主键ID", example = "1")
        private Integer id;

        /**
         * 对应表中name
         * 企业名称
         */
        @ApiModelProperty(value = "企业名称", example = "某保险经纪有限公司")
        private String name;

        /**
         * 对应表中credit_code
         * 社会信用代码
         */
        @ApiModelProperty(value = "社会信用代码", example = "91110000123456789X")
        private String creditCode;

        /**
         * 对应表中enterprise_scale
         * 企业规模
         */
        @ApiModelProperty(value = "企业规模", example = "大型")
        private String enterpriseScale;

        /**
         * 对应表中staff_scale
         * 人员规模
         */
        @ApiModelProperty(value = "人员规模", example = "存儲編碼")
        private String staffScale;

        /**
         * 对应表中city
         * 所在城市
         */
        @ApiModelProperty(value = "所在城市", example = "北京市")
        private String city;

        /**
         * 对应表中district_code
         * 行政区划代码
         */
        @ApiModelProperty(value = "行政区划代码", example = "110000")
        private String districtCode;

        /**
         * 对应表中annual_income
         * 年收入规模
         */
        @ApiModelProperty(value = "年收入规模", example = "存儲編碼")
        private String annualIncome;

        /**
         * 对应表中category_code
         * 行业分类代码
         */
        @ApiModelProperty(value = "行业分类代码", example = "J66")
        private String categoryCode;

        /**
         * 对应表中category_name
         * 行业分类名称
         */
        @ApiModelProperty(value = "行业分类名称", example = "保险业")
        private String categoryName;

        /**
         * 对应表中enterprise_contacter
         * 企业联系人
         */
        @ApiModelProperty(value = "企业联系人", example = "张经理")
        private String enterpriseContacter;

        /**
         * 对应表中contacter_phone
         * 联系人电话
         */
        @ApiModelProperty(value = "联系人电话", example = "13800138000")
        private String contacterPhone;

        /**
         * 对应表中remark
         * 备注信息
         */
        @ApiModelProperty(value = "备注信息", example = "重点客户，需要重点关注")
        private String remark;

        private String queryRecordId;
}
