package com.kbao.kbcelms.enterpriseconfirmation.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Description 企业确权表实体
 * @Date 2025-08-20
 */
@Data
@ApiModel(description = "企业确权实体")
public class EnterpriseConfirmation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中id
     * 主键
     */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    /**
     * 对应表中legal_code
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码", example = "LEGAL001")
    private String legalCode;

    /**
     * 对应表中legal_name
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称", example = "某某机构")
    private String legalName;

    /**
     * 对应表中credit_code
     * 统一信用代码
     */
    @ApiModelProperty(value = "统一信用代码", example = "91110000123456789X")
    private String creditCode;

    /**
     * 对应表中enterprise_name
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", example = "某保险经纪有限公司")
    private String enterpriseName;

    /**
     * 对应表中district_code
     * 行政区域编码
     */
    @ApiModelProperty(value = "行政区域编码", example = "110000")
    private String districtCode;

    /**
     * 对应表中city
     * 所在地
     */
    @ApiModelProperty(value = "所在地", example = "北京市")
    private String city;

    /**
     * 对应表中staff_scale
     * 人员规模
     */
    @ApiModelProperty(value = "人员规模", example = "2599-3000人")
    private String staffScale;

    /**
     * 对应表中annual_income
     * 营业收入
     */
    @ApiModelProperty(value = "营业收入", example = "450-500万元")
    private String annualIncome;

    /**
     * 对应表中is_duplicate
     * 是否重复：0-否，1-是
     */
    @ApiModelProperty(value = "是否重复", example = "0", notes = "0-否，1-是")
    private Integer isDuplicate;

    /**
     * 对应表中is_processed
     * 是否处理：0-未处理，1-已处理
     */
    @ApiModelProperty(value = "是否处理", example = "0", notes = "0-未处理，1-已处理")
    private Integer isProcessed;

    /**
     * 对应表中create_time
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2025-08-20 16:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 对应表中tenant_id
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "TENANT001")
    private String tenantId;
}
