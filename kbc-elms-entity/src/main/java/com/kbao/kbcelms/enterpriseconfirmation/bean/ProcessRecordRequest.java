package com.kbao.kbcelms.enterpriseconfirmation.bean;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Description 处理记录请求VO
 * @Date 2025-08-22
 */
@Data
@ApiModel(description = "处理记录请求VO")
public class ProcessRecordRequest {

    /**
     * 企业确权ID
     */
    @ApiModelProperty(value = "企业确权ID", example = "1", required = true)
    private Integer confirmationId;

    /**
     * 处理备注
     */
    @ApiModelProperty(value = "处理备注", example = "已核实，确认为重复企业，已进行合并处理", required = true)
    private String processRemark;
}
