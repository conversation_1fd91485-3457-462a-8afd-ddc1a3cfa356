package com.kbao.kbcelms.enterpriseconfirmation.bean;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;import java.util.List;

/**
 * <AUTHOR>
 * @Description 企业确权搜索VO
 * @Date 2025-08-20
 */
@Data
@ApiModel(description = "企业确权搜索VO")
public class EnterpriseConfirmationSearchVo {

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", example = "某保险经纪有限公司")
    private String enterpriseName;

    /**
     * 处理状态：0-未处理，1-已处理
     */
    @ApiModelProperty(value = "处理状态", example = "0", notes = "0-未处理，1-已处理")
    private Integer processStatus;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码", example = "LEGAL001")
    private List<String> orgList;
}
