package com.kbao.kbcelms.enterpriseconfirmation.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Description 确权处理记录表实体
 * @Date 2025-08-20
 */
@Data
@ApiModel(description = "确权处理记录实体")
public class ConfirmationProcessRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中id
     * 主键
     */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    /**
     * 对应表中credit_code
     * 统一信用代码
     */
    @ApiModelProperty(value = "统一信用代码", example = "91110000123456789X")
    private String creditCode;

    /**
     * 对应表中user_id
     * 处理人ID
     */
    @ApiModelProperty(value = "处理人ID", example = "USER001")
    private String userId;

    /**
     * 对应表中user_name
     * 处理人姓名
     */
    @ApiModelProperty(value = "处理人姓名", example = "张三")
    private String userName;

    /**
     * 对应表中remark
     * 批注
     */
    @ApiModelProperty(value = "批注", example = "已确认企业信息无误")
    private String remark;

    /**
     * 对应表中process_time
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间", example = "2025-08-20 16:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    /**
     * 对应表中legal_code
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码", example = "LEGAL001")
    private String legalCode;

    /**
     * 对应表中legal_name
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称", example = "北京分公司")
    private String legalName;

    /**
     * 对应表中tenant_id
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "TENANT001")
    private String tenantId;
}
