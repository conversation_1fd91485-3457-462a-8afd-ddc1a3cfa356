package com.kbao.kbcelms.enterpriseconfirmation.bean;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 重复企业创建人信息VO
 * @Date 2025-08-20
 */
@Data
@ApiModel(description = "重复企业创建人信息VO")
public class DuplicateEnterpriseCreatorVo {

    /**
     * 企业创建人
     */
    @ApiModelProperty(value = "企业创建人", example = "张三")
    private String creatorName;

    /**
     * 创建人所在机构
     */
    @ApiModelProperty(value = "创建人所在机构", example = "北京分公司")
    private String legalName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2025-08-20 16:30:00")
    private Date createTime;

    /**
     * 机会数量
     */
    @ApiModelProperty(value = "机会数量", example = "5")
    private Integer opportunityCount; // 先空着，后续补充
}
