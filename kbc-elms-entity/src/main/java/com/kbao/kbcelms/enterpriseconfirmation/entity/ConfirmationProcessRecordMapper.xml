<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.enterpriseconfirmation.dao.ConfirmationProcessRecordMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord">
        <id property="id" jdbcType="INTEGER" column="id"/>
        <result property="creditCode" jdbcType="VARCHAR" column="credit_code"/>
        <result property="userId" jdbcType="VARCHAR" column="user_id"/>
        <result property="userName" jdbcType="VARCHAR" column="user_name"/>
        <result property="remark" jdbcType="VARCHAR" column="remark"/>
        <result property="processTime" jdbcType="TIMESTAMP" column="process_time"/>
        <result property="legalCode" jdbcType="VARCHAR" column="legal_code"/>
        <result property="legalName" jdbcType="VARCHAR" column="legal_name"/>
        <result property="tenantId" jdbcType="VARCHAR" column="tenant_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, credit_code, user_id, user_name, remark, process_time, 
        legal_code, legal_name, tenant_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_confirmation_process_record
        where id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from t_confirmation_process_record
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord" useGeneratedKeys="true" keyProperty="id">
        insert into t_confirmation_process_record (
            credit_code, user_id, user_name, remark, process_time,
            legal_code, legal_name, tenant_id
        ) values (
            #{creditCode}, #{userId}, #{userName}, #{remark}, #{processTime},
            #{legalCode}, #{legalName}, #{tenantId}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.enterpriseconfirmation.entity.ConfirmationProcessRecord">
        update t_confirmation_process_record
        set credit_code = #{creditCode},
            user_id = #{userId},
            user_name = #{userName},
            remark = #{remark},
            process_time = #{processTime},
            legal_code = #{legalCode},
            legal_name = #{legalName},
            tenant_id = #{tenantId}
        where id = #{id}
    </update>

    <!-- 根据统一信用代码查询处理记录 -->
    <select id="getByCreditCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM t_confirmation_process_record
        WHERE credit_code = #{creditCode}
        ORDER BY process_time DESC
    </select>

</mapper>
