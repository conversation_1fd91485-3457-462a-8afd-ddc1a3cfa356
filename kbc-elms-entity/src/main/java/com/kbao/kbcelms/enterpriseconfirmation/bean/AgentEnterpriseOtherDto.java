package com.kbao.kbcelms.enterpriseconfirmation.bean;

import lombok.Data;import java.util.Date;@Data
public class AgentEnterpriseOtherDto {
    /**
     * 代理企业ID
     */
    private Integer agentEnterpriseId;

    /**
     * 机会数量
     */
    private Integer opportunityNum;

    /**
     * 进行中员工保险类型（逗号分隔）
     */
    private String underwayEmployeeInsureTypes;

    /**
     * 进行中通用保险类型（逗号分隔）
     */
    private String underwayGeneralInsureTypes;

    /**
     * 锁定的员工保险类型（逗号分隔）
     */
    private String lockEmployeeInsureTypes;

    /**
     * 锁定的通用保险类型（逗号分隔）
     */
    private String lockGeneralInsureTypes;


    /**
     * 已签发的员工保险类型（逗号分隔）
     */
    private String issuedEmployeeInsureTypes;

    /**
     * 已签发的通用保险类型（逗号分隔）
     */
    private String issuedGeneralInsureTypes;
}
