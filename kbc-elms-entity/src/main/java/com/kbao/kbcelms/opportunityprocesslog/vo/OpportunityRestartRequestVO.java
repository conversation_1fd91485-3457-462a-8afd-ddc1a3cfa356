package com.kbao.kbcelms.opportunityprocesslog.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 机会重启请求VO
 * <AUTHOR>
 * @date 2025/1/27 15:13
 */
@Data
@ApiModel(value = "OpportunityRestartRequestVO", description = "机会重启请求VO")
public class OpportunityRestartRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机会ID", required = true)
    @NotNull(message = "机会ID不能为空")
    private Integer opportunityId;

    @ApiModelProperty(value = "重启原因描述", required = true, example = "客户重新考虑项目需求")
    @NotNull(message = "重启原因描述不能为空")
    private String reasonDesc;

    @ApiModelProperty(value = "业务租户ID", required = true)
    @NotNull(message = "业务租户ID不能为空")
    private String businessTenantId;

    @ApiModelProperty(value = "指派用户ID", required = true)
    @NotNull(message = "指派用户ID不能为空")
    private String assignee;

    @ApiModelProperty(value = "指派用户角色类型", required = true)
    @NotNull(message = "指派用户角色类型不能为空")
    private Integer assigneeRoleType;

    @ApiModelProperty(value = "指派用户机构", required = true)
    @NotNull(message = "指派用户机构不能为空")
    private String assigneeOrg;
} 