<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="opportunityId" column="opportunity_id" jdbcType="INTEGER" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="processId" column="process_id" jdbcType="VARCHAR" />
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR" />
        <result property="targetId" column="target_id" jdbcType="VARCHAR" />
        <result property="operatorDesc" column="operator_desc" jdbcType="VARCHAR" />
        <result property="reasonType" column="reason_type" jdbcType="INTEGER" />
        <result property="reasonDesc" column="reason_desc" jdbcType="VARCHAR" />
        <result property="isNodeCompleteLog" column="is_node_complete_log" jdbcType="INTEGER" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, opportunity_id, tenant_id, process_id, operator_id, target_id, operator_desc, reason_type, reason_desc, is_node_complete_log, create_time, update_id, update_time, is_deleted
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.opportunity_id,
        t.tenant_id,
        t.process_id,
        t.operator_id,
        t.target_id,
        t.operator_desc,
        t.reason_type,
        t.reason_desc,
        t.is_node_complete_log,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="opportunityId != null">
                and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="processId != null">
                and t.process_id = #{processId,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null">
                and t.operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="targetId != null">
                and t.target_id = #{targetId,jdbcType=VARCHAR}
            </if>
            <if test="operatorDesc != null">
                and t.operator_desc = #{operatorDesc,jdbcType=VARCHAR}
            </if>
            <if test="reasonType != null">
                and t.reason_type = #{reasonType,jdbcType=INTEGER}
            </if>
            <if test="reasonDesc != null">
                and t.reason_desc = #{reasonDesc,jdbcType=VARCHAR}
            </if>
            <if test="isNodeCompleteLog != null">
                and t.is_node_complete_log = #{isNodeCompleteLog,jdbcType=INTEGER}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_opportunity_process_log where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_opportunity_process_log t
        <include refid="Base_Condition"/>
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_opportunity_process_log t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_process_log (
            opportunity_id, tenant_id, process_id, operator_id, target_id, operator_desc, reason_type, reason_desc, is_node_complete_log, create_time, update_id, update_time, is_deleted
        ) values (
            #{opportunityId}, #{tenantId}, #{processId}, #{operatorId}, #{targetId}, #{operatorDesc}, #{reasonType}, #{reasonDesc}, #{isNodeCompleteLog}, #{createTime}, #{updateId}, #{updateTime}, 0
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_process_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="processId != null">process_id,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="targetId != null">target_id,</if>
            <if test="operatorDesc != null">operator_desc,</if>
            <if test="reasonType != null">reason_type,</if>
            <if test="reasonDesc != null">reason_desc,</if>
            <if test="isNodeCompleteLog != null">is_node_complete_log,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="processId != null">#{processId},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="operatorDesc != null">#{operatorDesc},</if>
            <if test="reasonType != null">#{reasonType},</if>
            <if test="reasonDesc != null">#{reasonDesc},</if>
            <if test="isNodeCompleteLog != null">#{isNodeCompleteLog},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog">
        update t_opportunity_process_log set
            opportunity_id = #{opportunityId},
            tenant_id = #{tenantId},
            process_id = #{processId},
            operator_id = #{operatorId},
            target_id = #{targetId},
            operator_desc = #{operatorDesc},
            reason_type = #{reasonType},
            reason_desc = #{reasonDesc},
            is_node_complete_log = #{isNodeCompleteLog},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog">
        update t_opportunity_process_log
        <set>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="operatorDesc != null">operator_desc = #{operatorDesc},</if>
            <if test="reasonType != null">reason_type = #{reasonType},</if>
            <if test="reasonDesc != null">reason_desc = #{reasonDesc},</if>
            <if test="isNodeCompleteLog != null">is_node_complete_log = #{isNodeCompleteLog},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_opportunity_process_log set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_opportunity_process_log (
            opportunity_id, tenant_id, process_id, operator_id, target_id, operator_desc, reason_type, reason_desc, is_node_complete_log, create_time, update_id, update_time, is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.opportunityId}, #{item.tenantId}, #{item.processId}, #{item.operatorId}, #{item.targetId}, #{item.operatorDesc}, #{item.reasonType}, #{item.reasonDesc}, #{item.isNodeCompleteLog}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_opportunity_process_log set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询用户参与过的机会数量 -->
    <select id="countUserParticipatedOpportunities" resultType="java.lang.Integer">
        SELECT count(DISTINCT t.opportunity_id) 
        from t_opportunity_process_log t 
        WHERE (t.operator_id = #{userId,jdbcType=VARCHAR} OR t.target_id = #{userId,jdbcType=VARCHAR}) 
        AND t.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND t.is_deleted = 0
    </select>
    
    <!-- 根据机会ID查询关闭原因 -->
    <select id="getCloseReasonByOpportunityId" resultType="com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityCloseReasonQueryResponseVO">
        SELECT 
            t.opportunity_id as opportunityId,
            t.reason_type as reasonType,
            t.reason_desc as reasonDesc,
            t.operator_desc as operatorDesc,
            t.create_time as createTime,
            t.operator_id as operatorId,
            u.nick_name as operatorName,
            u.bsc_use_name as operatorUserName,
<!--            r.role_name as operatorRoleName,-->
            ut.organ_name_path as operatorOrgPath
        FROM t_opportunity_process_log t 
        LEFT JOIN t_user u ON t.operator_id = u.user_id AND u.is_deleted = 0
<!--        LEFT JOIN t_user_role ur ON t.operator_id = ur.user_id AND ur.is_deleted = 0-->
<!--        LEFT JOIN t_role r ON ur.role_id = r.id AND r.is_deleted = 0 AND r.tenant_id = #{tenantId,jdbcType=VARCHAR}-->
        LEFT JOIN t_user_tenant ut ON t.operator_id = ut.user_id AND ut.tenant_id = #{tenantId,jdbcType=VARCHAR} AND ut.is_deleted = 0
        WHERE t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
        AND t.tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND t.is_deleted = 0
        AND t.reason_type IS NOT NULL
        ORDER BY t.create_time DESC
        LIMIT 1
    </select>

</mapper> 