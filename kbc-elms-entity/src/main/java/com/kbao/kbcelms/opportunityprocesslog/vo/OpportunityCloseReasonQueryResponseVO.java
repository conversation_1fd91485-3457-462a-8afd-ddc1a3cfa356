package com.kbao.kbcelms.opportunityprocesslog.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 查询机会关闭原因响应VO
 * <AUTHOR>
 * @date 2025/1/27 15:13
 */
@Data
@ApiModel(value = "OpportunityCloseReasonQueryResponseVO", description = "查询机会关闭原因响应VO")
public class OpportunityCloseReasonQueryResponseVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "机会ID")
    private Integer opportunityId;
    
    @ApiModelProperty(value = "关闭原因类型")
    private Integer reasonType;
    
    @ApiModelProperty(value = "关闭原因类型名称")
    private String reasonTypeName;
    
    @ApiModelProperty(value = "关闭原因描述")
    private String reasonDesc;
    
    @ApiModelProperty(value = "操作描述")
    private String operatorDesc;
    
    @ApiModelProperty(value = "操作时间")
    private Date createTime;
    
    @ApiModelProperty(value = "操作人ID")
    private String operatorId;
    
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;
    
    @ApiModelProperty(value = "操作人用户名")
    private String operatorUserName;
    
//    @ApiModelProperty(value = "操作人角色名称")
//    private String operatorRoleName;
    
    @ApiModelProperty(value = "操作人机构路径")
    private String operatorOrgPath;
    
    @ApiModelProperty(value = "格式化后的操作描述")
    private String formattedOperatorDesc;
} 