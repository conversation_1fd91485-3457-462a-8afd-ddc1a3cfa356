package com.kbao.kbcelms.opportunityprocesslog.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 机会关闭请求VO
 * <AUTHOR>
 * @date 2025/1/27 15:13
 */
@Data
@ApiModel(value = "OpportunityCloseRequestVO", description = "机会关闭请求VO")
public class OpportunityCloseRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机会ID", required = true)
    @NotNull(message = "机会ID不能为空")
    private Integer opportunityId;

    @ApiModelProperty(value = "关闭原因类型", required = true, example = "1")
    @NotNull(message = "关闭原因类型不能为空")
    private Integer reasonType;

    @ApiModelProperty(value = "关闭原因描述", required = true, example = "客户因预算问题取消项目")
    @NotNull(message = "关闭原因描述不能为空")
    private String reasonDesc;
} 