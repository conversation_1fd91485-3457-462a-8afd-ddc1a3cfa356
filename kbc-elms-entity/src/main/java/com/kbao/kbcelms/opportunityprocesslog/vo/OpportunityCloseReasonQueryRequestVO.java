package com.kbao.kbcelms.opportunityprocesslog.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 查询机会关闭原因请求VO
 * <AUTHOR>
 * @date 2025/1/27 15:13
 */
@Data
@ApiModel(value = "OpportunityCloseReasonQueryRequestVO", description = "查询机会关闭原因请求VO")
public class OpportunityCloseReasonQueryRequestVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "机会ID", required = true, example = "123")
    @NotNull(message = "机会ID不能为空")
    private Integer opportunityId;
} 