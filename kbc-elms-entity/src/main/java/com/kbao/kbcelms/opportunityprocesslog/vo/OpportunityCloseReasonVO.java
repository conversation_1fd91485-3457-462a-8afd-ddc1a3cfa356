package com.kbao.kbcelms.opportunityprocesslog.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 机会关闭原因响应VO
 * <AUTHOR>
 * @date 2025/1/27 15:13
 */
@Data
@ApiModel(value = "OpportunityCloseReasonVO", description = "机会关闭原因响应VO")
public class OpportunityCloseReasonVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "原因类型编码")
    private Integer code;

    @ApiModelProperty(value = "原因类型名称")
    private String name;
} 