package com.kbao.kbcelms.riskmatrix.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 评分项评分标准实体类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScoreItemCriteria {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 评分项ID
     */
    private Long scoreItemId;
    
    /**
     * 分值
     */
    private Integer score;
    
    /**
     * 评分标准描述
     */
    private String description;
    
    /**
     * 排序序号
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
