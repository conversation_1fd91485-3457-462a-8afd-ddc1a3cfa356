<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.riskmatrix.dao.RiskMatrixLevelMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixLevel">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="min_value" property="minValue" jdbcType="DECIMAL"/>
        <result column="max_value" property="maxValue" jdbcType="DECIMAL"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="color" property="color" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, category_id, name, min_value, max_value, description, color, sort_order,
        create_time, update_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_level
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据类别ID查询档次列表 -->
    <select id="selectByCategoryId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_level
        WHERE category_id = #{categoryId,jdbcType=BIGINT}
        ORDER BY sort_order ASC, min_value ASC
    </select>

    <!-- 根据类别ID和分值范围查询档次 -->
    <select id="selectByCategoryIdAndScore" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_level
        WHERE category_id = #{categoryId,jdbcType=BIGINT}
        AND #{score,jdbcType=DECIMAL} >= min_value
        AND #{score,jdbcType=DECIMAL} &lt;= max_value
        ORDER BY sort_order ASC
        LIMIT 1
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixLevel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix_level (
            category_id, name, min_value, max_value, description, color, sort_order,
            create_time, update_time
        ) VALUES (
            #{categoryId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{minValue,jdbcType=DECIMAL},
            #{maxValue,jdbcType=DECIMAL}, #{description,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR},
            #{sortOrder,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixLevel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="name != null">name,</if>
            <if test="minValue != null">min_value,</if>
            <if test="maxValue != null">max_value,</if>
            <if test="description != null">description,</if>
            <if test="color != null">color,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="minValue != null">#{minValue,jdbcType=DECIMAL},</if>
            <if test="maxValue != null">#{maxValue,jdbcType=DECIMAL},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="color != null">#{color,jdbcType=VARCHAR},</if>
            <if test="sortOrder != null">#{sortOrder,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 批量插入档次 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_risk_matrix_level (
            category_id, name, min_value, max_value, description, color, sort_order,
            create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.categoryId,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR},
                #{item.minValue,jdbcType=DECIMAL}, #{item.maxValue,jdbcType=DECIMAL},
                #{item.description,jdbcType=VARCHAR}, #{item.color,jdbcType=VARCHAR},
                #{item.sortOrder,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixLevel">
        UPDATE t_risk_matrix_level
        SET category_id = #{categoryId,jdbcType=BIGINT},
            name = #{name,jdbcType=VARCHAR},
            min_value = #{minValue,jdbcType=DECIMAL},
            max_value = #{maxValue,jdbcType=DECIMAL},
            description = #{description,jdbcType=VARCHAR},
            color = #{color,jdbcType=VARCHAR},
            sort_order = #{sortOrder,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixLevel">
        UPDATE t_risk_matrix_level
        <set>
            <if test="categoryId != null">category_id = #{categoryId,jdbcType=BIGINT},</if>
            <if test="name != null">name = #{name,jdbcType=VARCHAR},</if>
            <if test="minValue != null">min_value = #{minValue,jdbcType=DECIMAL},</if>
            <if test="maxValue != null">max_value = #{maxValue,jdbcType=DECIMAL},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="color != null">color = #{color,jdbcType=VARCHAR},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder,jdbcType=INTEGER},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_risk_matrix_level
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据类别ID删除档次 -->
    <delete id="deleteByCategoryId" parameterType="java.lang.Long">
        DELETE FROM t_risk_matrix_level
        WHERE category_id = #{categoryId,jdbcType=BIGINT}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM t_risk_matrix_level
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
