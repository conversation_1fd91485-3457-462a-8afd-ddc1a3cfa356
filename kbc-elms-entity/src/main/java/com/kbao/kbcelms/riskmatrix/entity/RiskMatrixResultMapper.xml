<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.riskmatrix.dao.RiskMatrixResultMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixResult">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="matrix_id" property="matrixId" jdbcType="BIGINT"/>
        <result column="enterprise_id" property="enterpriseId" jdbcType="BIGINT"/>
        <result column="enterprise_type" property="enterpriseType" jdbcType="VARCHAR"/>
        <result column="total_score" property="totalScore" jdbcType="DECIMAL"/>
        <result column="average_score" property="averageScore" jdbcType="DECIMAL"/>
        <result column="overall_risk_level" property="overallRiskLevel" jdbcType="VARCHAR"/>
        <result column="calculation_data" property="calculationData" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, matrix_id, enterprise_id, enterprise_type, total_score, average_score,
        overall_risk_level, calculation_data, create_time, update_time, create_user
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_result
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据矩阵ID查询结果列表 -->
    <select id="selectByMatrixId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_result
        WHERE matrix_id = #{matrixId,jdbcType=BIGINT}
        ORDER BY create_time DESC
    </select>

    <!-- 根据企业ID查询结果列表 -->
    <select id="selectByEnterpriseId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_result
        WHERE enterprise_id = #{enterpriseId,jdbcType=BIGINT}
        ORDER BY create_time DESC
    </select>

    <!-- 根据矩阵ID和企业ID查询结果 -->
    <select id="selectByMatrixIdAndEnterpriseId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_result
        WHERE matrix_id = #{matrixId,jdbcType=BIGINT}
        AND enterprise_id = #{enterpriseId,jdbcType=BIGINT}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据企业类型查询结果列表 -->
    <select id="selectByEnterpriseType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_result
        WHERE enterprise_type = #{enterpriseType,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>

    <!-- 根据风险等级查询结果列表 -->
    <select id="selectByRiskLevel" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_risk_matrix_result
        WHERE overall_risk_level = #{riskLevel,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixResult" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix_result (
            matrix_id, enterprise_id, enterprise_type, total_score, average_score,
            overall_risk_level, calculation_data, create_time, update_time, create_user
        ) VALUES (
            #{matrixId,jdbcType=BIGINT}, #{enterpriseId,jdbcType=BIGINT}, #{enterpriseType,jdbcType=VARCHAR},
            #{totalScore,jdbcType=DECIMAL}, #{averageScore,jdbcType=DECIMAL}, #{overallRiskLevel,jdbcType=VARCHAR},
            #{calculationData,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
            #{createUser,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixResult" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="matrixId != null">matrix_id,</if>
            <if test="enterpriseId != null">enterprise_id,</if>
            <if test="enterpriseType != null">enterprise_type,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="averageScore != null">average_score,</if>
            <if test="overallRiskLevel != null">overall_risk_level,</if>
            <if test="calculationData != null">calculation_data,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUser != null">create_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="matrixId != null">#{matrixId,jdbcType=BIGINT},</if>
            <if test="enterpriseId != null">#{enterpriseId,jdbcType=BIGINT},</if>
            <if test="enterpriseType != null">#{enterpriseType,jdbcType=VARCHAR},</if>
            <if test="totalScore != null">#{totalScore,jdbcType=DECIMAL},</if>
            <if test="averageScore != null">#{averageScore,jdbcType=DECIMAL},</if>
            <if test="overallRiskLevel != null">#{overallRiskLevel,jdbcType=VARCHAR},</if>
            <if test="calculationData != null">#{calculationData,jdbcType=LONGVARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createUser != null">#{createUser,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixResult">
        UPDATE t_risk_matrix_result
        SET matrix_id = #{matrixId,jdbcType=BIGINT},
            enterprise_id = #{enterpriseId,jdbcType=BIGINT},
            enterprise_type = #{enterpriseType,jdbcType=VARCHAR},
            total_score = #{totalScore,jdbcType=DECIMAL},
            average_score = #{averageScore,jdbcType=DECIMAL},
            overall_risk_level = #{overallRiskLevel,jdbcType=VARCHAR},
            calculation_data = #{calculationData,jdbcType=LONGVARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixResult">
        UPDATE t_risk_matrix_result
        <set>
            <if test="matrixId != null">matrix_id = #{matrixId,jdbcType=BIGINT},</if>
            <if test="enterpriseId != null">enterprise_id = #{enterpriseId,jdbcType=BIGINT},</if>
            <if test="enterpriseType != null">enterprise_type = #{enterpriseType,jdbcType=VARCHAR},</if>
            <if test="totalScore != null">total_score = #{totalScore,jdbcType=DECIMAL},</if>
            <if test="averageScore != null">average_score = #{averageScore,jdbcType=DECIMAL},</if>
            <if test="overallRiskLevel != null">overall_risk_level = #{overallRiskLevel,jdbcType=VARCHAR},</if>
            <if test="calculationData != null">calculation_data = #{calculationData,jdbcType=LONGVARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_risk_matrix_result
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据矩阵ID删除结果 -->
    <delete id="deleteByMatrixId" parameterType="java.lang.Long">
        DELETE FROM t_risk_matrix_result
        WHERE matrix_id = #{matrixId,jdbcType=BIGINT}
    </delete>

    <!-- 根据企业ID删除结果 -->
    <delete id="deleteByEnterpriseId" parameterType="java.lang.Long">
        DELETE FROM t_risk_matrix_result
        WHERE enterprise_id = #{enterpriseId,jdbcType=BIGINT}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM t_risk_matrix_result
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
