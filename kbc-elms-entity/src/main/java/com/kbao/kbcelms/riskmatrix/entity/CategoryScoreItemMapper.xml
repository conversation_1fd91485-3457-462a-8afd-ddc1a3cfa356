<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.riskmatrix.dao.CategoryScoreItemMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.riskmatrix.entity.CategoryScoreItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="score_item_id" property="scoreItemId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, category_id, score_item_id,  create_time
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_category_score_item
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 插入记录 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.CategoryScoreItem"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_category_score_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">
                category_id,
            </if>
            <if test="scoreItemId != null">
                score_item_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">
                #{categoryId,jdbcType=BIGINT},
            </if>
            <if test="scoreItemId != null">
                #{scoreItemId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_category_score_item
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据类别ID删除关联关系 -->
    <delete id="deleteByCategoryId" parameterType="java.lang.Long">
        DELETE FROM t_category_score_item
        WHERE category_id = #{categoryId,jdbcType=BIGINT}
    </delete>

    <!-- 根据类别ID列表删除关联关系 -->
    <delete id="deleteByCategoryIds" parameterType="java.util.List">
        DELETE FROM t_category_score_item
        WHERE category_id IN
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <!-- 根据评分项ID删除关联关系 -->
    <delete id="deleteByScoreItemId" parameterType="java.lang.Long">
        DELETE FROM t_category_score_item
        WHERE score_item_id = #{scoreItemId,jdbcType=BIGINT}
    </delete>

    <!-- 根据类别ID查询关联的评分项ID列表 -->
    <select id="selectScoreItemIdsByCategoryId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT score_item_id
        FROM t_category_score_item
        WHERE category_id = #{categoryId,jdbcType=BIGINT}
    </select>

    <!-- 根据类别ID列表查询关联关系 -->
    <select id="selectByCategoryIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_category_score_item
        WHERE category_id IN
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        ORDER BY category_id, id
    </select>

    <!-- 批量插入关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_category_score_item (category_id, score_item_id, create_time)
        VALUES
        <foreach collection="relations" item="item" separator=",">
            (#{item.categoryId,jdbcType=BIGINT}, 
             #{item.scoreItemId,jdbcType=BIGINT},
             #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

</mapper>
