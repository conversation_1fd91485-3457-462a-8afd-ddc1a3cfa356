package com.kbao.kbcelms.riskmatrix.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 风险矩阵档次配置实体类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixLevel {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 类别ID
     */
    private Long categoryId;
    
    /**
     * 档次名称
     */
    private String name;
    
    /**
     * 最小值
     */
    private BigDecimal minValue;
    
    /**
     * 最大值
     */
    private BigDecimal maxValue;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 颜色代码
     */
    private String color;
    
    /**
     * 排序序号
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
