package com.kbao.kbcelms.riskmatrix.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 评分项查询参数
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScoreItemQuery {
    
    /**
     * 评分项名称（模糊查询）
     */
    private String name;
    
    /**
     * 所属类别
     */
    private String category;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 企业类型
     */
    private String enterpriseType;
    
    /**
     * 公式ID
     */
    private Long formulaId;
    
    /**
     * 创建人
     */
    private String createUser;
}
