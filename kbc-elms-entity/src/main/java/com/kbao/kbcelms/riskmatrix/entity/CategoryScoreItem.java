package com.kbao.kbcelms.riskmatrix.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 类别与评分项关联表实体类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryScoreItem {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 类别ID
     */
    private Long categoryId;
    
    /**
     * 评分项ID
     */
    private Long scoreItemId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
