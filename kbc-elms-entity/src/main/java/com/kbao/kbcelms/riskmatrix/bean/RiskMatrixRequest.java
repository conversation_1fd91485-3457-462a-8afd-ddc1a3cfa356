package com.kbao.kbcelms.riskmatrix.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 风险矩阵保存请求参数
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixRequest {
    
    /**
     * 主键ID（更新时必填）
     */
    private Long id;
    
    /**
     * 矩阵名称
     */
    @NotBlank(message = "矩阵名称不能为空")
    @Size(max = 100, message = "矩阵名称长度不能超过100个字符")
    private String name;
    
    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    /**
     * 矩阵分类
     */
    private String category;
    
    /**
     * 关联企业类型
     */
    @NotEmpty(message = "关联企业类型不能为空")
    private List<String> enterpriseTypes;
    
    /**
     * 类别列表
     */
    private List<CategoryRequest> categories;
    
    /**
     * 类别请求参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryRequest {
        
        /**
         * 类别ID（更新时必填）
         */
        private Long id;
        
        /**
         * 类别名称
         */
        @NotBlank(message = "类别名称不能为空")
        private String name;

        /**
         * 类别描述
         */
        @Size(max = 500, message = "类别描述长度不能超过500个字符")
        private String description;

        /**
         * 权重
         */
        private Double weight;
        
        /**
         * 计算方法
         */
        private String calculationMethod;
        
        /**
         * 关联评分项ID列表
         */
        private List<Long> scoreItems;
        
        /**
         * 档次配置列表
         */
        private List<LevelRequest> levels;
    }
    
    /**
     * 档次请求参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LevelRequest {
        
        /**
         * 档次ID（更新时必填）
         */
        private Long id;
        
        /**
         * 档次名称
         */
        @NotBlank(message = "档次名称不能为空")
        private String name;
        
        /**
         * 最小值
         */
        private Double minValue;
        
        /**
         * 最大值
         */
        private Double maxValue;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 颜色代码
         */
        private String color;
    }
}
