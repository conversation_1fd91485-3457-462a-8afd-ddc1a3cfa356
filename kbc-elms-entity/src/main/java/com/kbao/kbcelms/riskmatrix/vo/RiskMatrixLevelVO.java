package com.kbao.kbcelms.riskmatrix.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 风险矩阵档次视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixLevelVO {
    
    /**
     * 档次ID
     */
    private Long id;
    
    /**
     * 类别ID
     */
    private Long categoryId;
    
    /**
     * 档次名称
     */
    private String name;
    
    /**
     * 最小值
     */
    private Double minValue;
    
    /**
     * 最大值
     */
    private Double maxValue;
    
    /**
     * 区间范围显示
     */
    private String rangeDisplay;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 颜色代码
     */
    private String color;
    
    /**
     * 排序序号
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
