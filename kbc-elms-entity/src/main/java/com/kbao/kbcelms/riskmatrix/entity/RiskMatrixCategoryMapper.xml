<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.riskmatrix.dao.RiskMatrixCategoryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="matrix_id" property="matrixId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="DECIMAL"/>
        <result column="calculation_method" property="calculationMethod" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="level_count" property="levelCount" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="BaseScoreResultMap" type="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="matrix_id" property="matrixId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="DECIMAL"/>
        <result column="score_ids" property="scoreIds" jdbcType="VARCHAR"/>
        <result column="calculation_method" property="calculationMethod" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="level_count" property="levelCount" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, matrix_id, name, description, weight, calculation_method, sort_order, create_time, update_time
    </sql>

    <!-- 带档次数量统计的字段列表 -->
    <sql id="Base_Column_List_With_Level_Count">
        rmc.id, rmc.matrix_id, rmc.name, rmc.description, rmc.weight, rmc.calculation_method, rmc.sort_order, rmc.create_time, rmc.update_time,
        COALESCE(lc.level_count, 0) as level_count
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_With_Level_Count"/>, si.score_ids
        FROM t_risk_matrix_category rmc
        LEFT JOIN (
            SELECT category_id, GROUP_CONCAT(score_item_id SEPARATOR ',') as score_ids
            FROM t_category_score_item
            GROUP BY category_id
        ) si ON rmc.id = si.category_id
        LEFT JOIN (
            SELECT category_id, COUNT(*) as level_count
            FROM t_risk_matrix_level
            GROUP BY category_id
        ) lc ON rmc.id = lc.category_id
        WHERE rmc.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据矩阵ID查询类别列表 -->
    <select id="selectByMatrixId" parameterType="java.lang.Long" resultMap="BaseScoreResultMap">
        SELECT
        <include refid="Base_Column_List_With_Level_Count"/>, si.score_ids
        FROM t_risk_matrix_category rmc
        LEFT JOIN (
            SELECT category_id, GROUP_CONCAT(score_item_id SEPARATOR ',') as score_ids
            FROM t_category_score_item
            GROUP BY category_id
            ) si ON rmc.id = si.category_id
        LEFT JOIN (
            SELECT category_id, COUNT(*) as level_count
            FROM t_risk_matrix_level
            GROUP BY category_id
        ) lc ON rmc.id = lc.category_id
        WHERE rmc.matrix_id = #{matrixId,jdbcType=BIGINT}
        ORDER BY rmc.sort_order, rmc.id
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix_category (
            matrix_id, name, description, weight, calculation_method, sort_order, create_time, update_time
        ) VALUES (
            #{matrixId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{weight,jdbcType=DECIMAL},
            #{calculationMethod,jdbcType=VARCHAR}, #{sortOrder,jdbcType=INTEGER},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="matrixId != null">matrix_id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="weight != null">weight,</if>
            <if test="calculationMethod != null">calculation_method,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="matrixId != null">#{matrixId,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="weight != null">#{weight,jdbcType=DECIMAL},</if>
            <if test="calculationMethod != null">#{calculationMethod,jdbcType=VARCHAR},</if>
            <if test="sortOrder != null">#{sortOrder,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_risk_matrix_category (
            matrix_id, name, description, weight, calculation_method, sort_order, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.matrixId,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
                #{item.weight,jdbcType=DECIMAL}, #{item.calculationMethod,jdbcType=VARCHAR}, #{item.sortOrder,jdbcType=INTEGER},
                #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory">
        UPDATE t_risk_matrix_category
        SET matrix_id = #{matrixId,jdbcType=BIGINT},
            name = #{name,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            weight = #{weight,jdbcType=DECIMAL},
            calculation_method = #{calculationMethod,jdbcType=VARCHAR},
            sort_order = #{sortOrder,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory">
        UPDATE t_risk_matrix_category
        <set>
            <if test="matrixId != null">matrix_id = #{matrixId,jdbcType=BIGINT},</if>
            <if test="name != null">name = #{name,jdbcType=VARCHAR},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="weight != null">weight = #{weight,jdbcType=DECIMAL},</if>
            <if test="calculationMethod != null">calculation_method = #{calculationMethod,jdbcType=VARCHAR},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder,jdbcType=INTEGER},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_risk_matrix_category
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据矩阵ID删除类别 -->
    <delete id="deleteByMatrixId" parameterType="java.lang.Long">
        DELETE FROM t_risk_matrix_category
        WHERE matrix_id = #{matrixId,jdbcType=BIGINT}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM t_risk_matrix_category
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
