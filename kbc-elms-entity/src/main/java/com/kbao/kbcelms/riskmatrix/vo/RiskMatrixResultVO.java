package com.kbao.kbcelms.riskmatrix.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 风险矩阵计算结果视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixResultVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 风险矩阵ID
     */
    private Long matrixId;
    
    /**
     * 风险矩阵名称
     */
    private String matrixName;
    
    /**
     * 企业ID
     */
    private Long enterpriseId;
    
    /**
     * 企业名称
     */
    private String enterpriseName;
    
    /**
     * 企业类型
     */
    private String enterpriseType;
    
    /**
     * 企业类型名称
     */
    private String enterpriseTypeName;
    
    /**
     * 总分
     */
    private BigDecimal totalScore;
    
    /**
     * 平均分
     */
    private BigDecimal averageScore;
    
    /**
     * 整体风险等级
     */
    private String overallRiskLevel;
    
    /**
     * 风险等级颜色
     */
    private String riskLevelColor;
    
    /**
     * 类别结果列表
     */
    private List<CategoryResultVO> categoryResults;
    
    /**
     * 计算过程详情
     */
    private CalculationProcessVO calculationProcess;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 类别结果视图对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryResultVO {
        
        /**
         * 类别ID
         */
        private Long categoryId;
        
        /**
         * 类别名称
         */
        private String categoryName;
        
        /**
         * 类别得分
         */
        private BigDecimal score;
        
        /**
         * 权重
         */
        private BigDecimal weight;
        
        /**
         * 加权得分
         */
        private BigDecimal weightedScore;
        
        /**
         * 风险等级
         */
        private String riskLevel;
        
        /**
         * 风险等级颜色
         */
        private String riskLevelColor;
        
        /**
         * 评分项结果列表
         */
        private List<ScoreItemResultVO> scoreItemResults;
    }
    
    /**
     * 评分项结果视图对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreItemResultVO {
        
        /**
         * 评分项ID
         */
        private Long scoreItemId;
        
        /**
         * 评分项名称
         */
        private String scoreItemName;
        
        /**
         * 原始得分
         */
        private BigDecimal rawScore;
        
        /**
         * 权重
         */
        private BigDecimal weight;
        
        /**
         * 系数
         */
        private BigDecimal coefficient;
        
        /**
         * 最终得分
         */
        private BigDecimal finalScore;
    }
    
    /**
     * 计算过程视图对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CalculationProcessVO {
        
        /**
         * 问卷答案列表
         */
        private List<Object> questionnaireAnswers;
        
        /**
         * 基础参数
         */
        private Object basicParameters;
        
        /**
         * 计算步骤说明
         */
        private List<String> calculationSteps;
    }
}
