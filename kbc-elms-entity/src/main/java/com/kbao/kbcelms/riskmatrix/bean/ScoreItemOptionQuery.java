package com.kbao.kbcelms.riskmatrix.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 评分项下拉选项查询参数
 * 
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScoreItemOptionQuery {
    
    /**
     * 评分项名称（模糊查询）
     */
    private String name;
    
    /**
     * 企业类型
     */
    private List<String> enterpriseTypeList;

    private String enterpriseTypes;

    /**
     * 关键词搜索（同时搜索名称、编码、描述）
     */
    private String keyword;
}
