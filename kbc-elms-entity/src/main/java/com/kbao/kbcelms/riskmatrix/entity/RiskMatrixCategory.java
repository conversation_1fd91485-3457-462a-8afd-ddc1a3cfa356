package com.kbao.kbcelms.riskmatrix.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 风险矩阵类别实体类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixCategory {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 风险矩阵ID
     */
    private Long matrixId;
    
    /**
     * 类别名称
     */
    private String name;

    /**
     * 类别描述
     */
    private String description;

    /**
     * 权重
     */
    private BigDecimal weight;
    
    /**
     * 计算方法：sum-求和，avg-平均值，max-最大值
     */
    private String calculationMethod;

    /**
     * 排序序号
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 档次数量（查询时统计）
     */
    @Transient
    private Integer levelCount;

    /**
     * 关联评分项ID，逗号分隔
     */
    @Transient
    private String scoreIds;
}
