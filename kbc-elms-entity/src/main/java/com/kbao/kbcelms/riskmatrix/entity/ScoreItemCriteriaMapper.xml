<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.riskmatrix.dao.ScoreItemCriteriaMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.riskmatrix.entity.ScoreItemCriteria">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="score_item_id" property="scoreItemId" jdbcType="BIGINT"/>
        <result column="score" property="score" jdbcType="INTEGER"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, score_item_id, score, description, sort_order, create_time, update_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_score_item_criteria
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据评分项ID查询标准列表 -->
    <select id="selectByScoreItemId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_score_item_criteria
        WHERE score_item_id = #{scoreItemId,jdbcType=BIGINT}
        ORDER BY sort_order, score
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.riskmatrix.entity.ScoreItemCriteria" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_score_item_criteria (
            score_item_id, score, description, sort_order, create_time, update_time
        ) VALUES (
            #{scoreItemId,jdbcType=BIGINT}, #{score,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR},
            #{sortOrder,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 选择性插入 -->
    <insert id="insertSelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.ScoreItemCriteria" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_score_item_criteria
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scoreItemId != null">score_item_id,</if>
            <if test="score != null">score,</if>
            <if test="description != null">description,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scoreItemId != null">#{scoreItemId,jdbcType=BIGINT},</if>
            <if test="score != null">#{score,jdbcType=INTEGER},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="sortOrder != null">#{sortOrder,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_score_item_criteria (
            score_item_id, score, description, sort_order, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.scoreItemId,jdbcType=BIGINT}, #{item.score,jdbcType=INTEGER}, #{item.description,jdbcType=VARCHAR},
                #{item.sortOrder,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.riskmatrix.entity.ScoreItemCriteria">
        UPDATE t_score_item_criteria
        SET score_item_id = #{scoreItemId,jdbcType=BIGINT},
            score = #{score,jdbcType=INTEGER},
            description = #{description,jdbcType=VARCHAR},
            sort_order = #{sortOrder,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.riskmatrix.entity.ScoreItemCriteria">
        UPDATE t_score_item_criteria
        <set>
            <if test="scoreItemId != null">score_item_id = #{scoreItemId,jdbcType=BIGINT},</if>
            <if test="score != null">score = #{score,jdbcType=INTEGER},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder,jdbcType=INTEGER},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM t_score_item_criteria
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据评分项ID删除标准 -->
    <delete id="deleteByScoreItemId" parameterType="java.lang.Long">
        DELETE FROM t_score_item_criteria
        WHERE score_item_id = #{scoreItemId,jdbcType=BIGINT}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM t_score_item_criteria
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
