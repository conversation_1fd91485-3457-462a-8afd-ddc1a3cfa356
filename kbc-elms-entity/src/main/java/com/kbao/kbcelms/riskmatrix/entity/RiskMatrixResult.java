package com.kbao.kbcelms.riskmatrix.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 风险矩阵计算结果实体类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixResult {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 风险矩阵ID
     */
    private Long matrixId;
    
    /**
     * 企业ID
     */
    private Long enterpriseId;
    
    /**
     * 企业类型
     */
    private String enterpriseType;
    
    /**
     * 总分
     */
    private BigDecimal totalScore;
    
    /**
     * 平均分
     */
    private BigDecimal averageScore;
    
    /**
     * 整体风险等级
     */
    private String overallRiskLevel;
    
    /**
     * 计算过程数据（JSON格式）
     */
    private String calculationData;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
}
