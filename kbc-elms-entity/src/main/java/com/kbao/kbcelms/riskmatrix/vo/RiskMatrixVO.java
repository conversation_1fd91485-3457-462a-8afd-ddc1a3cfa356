package com.kbao.kbcelms.riskmatrix.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 风险矩阵视图对象
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 矩阵名称
     */
    private String name;
    
    /**
     * 矩阵编码
     */
    private String code;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 矩阵分类
     */
    private String category;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 关联企业类型列表
     */
    private List<String> enterpriseTypes;
    
    /**
     * 企业类型名称（用于显示）
     */
    private String enterpriseTypesDisplay;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 核心类别数量
     */
    private Integer categoryCount;

}
