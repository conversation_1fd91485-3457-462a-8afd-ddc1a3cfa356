package com.kbao.kbcelms.riskmatrix.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 评分项保存请求参数
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScoreItemRequest {
    
    /**
     * 主键ID（更新时必填）
     */
    private Long id;
    
    /**
     * 评分项名称
     */
    @NotBlank(message = "评分项名称不能为空")
    @Size(max = 100, message = "评分项名称长度不能超过100个字符")
    private String name;
    
    /**
     * 评分项编码
     */
//    @NotBlank(message = "评分项编码不能为空")
    @Size(max = 50, message = "评分项编码长度不能超过50个字符")
    private String code;
    
    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    /**
     * 所属类别
     */
    private String category;
    
    /**
     * 权重
     */
    private Double weight;
    
    /**
     * 最大分值
     */
//    @NotNull(message = "最大分值不能为空")
    private Integer maxScore;

    /**
     * 是否关联公式：0-否，1-是
     */
    private Integer isFormula;
    
    /**
     * 关联公式ID
     */
    private Long formulaId;
    
    /**
     * 公式名称
     */
    private String formulaName;
    
    /**
     * 系数
     */
    private Double coefficient;
    
    /**
     * 适用企业类型
     */
    private List<String> enterpriseTypes;
    
    /**
     * 评分标准列表
     */
    private List<CriteriaRequest> criteria;
    
    /**
     * 评分标准请求参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CriteriaRequest {
        
        /**
         * 标准ID（更新时必填）
         */
        private Long id;
        
        /**
         * 分值
         */
        private Integer score;
        
        /**
         * 评分标准描述
         */
        @Size(max = 500, message = "评分标准描述长度不能超过500个字符")
        private String description;
    }
}
