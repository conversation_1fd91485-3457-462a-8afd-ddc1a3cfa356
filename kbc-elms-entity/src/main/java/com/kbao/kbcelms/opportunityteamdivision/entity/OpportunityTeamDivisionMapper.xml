<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result property="opportunityId" column="opportunity_id" jdbcType="INTEGER" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="userId" column="user_id" jdbcType="VARCHAR" />
        <result property="divisionId" column="division_id" jdbcType="VARCHAR" />
        <result property="divisionRatio" column="division_ratio" jdbcType="DECIMAL" />
        <result property="status" column="status" jdbcType="INTEGER" />
        <result property="remark" column="remark" jdbcType="VARCHAR" />
        <result property="createId" column="create_id" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateId" column="update_id" jdbcType="VARCHAR" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER" />
        <result property="times" column="times" jdbcType="INTEGER" />
        <result property="isDefault" column="is_default" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        id, opportunity_id, tenant_id, user_id, division_id, division_ratio, status, remark, create_id, create_time, update_id, update_time, is_deleted, num,is_default,times
    </sql>

    <sql id="Alias_Column_List">
        t.id,
        t.opportunity_id,
        t.tenant_id,
        t.user_id,
        t.division_id,
        t.division_ratio,
        t.status,
        t.remark,
        t.create_id,
        t.create_time,
        t.update_id,
        t.update_time,
        t.is_deleted,
        t.num,
        t.is_default,
        t.times
    </sql>

    <sql id="Base_Condition">
        <where>
            t.is_deleted = 0
            <if test="opportunityId != null">
                and t.opportunity_id = #{opportunityId,jdbcType=INTEGER}
            </if>
            <if test="tenantId != null">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and t.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="divisionId != null">
                and t.division_id = #{divisionId,jdbcType=VARCHAR}
            </if>
            <if test="divisionRatio != null">
                and t.division_ratio = #{divisionRatio,jdbcType=DECIMAL}
            </if>
            <if test="status != null">
                and t.status = #{status,jdbcType=INTEGER}
            </if>
            <if test="remark != null">
                and t.remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="createId != null">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateId != null">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="num != null">
                and t.num = #{num,jdbcType=INTEGER}
            </if>
            <if test="isDefault != null">
                and t.is_default = #{isDefault,jdbcType=INTEGER}
            </if>
            <if test="times != null">
                and t.times = #{times}
            </if>
            <!-- 可扩展自定义条件 -->
        </where>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select <include refid="Base_Column_List" /> from t_opportunity_team_division where id = #{id} and is_deleted = 0
    </select>

    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Alias_Column_List"/>
        from t_opportunity_team_division t
        <include refid="Base_Condition"/>
    </select>


    <select id="findDivision" resultType="com.kbao.kbcelms.opportunityteamdivision.model.OpportunityTeamDivisionVO">
        select
            d.id,d.opportunity_id,d.tenant_id , d.division_id,d.division_ratio ,d.status ,d.remark,d.is_default ,
            d.user_id,d.times,
            IF(t.is_agent=1,t.nick_name,u.nick_name) as nickname ,
            u.email
        from t_opportunity_team_division  d
        left join t_user u on d.user_id =u.user_id and u.is_deleted =0
        left join t_opportunity_team  t on d.opportunity_id =t.opportunity_id and t.user_id =d.user_id and t.is_deleted =0
        where d.tenant_id =#{tenantId}
        and d.is_deleted =0
        and d.opportunity_id =#{opportunityId}
        and d.num = #{num}
        <if test="divisionId != null">
            and d.division_id = #{divisionId}
        </if>
        <if test="userId != null and userId !=null">
            and d.user_id = #{userId}
        </if>
    </select>

    <select id="findOneDivision" resultType="com.kbao.kbcelms.opportunityteamdivision.model.OpportunityTeamDivisionVO">
        select
            d.id,d.opportunity_id,d.tenant_id , d.division_id,d.division_ratio ,d.status ,d.remark,d.is_default ,
            d.user_id,d.times,
            IF(t.is_agent=1,t.nick_name,u.nick_name) as nickname ,
            u.email
        from t_opportunity_team_division  d
        left join t_user u on d.user_id =u.user_id and u.is_deleted =0
        left join t_opportunity_team  t on d.opportunity_id =t.opportunity_id and t.user_id =d.user_id and t.is_deleted =0
        where d.tenant_id =#{tenantId}
          and d.is_deleted =0
          and d.id = #{id}
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0)
        from t_opportunity_team_division t
        <include refid="Base_Condition"/>
    </select>

    <insert id="insert" parameterType="com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_team_division (
            opportunity_id, tenant_id, user_id, division_id, division_ratio, status, remark, create_id, create_time, update_id, update_time, is_deleted, num,is_default,times
        ) values (
            #{opportunityId}, #{tenantId}, #{userId}, #{divisionId}, #{divisionRatio}, #{status}, #{remark}, #{createId}, #{createTime}, #{updateId}, #{updateTime}, 0, #{num},#{isDefault},#{times}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision" useGeneratedKeys="true" keyProperty="id">
        insert into t_opportunity_team_division
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">opportunity_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="divisionId != null">division_id,</if>
            <if test="divisionRatio != null">division_ratio,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="num != null">num,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="times != null">times,</if>
            is_deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opportunityId != null">#{opportunityId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="divisionId != null">#{divisionId},</if>
            <if test="divisionRatio != null">#{divisionRatio},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="num != null">#{num},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="times != null">#{times},</if>
            0,
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision">
        update t_opportunity_team_division set
            opportunity_id = #{opportunityId},
            tenant_id = #{tenantId},
            user_id = #{userId},
            division_id = #{divisionId},
            division_ratio = #{divisionRatio},
            status = #{status},
            remark = #{remark},
            create_id = #{createId},
            create_time = #{createTime},
            update_id = #{updateId},
            update_time = #{updateTime},
            is_deleted = #{isDeleted},
            num = #{num},
            is_default = #{isDefault},
            times = #{times}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision">
        update t_opportunity_team_division
        <set>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="divisionId != null">division_id = #{divisionId},</if>
            <if test="divisionRatio != null">division_ratio = #{divisionRatio},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="num != null">num = #{num},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="times != null">times = #{times},</if>

        </set>
        where id = #{id}
    </update>

    <update id="update" parameterType="com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision">
        update t_opportunity_team_division
        <set>
            <if test="opportunityId != null">opportunity_id = #{opportunityId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="divisionId != null">division_id = #{divisionId},</if>
            <if test="divisionRatio != null">division_ratio = #{divisionRatio},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="num != null">num = #{num},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="times != null">times = #{times},</if>

        </set>
        where id = #{id}
    </update>
    <update id="updateUserId" >
        update t_opportunity_team_division
        set
            user_id = #{userId},
            update_id = #{updateId},
            update_time = now()
        where opportunity_id = #{opportunityId}
            and user_id = #{oldUserId}
            and is_deleted=0
    </update>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update t_opportunity_team_division set is_deleted = 1 where id = #{id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into t_opportunity_team_division (
            opportunity_id, tenant_id, user_id, division_id, division_ratio, status, remark, create_id, create_time, update_id, update_time, is_deleted, num,is_default,times
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.opportunityId}, #{item.tenantId}, #{item.userId}, #{item.divisionId}, #{item.divisionRatio}, #{item.status}, #{item.remark}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, 0, #{item.num},#{item.isDefault},#{item.times}
            )
        </foreach>
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        update t_opportunity_team_division set is_deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByOpportunityId" >
        update t_opportunity_team_division
        set is_deleted = 1,update_time=now()
        where opportunity_id= #{opportunityId}
        <if test="userId != null and userId != ''">and user_id = #{userId}</if>
    </delete>



</mapper> 