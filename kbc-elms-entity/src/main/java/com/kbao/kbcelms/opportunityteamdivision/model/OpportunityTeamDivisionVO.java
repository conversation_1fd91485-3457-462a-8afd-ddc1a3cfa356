package com.kbao.kbcelms.opportunityteamdivision.model;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 项目分工比例
 */
@Data
public class OpportunityTeamDivisionVO {

    /** 编号 */
    private Integer id;
    /** 机会id */
    @NotNull(message = "机会id不能为空")
    private Integer opportunityId;
    /** 租户id */
    private String tenantId;
    /** 用户id */
    @NotNull(message = "用户id不能为空")
    private String userId;
    /**
     * 用户姓名
     */
    private String nickName;
    /**
     * 电子邮箱
     */
    private String email;
    /** 项目分工 */
    @NotNull(message = "项目分工不能为空")
    private String divisionId;
    /** 项目分工比例 */
    @NotNull(message = "项目分工比例不能为空")
    private BigDecimal divisionRatio;
    // 比例上限
    Integer ratio;
    //分工名称
    String name;
    /** 参与状态 0 待确认，1已确认  5 已拒绝 */
    private Integer status;
    /** 说明 */
    private String remark;
    /**
     * 是否默认成员
     */
    private Integer isDefault;
    /**
     * 分工次数
     */
    private Integer num;

    private Integer times;

    /**
     * 角色类型
     */
    private Integer roleType;
}
