<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :loading="saving"
    @save="handleSave"
    @back="handleBack"
  >
    <!-- 基本信息表单 -->
    <div class="form-content">
      <UniversalForm
        ref="basicForm"
        :form-data="form"
        :form-rules="formRules"
        :form-groups="basicFormGroups"
        label-width="120px"
      />

    </div>
  </EditPageContainer>
</template>

<script>
import {
  createQuestionnaire,
  updateQuestionnaire, getQuestionnaire
} from '@/api/questionnaire/management'
import { getEnterpriseTypeOptions } from '@/api/enterprise/type'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'

export default {
  name: 'QuestionnaireEdit',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      saving: false,
      form: {
        title: '',
        description: '',
        enterpriseTypes: [], // 适用企业类型
        status: 2 // 默认为草稿状态
      },
      enterpriseTypeOptions: [], // 企业类型选项
      statusOptions: [ // 状态选项
        { value: 0, label: '禁用' },
        { value: 1, label: '启用' },
        { value: 2, label: '草稿' }
      ]
    }
  },
  computed: {
    pageTitle() {
      const { id } = this.$route.params
      return id && id !== 'new' ? '编辑问卷' : '新建问卷'
    },
    pageIcon() {
      const { id } = this.$route.params
      return id && id !== 'new' ? 'el-icon-edit' : 'el-icon-plus'
    },
    breadcrumbItems() {
      const { id } = this.$route.params
      const isEdit = id && id !== 'new'
      return [
        { text: '问卷管理', to: '/questionnaireList' },
        { text: isEdit ? '编辑问卷' : '新建问卷' }
      ]
    },
    formRules() {
      return {
        title: [{ required: true, message: '请输入问卷标题', trigger: 'blur' }],
        description: [{ required: true, message: '请输入问卷描述', trigger: 'blur' }],
        enterpriseTypes: [{ required: true, message: '请选择适用企业类型', trigger: 'change' }]
      }
    },
    basicFormGroups() {
      return [
        {
          title: '基本信息',
          fields: [[
            {
              prop: 'title',
              label: '问卷标题',
              type: 'input',
              placeholder: '请输入问卷标题',
              required: true
            },
            {
              prop: 'description',
              label: '问卷描述',
              type: 'textarea',
              placeholder: '请输入问卷描述',
              required: true
            },
            {
              prop: 'enterpriseTypes',
              label: '适用企业类型',
              type: 'select',
              placeholder: '请选择适用企业类型',
              required: true,
              multiple: true,
              collapseTags: true,
              filterable: true,
              options: this.enterpriseTypeOptions
            },
            {
              prop: 'status',
              label: '问卷状态',
              type: 'select',
              placeholder: '请选择问卷状态',
              required: true,
              options: this.statusOptions
            }
          ]]
        }
      ]
    },



  },


  beforeDestroy() {
    // 组件销毁前清理备份数据（如果用户直接关闭页面或导航到其他页面）
    this.clearFormBackup()
  },
  created() {
    // 加载企业类型选项
    this.loadEnterpriseTypeOptions()

    // 根据路由参数加载问卷
    const { id } = this.$route.params

    if (id && id !== 'new') {
      // 编辑模式：从后端加载问卷详情
      this.loadQuestionnaireDetail(id)
    } else {
      // 新建模式：使用默认表单数据
      this.initializeNewQuestionnaire()
    }
  },
  methods: {
    // 加载企业类型选项
    async loadEnterpriseTypeOptions() {
      try {
        const response = await getEnterpriseTypeOptions()
        if (response) {
          this.enterpriseTypeOptions = response.map(item => ({
            label: item.name,
            value: item.code
          }))
        }
      } catch (error) {
        console.error('加载企业类型选项失败:', error)
        this.$message.error('加载企业类型选项失败')
      }
    },

    // 加载问卷详情
    async loadQuestionnaireDetail(id) {
      try {
        const response = await getQuestionnaire(id)
        console.log('加载问卷详情响应:', response)

        if (response) {
          // 处理企业类型数据：优先使用enterpriseTypeList，如果没有则解析enterpriseTypes字符串
          let enterpriseTypes = []
          if (response.enterpriseTypeList && Array.isArray(response.enterpriseTypeList)) {
            enterpriseTypes = response.enterpriseTypeList
          } else if (response.enterpriseTypes && typeof response.enterpriseTypes === 'string') {
            enterpriseTypes = response.enterpriseTypes.split(',').filter(type => type.trim())
          }

          // 设置基本信息数据
          this.form = {
            id: response.id,
            title: response.title,
            description: response.description,
            enterpriseTypes: enterpriseTypes,
            status: response.status !== undefined ? response.status : 2 // 默认草稿状态
          }

          // 尝试恢复用户之前填写的基本信息（如果有的话）
          const restored = this.restoreFormData()
          if (restored) {
            console.log('已恢复用户之前填写的基本信息')
          }
        }
      } catch (error) {
        console.error('加载问卷详情失败:', error)
        this.$message.error('加载问卷详情失败')
      }
    },

    // 初始化新问卷
    initializeNewQuestionnaire() {
      this.form = {
        title: '',
        description: '',
        enterpriseTypes: [],
        status: 2 // 默认草稿状态
      }

      // 尝试恢复用户之前填写的基本信息（如果有的话）
      const restored = this.restoreFormData()
      if (restored) {
        console.log('已恢复用户之前填写的基本信息')
      }
    },

    // 保存问卷
    async handleSave() {
      try {
        // 表单验证
        await this.$refs.basicForm.validate()

        this.saving = true

        const { id } = this.$route.params
        const saveData = {
          title: this.form.title,
          description: this.form.description,
          enterpriseTypes: this.form.enterpriseTypes,
          status: this.form.status // 使用表单中的状态值
        }

        console.log('准备保存的数据:', saveData)

        let response
        if (id && id !== 'new') {
          // 更新问卷
          console.log('更新问卷，ID:', id)
          response = await updateQuestionnaire(id, saveData)
        } else {
          // 创建问卷
          console.log('创建新问卷')
          response = await createQuestionnaire(saveData)
        }

        console.log('后端响应:', response)

        // 保存成功后清理备份数据
        this.clearFormBackup()

        this.$message.success(id && id !== 'new' ? '问卷更新成功' : '问卷创建成功')

        // 保存成功后跳转到列表页
        this.$router.push({ name: 'questionnaireList' })

      } catch (error) {
        console.error('保存问卷失败:', error)
        this.$message.error('保存问卷失败')
      } finally {
        this.saving = false
      }
    },
    handleBack() {
      // 返回前清理备份数据
      this.clearFormBackup()
      this.$router.back()
    },

    // 保存当前表单数据到sessionStorage
    saveCurrentFormData() {
      try {
        const formDataToSave = {
          title: this.form.title,
          description: this.form.description,
          enterpriseTypes: this.form.enterpriseTypes,
          status: this.form.status,
          timestamp: Date.now()
        }

        console.log('保存当前表单数据:', formDataToSave)
        sessionStorage.setItem('questionnaireFormBackup', JSON.stringify(formDataToSave))
      } catch (error) {
        console.error('保存表单数据失败:', error)
      }
    },

    // 从sessionStorage恢复表单数据
    restoreFormData() {
      try {
        const backupData = sessionStorage.getItem('questionnaireFormBackup')
        if (backupData) {
          const parsedData = JSON.parse(backupData)
          console.log('恢复表单数据:', parsedData)

          // 检查数据是否在合理的时间范围内（5分钟内）
          const timeDiff = Date.now() - parsedData.timestamp
          if (timeDiff < 5 * 60 * 1000) { // 5分钟
            // 恢复基本信息
            this.form.title = parsedData.title || this.form.title
            this.form.description = parsedData.description || this.form.description
            this.form.enterpriseTypes = parsedData.enterpriseTypes || this.form.enterpriseTypes
            this.form.status = parsedData.status !== undefined ? parsedData.status : this.form.status

            console.log('表单数据恢复成功')
            return true
          } else {
            console.log('备份数据过期，不进行恢复')
            sessionStorage.removeItem('questionnaireFormBackup')
          }
        }
      } catch (error) {
        console.error('恢复表单数据失败:', error)
      }
      return false
    },

    // 清理备份数据
    clearFormBackup() {
      try {
        sessionStorage.removeItem('questionnaireFormBackup')
        console.log('清理表单备份数据')
      } catch (error) {
        console.error('清理备份数据失败:', error)
      }
    }

  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/shared-styles.less";

.form-content {
  margin-bottom: 24px;
}

.questions-section {
  margin-bottom: 24px;

  // 表格样式优化
  .el-table {
    .el-table__body-wrapper {
      .el-table__row {
        .el-table__cell {
          padding: 12px 8px;

          // 确保表单组件正确显示
          .el-select,
          .el-input-number,
          .el-input {
            width: 100%;
          }

          // 选择框样式
          .el-select {
            .el-input__inner {
              height: 32px;
              line-height: 32px;
            }
          }

          // 数字输入框样式
          .el-input-number {
            .el-input__inner {
              height: 32px;
              line-height: 32px;
              text-align: center;
            }
          }

          // 文本域样式
          .el-textarea {
            .el-textarea__inner {
              resize: none;
            }
          }
        }
      }
    }
  }
}


</style>
