<template>
  <div class="detail-info">
    <el-breadcrumb class="breadcrumb dt-bread" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item @click.native="goBack"> {{ getEntranceTitle() }} </el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: themeObj.color }"> 询价详情 </el-breadcrumb-item>
    </el-breadcrumb>

    <TableToolTemp :toolListProps="toolListProps" class="log-tool"></TableToolTemp>

    <div v-if="entrance == '2'">
    <el-divider></el-divider>

    <TableToolTemp :toolListProps="{ toolTitle: '机会信息' }" class="log-tool"></TableToolTemp>
    <div class="info-item">
      <div class="item">机会名称：{{ (inquiryDetail.opportunity && inquiryDetail.opportunity.opportunityName) || '-'
        }}</div>
      <div class="item">机会ID：{{ (inquiryDetail.opportunity && inquiryDetail.opportunity.opportunityId) || '-' }}</div>
      <div class="item">询价公司：{{ inquiryDetail.opportunity && inquiryDetail.opportunity.inquiryCompanyName}}</div>
      <div> <i class="el-icon-link"></i> <a href="" @click.prevent="goOpportunityDetail">机会详情</a></div>
    </div>
    <div class="info-item">
      <div class="item">顾问姓名：{{ inquiryDetail.opportunity && inquiryDetail.opportunity.agentName }}</div>
      <div class="item">顾问工号：{{ inquiryDetail.opportunity && inquiryDetail.opportunity.agentCode }} </div>
      <div class="item">所属机构：{{ inquiryDetail.opportunity && inquiryDetail.opportunity.companyName }}</div>
      <div class="item">项目经理：{{ inquiryDetail.opportunity && inquiryDetail.opportunity.projectManager }}</div>
    </div>
    </div>

    <el-divider></el-divider>

    <TableToolTemp :toolListProps="{ toolTitle: '项目信息' }" class="log-tool"></TableToolTemp>
    <div class="info-item">
      <div class="item">项目名称：{{ (inquiryDetail.project && inquiryDetail.project.projectName) || '-' }}</div>
      <div class="item">项目编号：{{ (inquiryDetail.project && inquiryDetail.project.projectId) || '-' }}</div>
      <div class="item">提交时间：{{ (inquiryDetail.project && inquiryDetail.project.submitTime) || '-' }}</div>
    </div>

    <el-divider></el-divider>

    <TableToolTemp :toolListProps="{ toolTitle: '客户信息' }" class="log-tool"></TableToolTemp>
    <div class="info-item">
      <div class="item">投保人：{{ (inquiryDetail.customer && inquiryDetail.customer.policyholder) || '-' }}</div>
      <div class="item">被保人：{{ (inquiryDetail.customer && inquiryDetail.customer.insured) || '-' }}</div>
    </div>
    <div class="info-item">
      <div class="item">社会统一信用代码：{{ (inquiryDetail.customer && inquiryDetail.customer.policyholderCreditCode) || '-' }}</div>
      <div class="item">社会统一信用代码：{{ (inquiryDetail.customer && inquiryDetail.customer.insuredCreditCode) || '-' }}</div>
    </div>
    <div class="info-item">
      <div class="item">行业类型：{{ (inquiryDetail.customer && inquiryDetail.customer.policyholderIndustryType) || '-' }}</div>
      <div class="item">行业类型：{{ (inquiryDetail.customer && inquiryDetail.customer.insuredIndustryType) || '-' }}</div>
    </div>

    <el-divider></el-divider>

    <TableToolTemp :toolListProps="{ toolTitle: '询价信息' }" class="log-tool"></TableToolTemp>
    <div class="info-item">
      <div class="item">平安业务员：{{ (inquiryDetail.inquiryInfo && inquiryDetail.inquiryInfo.salesman) || '-' }}</div>
    </div>
    <div class="info-item">
      <div class="item">预计成交时间：{{ (inquiryDetail.inquiryInfo && inquiryDetail.inquiryInfo.dealTime) || '-' }}</div>
    </div>
    <div class="" style="padding: 0 20px;">
      <div class="item">
        <el-table :data="inquiryDetail.inquiryInfo.productData" class="dt-table" v-hover border stripe>
          <el-table-column align="center" label="产品大类">
            <template slot-scope="scope">
              <el-select v-model="scope.row.productCategoryCode" placeholder="请选择产品大类" disabled>
                <el-option v-for="item in productCategoryList" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" label="产品名称">
            <template slot-scope="scope">
              <el-select v-model="scope.row.productCode" placeholder="请选择产品名称" disabled>
                <el-option v-for="item in productNameList" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="premium" label="预计保费">
            <template slot-scope="scope">
              <el-input v-model="scope.row.premium" style="width: 200px" disabled></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="item-premium">预计总保费：<span style="color:#CC0000;">{{inquiryDetail.inquiryInfo.totalPremium}}</span> 元</div>
    </div>
      <div class="" style="padding: 20px 20px;">
        <div class="item">询价描述</div>
      </div>
    <div style="padding: 0 20px;">
      <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8}" placeholder="请输入上报备注" v-model="inquiryDetail.inquiryInfo.notes"
                disabled></el-input>
    </div>
      <div class="" style="padding-left: 20px;padding-top: 20px;">
        <div class="item">附件</div>
      </div>
    <div style="padding-left: 20px;padding-top: 20px;">
      <div class="product-item" v-for="(item, index) in inquiryDetail.inquiryInfo.productPlanList" :key="index">
        <div style="width: 60%; padding-left:10px;">
          <span>{{ item.fileName }}</span>
        </div>
        <div style="width: 35%;text-align: right">
          <i class="el-icon-download"></i>
          <el-button type="text" @click="download(item)" style="padding-left: 5px;">下载</el-button>
        </div>
      </div>
    </div>

    <el-divider></el-divider>

    <TableToolTemp :toolListProps="{ toolTitle: '关联询价单' }" class="log-tool"></TableToolTemp>
    <div class="info-item-order">
      <el-table :data="inquiryDetail.inquiryOrder" class="dt-table" style="width: 100%" v-hover stripe>
        <el-table-column align="center" prop="inquiryNo" label="询价单号"></el-table-column>
        <el-table-column align="center" prop="documentNo" label="单证号"></el-table-column>
        <el-table-column align="center" prop="productCategory" label="产品大类"></el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称"></el-table-column>
        <el-table-column align="center" prop="premium" label="保费"></el-table-column>
        <el-table-column align="center" prop="status" label="状态"></el-table-column>
        <el-table-column align="center" prop="inquiryTime" label="询价时间"></el-table-column>
        <el-table-column align="center" prop="insurancePeriod" label="保险期限"></el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button class="btn-center" type="text" @click="download(scope.row)">下载报价单</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-divider></el-divider>

    <div class="foot-btn">
      <el-button class="btn-center" :type="buttonType" :disabled="buttonClickStatus" @click="revoke">撤销</el-button>
      <el-button class="btn-center" :type="buttonType" :disabled="buttonClickStatus" @click="requote">重新报价</el-button>
      <el-button class="btn-center" :type="buttonType" :disabled="buttonClickStatus" @click="update">编辑</el-button>
      <el-button class="btn-center" :type="buttonType" :disabled="buttonClickStatus" @click="expedite">催办</el-button>
      <el-button class="btn-center" :type="buttonType" :disabled="buttonClickStatus" @click="agreeOrder">同意出单</el-button>
    </div>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/userManagement/index.js";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { getRolePage } from "@/api/roleManagement/index.js";
import {findLegalOrgDataByTenantId, findRolesByUserId, getRoleList} from "@/api/userManagement/index.js";

export default {
  name: "inquiryDetail",
  data() {
    return {
      toolListProps: {
        toolTitle: "询价详情",
        toolList: []
      },
      entranceTitle:"线上询价记录",
      entrance:"1",
      buttonClickStatus:false,
      buttonType:"primary",
      inquiryDetail:{
        opportunity:{
          opportunityId:"",
          opportunityName:""
        },// 机会信息
        project:{
          projectId:"",
          projectName:"",
          submitTime:""
        },// 项目信息
        customer:{
          policyholder:"",
          insured:"",
          policyholderCreditCode:"",
          insuredCreditCode:"",
          policyholderIndustryType:"",
          insuredIndustryType:""
        },// 客户信息
        inquiryInfo:{
          salesman:"",
          dealTime:"",
          productData:[],
          notes:"",
          productPlanList:[],
          totalPremium:5000
        },// 询价信息
        inquiryOrder:[]// 关联询价单
      },
      productCategoryList:[],
      productNameList:[],
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    tenantId() {
      return this.$store.state.layoutStore.currentLoginUser.tenantId;
    },
  },
  filters: {
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    getEntranceTitle() {
      return this.entranceTitle;
    },
    goBack(){
      let entrance = this.$route.query.entrance;
      if (entrance == '2') {
        this.$router.push({
          name: "inquiryRecordList"
        });
      } else {
        this.$router.push({
          name: "userList"
        });
      }

    },
    async getDicFun() {
    },
    async initData() {

      this.entrance = this.$route.query.entrance;
      console.log(this.$route.query.opportunityId, '--------------queryId-------------------');

      if (this.$route.query.entrance == '2') {
        this.entranceTitle = "线上询价记录";
        this.buttonClickStatus = true;
        this.buttonType = 'info';
      } else {
        this.entranceTitle = "询价记录";
        this.buttonClickStatus = false;
        this.buttonType = 'primary';
      }

      this.inquiryDetail.opportunity = {
        opportunityId: "25",
        opportunityName: "海底捞-员福-2025",
        inquiryCompanyName: "平安产险",
        agentName: "徐晨旭",
        agentCode: "6400000001",
        companyName: "北京分公司",
        projectManager: "李增额",
      };

      this.inquiryDetail.project = {
        projectId: "39239239923",
        projectName: "投保企业名称+产品大类 项目",
        submitTime: "2025-04-11 15:00:23"
      };

      this.inquiryDetail.customer = {
        policyholder: "四川海底捞餐饮股份有限公司",
        insured: "四川海底捞餐饮股份有限公司",
        policyholderCreditCode: "239239239923",
        insuredCreditCode: "239239239923",
        policyholderIndustryType: "餐饮业-正餐服务",
        insuredIndustryType: "餐饮业-正餐服务"
      };

      this.inquiryDetail.inquiryInfo = {
        salesman: "业务员姓名",
        dealTime: "2025-04-11",
        productData: [
          {index:1,productCategoryCode:"产品大类1-value",productCode:"产品1-产品大类1-value",premium:"5000"}
        ],
        notes: "目前，[公司 / 单位] 在 [财产安全 / 员工保障 / 业务运营等方面] 存在一定风险隐患。例如，[具体说明风险情况，如公司固定资产面临自然灾害或意外事故损失风险、员工在工作中可能遭遇意外伤害、业务开展过程中可能因责任问题引发纠纷等]。为有效转移和规避这些风险，降低因意外事件造成的经济损失，保障公司正常运营和员工合法权益，采购相应的保险产品十分必要。",
        productPlanList: [
          {
            'fileUrl': 'https://www-sta.kbao123.com/gateway/oss2/kbc-ufs-sta/T0001/B00008/T0001/logo/2025/01/23/4e62d8/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250123110249.png?Expires=2368321430&OSSAccessKeyId=LTAI4GJaRdNjjXCwyh28M7Vr&Signature=rY04uEOiG3DUGKQc%2FSrlGp9xABk%3D',
            'fileName': '文件文件名称1'
          },
          {
            'fileUrl': 'https://www-sta.kbao123.com/gateway/oss2/kbc-ufs-sta/T0001/B00008/T0001/logo/2024/01/26/ec9641/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20240126161813.png?Expires=2336977111&OSSAccessKeyId=LTAI4GJaRdNjjXCwyh28M7Vr&Signature=JzUNl%2FJpL%2Bk0Hr%2BelrCiZFohNdg%3D',
            'fileName': '文件文件名称2'
          }
        ],
        totalPremium:5000
      };
      this.inquiryDetail.inquiryOrder = [
        {
          "inquiryNo":"Q056000390003825134446-1","documentNo":"","productCategory":"团意健","productName":"平安团体意健自定义产品","premium":"10000","status":"2","inquiryTime":"2025-05-12 02:01:24","insurancePeriod":"2025-04-12 - 2026-04-11"
        },
        {
          "inquiryNo":"Q056000390003825134446-2","documentNo":"","productCategory":"团意健","productName":"平安团体意健自定义产品","premium":"10000","status":"2","inquiryTime":"2025-05-12 02:01:24","insurancePeriod":"2025-04-12 - 2026-04-11"
        }
      ];

      // this.initParam.param.tenantId = this.tenantId;
      // let res = await api.getUserList(this.initParam);
      // if (res) {
      //   this.total = res.total;
      //   this.tableData = [];
      //   if (res.list) {
      //     this.tableData = res.list ? res.list : [{}];
      //   }
      // }
    },
    // 机会详情
    goOpportunityDetail() {
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: this.inquiryDetail.opportunity.opportunityId
        }
      });
    },
    // 下载文件
    download(item) {
      let link = document.createElement("a");
      link.setAttribute("download", item.fileName);
      link.href = item.fileUrl;
      link.click();
    },
    // 撤销
    revoke(){},
    // 重新报价
    requote(){},
    // 编辑
    update(){},
    // 催办
    expedite(){},
    // 同意出单
    agreeOrder(){},
  }
};
</script>
<style lang="less">
.detail-info {
  .log-tool {
    margin-top: 20px;
  }
  .link-text{
    color: #409EFF;
    cursor: pointer;
    text-decoration: underline;
    font-size: 14px;
  }

  .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    text-align: center;
    margin-bottom: 20px;
  }

  .info-item {
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;

    .item {
      font-size: 14px;
      color: #333;
      width: 25%;
      padding: 0 10px;
      margin-bottom: 20px;
    }
  }

  .info-item-order {
    padding: 0 20px;
    //width: 80%;
  }

  .tool-wrap {
    padding: 20px 20px 0 20px;
  }

  .item-tips {
    font-size: 14px;
    color: #333;
    padding-left: 20px;
  }

  .product-item {
    display: flex;
    background-color: rgba(242, 242, 242, 1);
    margin-bottom: 10px;
    border-radius: 5px;
    padding-top: 10px;
    width: 40%;
  }


  .foot-btn {
    text-align: center;
    padding: 30px;
  }

  .item-premium {
      text-align: right;
      //width: 80%;
      border: 1px solid #EBEEF5;
      height: 60px;
      padding: 20px 100px;
  }
}
</style>
