<template>
  <div class="online-product-config-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="线上产品配置"
      subtitle="配置行业风险等级和险种类型，支持动态调整产品配置规则"
      title-icon="el-icon-goods"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchParamsWithParam"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="240"
      :search-label-width="'100px'"
      add-button-text="新增配置"
      empty-title="暂无产品配置"
      empty-description="点击上方新增配置按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 自定义列内容插槽 -->
      <template #industryName="{ row }">
        <div class="industry-display">
          <div 
            :class="['industry-content', { 'collapsed': !isExpanded(getRowKey(row)) }]"
            v-if="row.industryName"
          >
            <span 
              v-for="(name, index) in getIndustryNames(row.industryName)" 
              :key="index"
              class="industry-tag"
            >
              {{ name }}
            </span>
          </div>
          <div class="industry-actions" v-if="shouldShowToggle(row.industryName)">
            <el-button 
              type="text" 
              size="mini"
              @click.stop="toggleExpand(getRowKey(row))"
              class="toggle-btn"
            >
              {{ isExpanded(getRowKey(row)) ? '收起' : '展开' }}
              <i :class="isExpanded(getRowKey(row)) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </el-button>
          </div>
          <span v-if="!row.industryName" class="no-data">未配置</span>
        </div>
      </template>
      
      <template #probability="{ row }">
        <el-tag :type="row.probability === '高' ? 'danger' : row.probability === '中' ? 'warning' : 'info'" size="small">
          {{ row.probability }}
        </el-tag>
          </template>
      
      <template #impact="{ row }">
        <el-tag :type="row.impact === '高' ? 'danger' : row.impact === '中' ? 'warning' : 'info'" size="small">
          {{ row.impact }}
        </el-tag>
          </template>
      
      <template #level="{ row }">
        <el-tag :type="row.level === '高' ? 'danger' : row.level === '中' ? 'warning' : 'info'" size="small">
          {{ row.level }}
        </el-tag>
          </template>
      
      <template #insuranceTypes="{ row }">
        <div v-if="row.insuranceTypes && row.insuranceTypes.length" >
              <el-tag 
              type="info"
              size="small" 
              v-for="type in row.insuranceTypes" 
                :key="type" 
              style="margin-bottom: 4px; margin-left: 4px;"
              >
                {{ getInsuranceTypeName(type) }}
              </el-tag>
            </div>
        <span v-else style="color:#bbb;">未配置</span>
          </template>
      
      <template #enabled="{ row }">
        <el-switch
          v-model="row.enabled"
          active-color="#67c23a"
          inactive-color="#909399"
          @change="handleStatusChange(row)"
          :loading="row.statusLoading"
          :disabled="row.statusLoading"
        />
      </template>
      
      <template #createTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.createTime }}
      </div>
      </template>
    </UniversalTable>



    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该配置？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import { 
  getIndustryTree, 
  getOnlineProductConfig,
  deleteOnlineProductConfig
} from '@/api/onlineProduct'
import { getInsureTypeMap } from '@/api/basicConfig'
import { getDicItems } from '@/api/dictionary'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'OnlineProductConfig',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  computed: {
    // 添加一个计算属性，将searchForm转换为带param的结构
    searchParamsWithParam() {
      return {
        param: { ...this.searchForm }
      }
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        industryCode: [],
        probability: '',
        level: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'industryName',
          label: '行业分类',
          minWidth: 250,
          align: 'left',
          showOverflowTooltip: false
        },
        {
          prop: 'probability',
          label: '风险发生概率',
          width: 120,
          align: 'center'
        },
        {
          prop: 'impact',
          label: '风险影响程度',
          width: 120,
          align: 'center'
        },
        {
          prop: 'level',
          label: '风险等级',
          width: 120,
          align: 'center'
        },
        {
          prop: 'insuranceTypes',
          label: '险种类型',
          minWidth: 120,
          align: 'center'
        },
        {
          prop: 'enabled',
          label: '状态',
          width: 100,
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '行业类别',
          name: 'industryCode',
          type: 'cascader',
          placeholder: '选择行业类别筛选，支持多选，下级全选时上级自动选中',
          options: [],
          props: {
            value: 'value',
            label: 'label',
            children: 'children',
            expandTrigger: 'click',        // 优化：点击展开
            emitPath: false,
            multiple: true,
            checkStrictly: false          // 修改：关闭严格模式，启用父子级联
          },
          filterable: true,                // 优化：启用搜索
          collapseTags: true,             // 启用标签折叠
          maxCollapseTags: 5,             // 搜索表单显示5个标签
          showAllLevels: false,           // 优化：简化显示
          clearable: true                // 优化：可清空
        }
      ],
      industryOptions: [],
      insuranceTypeOptions: [],
      riskLevelOptions: [], // 风险字典选项
      currentConfig: null,
      // 展开状态管理
      expandedRows: {},
      maxDisplayLines: 2 // 最大显示行数
    }
  },
  mounted() {
    this.loadIndustryTree();
    this.loadInsuranceTypeOptions();
    this.loadRiskLevelOptions();
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      try {
        // 智能优化搜索表单中的行业代码
        const optimizedSearchForm = {
          ...this.searchForm,
          industryCode: this.optimizeSearchIndustryCode(this.searchForm.industryCode)
        };
        
        const pageRequest = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: optimizedSearchForm
        };
        
        console.log('原始搜索条件:', this.searchForm);
        console.log('优化后搜索条件:', optimizedSearchForm);
        
        const response = await getOnlineProductConfig(pageRequest);
        
        // 直接使用响应数据（基于行业限制模块的经验）
        this.tableData = response.list || [];
        this.pagination.total = response.total || 0;
      } catch (error) {
        console.error('加载配置失败:', error);
        this.$message.error('加载配置失败');
        this.tableData = [];
      } finally {
        this.loading = false;
      }
    },
    async loadIndustryTree() {
      try {
        const response = await getIndustryTree();
        console.log('行业树响应:', response); // 调试日志
        
        // 解析响应数据结构
        let industryData = [];
        if (response && response.datas && Array.isArray(response.datas)) {
          industryData = response.datas;
        } else if (response && Array.isArray(response)) {
          industryData = response;
        } else {
          console.warn('行业树数据格式不符合预期:', response);
          industryData = [];
        }
        
        this.industryOptions = industryData;
        // 更新搜索表单配置中的行业选项（使用树形结构）
        this.searchFormConfig[0].options = industryData;
      } catch (error) {
        console.error('加载行业树失败:', error);
        this.$message.error('加载行业树失败');
      }
    },
    async loadInsuranceTypeOptions() {
      try {
        const response = await getInsureTypeMap();
        // 解析响应数据（FormConfigService可能返回不同的数据结构）
        console.log('险种类型响应:', response);
        
        // 根据实际返回的数据结构处理：{"2":"团意险","3":"雇主险","4":"团体高医"}
        if (response && typeof response === 'object' && !Array.isArray(response)) {
          // 如果返回的是Map格式，转换为数组
          this.insuranceTypeOptions = Object.keys(response).map(key => ({
            label: response[key],
            value: key
          }));
        } else if (response && Array.isArray(response)) {
          // 如果是标准数组格式
          this.insuranceTypeOptions = response;
        } else if (response && response.data && Array.isArray(response.data)) {
          // 如果是嵌套数组格式
          this.insuranceTypeOptions = response.data;
        } else {
          console.warn('险种类型数据格式不符合预期:', response);
          this.insuranceTypeOptions = [];
        }
      } catch (error) {
        console.error('加载险种类型选项失败:', error);
        this.$message.error('加载险种类型选项失败');
      }
    },
    async loadRiskLevelOptions() {
      try {
        const response = await getDicItems({ dicCode: 'elms.risk.level' });
        console.log('风险字典响应:', response);
        
        // 解析响应数据：标准字典数组格式
        let riskLevelData = [];
        if (response && Array.isArray(response)) {
          // 标准字典数组格式，按dicOrder排序
          riskLevelData = response
            .filter(item => item.isShow === 1 && item.isEnable === 1) // 过滤启用且显示的项
            .sort((a, b) => (a.dicOrder || 0) - (b.dicOrder || 0)) // 按排序字段排序
            .map(item => ({
              label: item.dicItemName,
              value: item.dicItemName // 使用名称作为值，保持与现有业务逻辑一致
            }));
        } else if (response && response.data && Array.isArray(response.data)) {
          // 如果是嵌套格式
          riskLevelData = response.data
            .filter(item => item.isShow === 1 && item.isEnable === 1)
            .sort((a, b) => (a.dicOrder || 0) - (b.dicOrder || 0))
            .map(item => ({
              label: item.dicItemName,
              value: item.dicItemName
            }));
        } else {
          console.warn('风险字典数据格式不符合预期:', response);
          // 降级处理：使用默认选项，按字典排序
          riskLevelData = [
            { label: '低', value: '低' },
            { label: '中', value: '中' },
            { label: '高', value: '高' }
          ];
        }
        
        this.riskLevelOptions = riskLevelData;
        
        // 更新搜索表单配置中的风险选项
        if (this.searchFormConfig[1]) {
          this.searchFormConfig[1].options = riskLevelData;
        }
        if (this.searchFormConfig[2]) {
          this.searchFormConfig[2].options = riskLevelData;
        }
        
      } catch (error) {
        console.error('加载风险字典失败:', error);
        this.$message.error('加载风险字典失败');
      }
    },
    getIndustryName(codes) {
      // 支持单个代码或代码数组
      if (!codes) return '';
      
      const codeArray = Array.isArray(codes) ? codes : [codes];
      const flatOptions = this.flattenIndustryTree(this.industryOptions);
      
      const names = codeArray.map(code => {
        const item = flatOptions.find(i => i.value === code);
        return item ? (item.fullName || item.label) : code;
      });
      
      return names.join('，');
    },
    
    // 扁平化行业树结构，用于搜索和显示
    flattenIndustryTree(tree) {
      const result = [];
      const flatten = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return;
        nodes.forEach(node => {
          result.push({
            label: node.label,
            value: node.value,
            fullName: node.fullName, // 保留完整名称
            fullPath: node.fullPath, // 保留完整路径
            level: node.level
          });
          if (node.children && Array.isArray(node.children) && node.children.length > 0) {
            flatten(node.children);
          }
        });
      };
      flatten(tree || []);
      return result;
    },
    getInsuranceTypeName(value) {
      const item = this.insuranceTypeOptions.find(i => i.value === value);
      return item ? item.label : value;
    },
    
    // 行业分类显示相关方法
    getIndustryNames(industryNameStr) {
      if (!industryNameStr) return [];
      return industryNameStr.split('，').filter(name => name.trim());
    },
    
    // 优化搜索中的行业代码
    optimizeSearchIndustryCode(selectedValues) {
      if (!Array.isArray(selectedValues) || selectedValues.length === 0) {
        return selectedValues;
      }
      
      // 使用与编辑页面相同的优化逻辑
      return this.optimizeSelectedValues(selectedValues);
    },
    
    // 智能优化选中的值（复用编辑页面逻辑）
    optimizeSelectedValues(selectedValues) {
      if (!this.industryOptions || this.industryOptions.length === 0) {
        return selectedValues;
      }
      
      // 创建一个包含所有节点的扁平映射
      const allNodesMap = this.createNodesMap(this.industryOptions);
      
      // 从最深层级开始向上优化
      let optimized = [...selectedValues];
      
      // 多次迭代直到没有更多优化
      let hasOptimization = true;
      while (hasOptimization) {
        hasOptimization = false;
        const newOptimized = this.optimizeOneLevel(optimized, allNodesMap);
        if (newOptimized.length !== optimized.length || 
            !newOptimized.every(v => optimized.includes(v))) {
          optimized = newOptimized;
          hasOptimization = true;
        }
      }
      
      return optimized;
    },
    
    // 创建节点映射
    createNodesMap(nodes) {
      const map = new Map();
      
      const traverse = (nodeList, parent = null) => {
        if (!nodeList) return;
        
        nodeList.forEach(node => {
          map.set(node.value, {
            ...node,
            parent: parent,
            children: node.children || []
          });
          
          if (node.children && node.children.length > 0) {
            traverse(node.children, node.value);
          }
        });
      };
      
      traverse(nodes);
      return map;
    },
    
    // 优化一个层级
    optimizeOneLevel(selectedValues, allNodesMap) {
      const result = [];
      const processed = new Set();
      
      // 按父级分组
      const groupsByParent = new Map();
      
      selectedValues.forEach(value => {
        if (processed.has(value)) return;
        
        const node = allNodesMap.get(value);
        if (!node) {
          result.push(value);
          processed.add(value);
          return;
        }
        
        const parentValue = node.parent;
        if (!parentValue) {
          // 没有父级，直接添加
          result.push(value);
          processed.add(value);
        } else {
          // 有父级，按父级分组
          if (!groupsByParent.has(parentValue)) {
            groupsByParent.set(parentValue, []);
          }
          groupsByParent.get(parentValue).push(value);
        }
      });
      
      // 检查每个父级下是否完全选中
      groupsByParent.forEach((childValues, parentValue) => {
        const parentNode = allNodesMap.get(parentValue);
        if (!parentNode) {
          // 父级节点不存在，保持原样
          childValues.forEach(v => {
            if (!processed.has(v)) {
              result.push(v);
              processed.add(v);
            }
          });
          return;
        }
        
        const allChildValues = parentNode.children.map(child => child.value);
        
        // 检查是否选中了所有子级
        const selectedChildValues = childValues.filter(v => allChildValues.includes(v));
        
        if (selectedChildValues.length === allChildValues.length && allChildValues.length > 0) {
          // 完全选中，使用父级
          if (!processed.has(parentValue)) {
            result.push(parentValue);
            processed.add(parentValue);
          }
          // 标记所有子级为已处理
          childValues.forEach(v => processed.add(v));
        } else {
          // 部分选中，保持子级
          childValues.forEach(v => {
            if (!processed.has(v)) {
              result.push(v);
              processed.add(v);
            }
          });
        }
      });
      
      return result;
    },
    
    shouldShowToggle(industryNameStr) {
      if (!industryNameStr) return false;
      const names = this.getIndustryNames(industryNameStr);
      return names.length > this.maxDisplayLines;
    },
    
    getRowKey(row) {
      // 使用id作为主键，如果没有id则使用索引或其他唯一标识
      return row.id || row.uuid || `row_${JSON.stringify(row).slice(0, 20)}`;
    },
    
    isExpanded(rowKey) {
      return !!this.expandedRows[rowKey];
    },
    
    toggleExpand(rowKey) {
      console.log('toggleExpand called with rowKey:', rowKey);
      console.log('Current expanded state:', this.expandedRows[rowKey]);
      
      // 使用Vue.set确保响应式更新
      this.$set(this.expandedRows, rowKey, !this.expandedRows[rowKey]);
      
      console.log('New expanded state:', this.expandedRows[rowKey]);
      console.log('Current expandedRows:', this.expandedRows);
    },
    getProbabilityTagType(probability) {
      const typeMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'success'
      };
      return typeMap[probability] || 'info';
    },
    getImpactTagType(impact) {
      const typeMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'success'
      };
      return typeMap[impact] || 'info';
    },
    getLevelTagType(level) {
      const typeMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'success'
      };
      return typeMap[level] || 'info';
    },
    // UniversalTable 事件处理方法
    handleSearch(params) {
      this.searchForm = { ...params.param };
      this.pagination.pageNum = 1;
      this.loadData();
    },
    handleReset() {
      this.searchForm = {
        industryCode: [],
        probability: '',
        level: ''
      };
      this.pagination.pageNum = 1;
      this.loadData();
    },
    handleAdd() {
      this.$router.push({
        name: 'onlineProductConfigEdit',
        query: { mode: 'add' }
      });
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.pageNum = 1;
      this.loadData();
    },
    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.loadData();
    },
    handleAction(actionData) {
      const { action, row } = actionData;
      if (action === 'view') {
        this.handleView(row);
      } else if (action === 'edit') {
        this.handleEdit(row);
      } else if (action === 'delete') {
        this.handleDelete(row);
      }
    },
    handleView(row) {
      this.$router.push({
        name: 'onlineProductConfigEdit',
        params: { id: row.id },
        query: { mode: 'view' }
      });
    },
    handleEdit(row) {
      this.$router.push({
        name: 'onlineProductConfigEdit',
        params: { id: row.id },
        query: { mode: 'edit' }
      });
    },
    handleDelete(row) {
      this.$refs.confirmDialog.show();
      this.currentConfig = row;
    },
    
    // 处理开关状态切换
    async handleStatusChange(row) {
      const newStatus = row.enabled;
      const actionText = newStatus ? '启用' : '禁用';
      
      // 设置加载状态
      this.$set(row, 'statusLoading', true);
      
      try {
        // 调用专用的启用/禁用接口
        await this.toggleOnlineProductConfigStatus(row.id, newStatus);
        
        this.$message.success(`${actionText}成功`);
        
        // 操作成功后不需要刷新整个列表，开关状态已经更新
        
      } catch (error) {
        console.error(`${actionText}失败:`, error);
        this.$message.error(`${actionText}失败: ${error.message || '未知错误'}`);
        
        // 操作失败时，恢复开关状态
        row.enabled = !newStatus;
        
      } finally {
        // 清除加载状态
        this.$set(row, 'statusLoading', false);
      }
    },
    
    // 更新产品配置
    async updateOnlineProductConfig(data) {
      try {
        const { updateOnlineProductConfig } = await import('@/api/onlineProduct');
        return await updateOnlineProductConfig(data);
      } catch (error) {
        console.error('更新产品配置失败:', error);
        throw error;
      }
    },
    
    // 切换产品配置启用/禁用状态
    async toggleOnlineProductConfigStatus(id, enabled) {
      try {
        const { toggleOnlineProductConfigStatus } = await import('@/api/onlineProduct');
        return await toggleOnlineProductConfigStatus(id, enabled);
      } catch (error) {
        console.error('切换产品配置状态失败:', error);
        throw error;
      }
    },
    async confirmDelete() {
      if (this.currentConfig) {
        try {
          const response = await deleteOnlineProductConfig(this.currentConfig.id);
          this.$message.success('删除成功');
          this.loadData();
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败');
        }
      }
    },


  }
}
</script>

<style lang="less" scoped>
// 通用表格样式，由 UniversalTable 组件提供

// 开关样式优化
.el-switch {
  .el-switch__label {
    font-size: 12px;
    font-weight: 500;
  }
  
  &.is-loading {
    .el-switch__core {
      opacity: 0.7;
    }
  }
}

// 行业分类显示样式
.industry-display {
  .industry-content {
    display: flex;
    flex-wrap: wrap;
    gap: 4px 6px;
    line-height: 1.4;
    max-height: none;
    overflow: visible;
    transition: all 0.3s ease;
    
    &.collapsed {
      max-height: calc(1.4em * 2 + 4px); // 2行的高度 + 间距
      overflow: hidden;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 30px;
        height: 1.4em;
        background: linear-gradient(to right, transparent, #fff 70%);
        pointer-events: none;
      }
    }
  }
  
  .industry-tag {
    display: inline-block;
    padding: 2px 6px;
    background-color: #f0f2f5;
    color: #333;
    font-size: 12px;
    border-radius: 3px;
    white-space: nowrap;
    border: 1px solid #e1e6eb;
    
    &:hover {
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #0050b3;
    }
  }
  
  .industry-actions {
    margin-top: 4px;
    
    .toggle-btn {
      padding: 0;
      font-size: 12px;
      color: #409eff;
      
      &:hover {
        color: #66b1ff;
      }
      
      i {
        margin-left: 2px;
        font-size: 10px;
      }
    }
  }
  
  .no-data {
    color: #bbb;
    font-style: italic;
    font-size: 12px;
  }
}
</style>