<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { 
  getIndustryTree, 
  getOnlineProductConfigDetail,
  createOnlineProductConfig,
  updateOnlineProductConfig
} from '@/api/onlineProduct'
import { getInsureTypeMap } from '@/api/basicConfig'
import { getDicItems } from '@/api/dictionary'

export default {
  name: 'OnlineProductConfigEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        id: '',
        industryCode: [], // 支持多选，初始化为数组
        probability: '',
        impact: '',
        level: '',
        insuranceTypes: [],
        enabled: true,
        description: '',
        createTime: ''
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { 
                prop: 'industryCode', 
                label: '行业分类', 
                type: 'cascader', 
                placeholder: '请选择行业分类，支持多选和任意级别选择，下级全选时上级自动选中', 
                options: [],
                props: {
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  expandTrigger: 'click',
                  emitPath: false,
                  multiple: true,
                  checkStrictly: false
                },
                filterable: true,              // 优化：启用搜索过滤
                collapseTags: true,          // 启动标签折叠，让标签自适应显示
                maxCollapseTags: 10,           // 限制显示数量
                showAllLevels: false,         // 优化：不显示完整路径
                disabled: false,
                // 操作提示
                helpText: '💡 操作提示：支持搜索关键词快速定位，可选择不同级别行业，支持多选且标签自适应显示'
              },
              { 
                prop: 'probability', 
                label: '风险发生概率', 
                type: 'radio', 
                options: [] // 动态加载
              },
              { 
                prop: 'impact', 
                label: '风险影响程度', 
                type: 'radio', 
                options: [] // 动态加载
              },
              { 
                prop: 'level', 
                label: '风险等级', 
                type: 'radio', 
                options: [] // 动态加载
              },
              { 
                prop: 'insuranceTypes', 
                label: '险种类型', 
                type: 'select', 
                placeholder: '请选择险种类型', 
                multiple: true,
                options: []
              },
                             { 
                 prop: 'enabled', 
                 label: '是否启用', 
                 type: 'radio',
                 options: [
                   { label: '启用', value: true },
                   { label: '禁用', value: false }
                 ]
               }
            ],
            [
              { 
                prop: 'description', 
                label: '描述信息', 
                type: 'textarea', 
                placeholder: '请输入描述信息', 
                maxlength: 500, 
                rows: 3, 
                showWordLimit: true, 
                span: 24 
              }
            ]
          ]
        }
      ],
      formRules: {
        industryCode: [
          { 
            required: true, 
            message: '请选择行业分类', 
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!value || (Array.isArray(value) && value.length === 0)) {
                callback(new Error('请选择行业分类'));
              } else {
                callback();
              }
            }
          }
        ],
        probability: [
          { required: true, message: '请选择风险发生概率', trigger: 'change' }
        ],
        impact: [
          { required: true, message: '请选择风险影响程度', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择风险等级', trigger: 'change' }
        ],
        insuranceTypes: [
          { required: true, message: '请选择至少一个险种类型', trigger: 'change' }
        ]
      },
      industryOptions: [],
      insuranceTypeOptions: [],
      riskLevelOptions: [] // 风险字典选项（概率、影响、等级）
    }
  },
  
  computed: {
    // 页面标题
    pageTitle() {
      return this.isView ? '查看产品配置' : (this.isEdit ? '编辑产品配置' : '新增产品配置')
    },
    
    // 页面图标
    pageIcon() {
      return this.isView ? 'el-icon-view' : (this.isEdit ? 'el-icon-edit' : 'el-icon-plus')
    },
    
    // 面包屑导航
    breadcrumbItems() {
      return [
        {
          text: '线上产品配置',
          icon: 'el-icon-goods',
          to: { name: 'onlineProductConfig' }
        },
        {
          text: this.pageTitle,
          icon: this.pageIcon
        }
      ]
    },
    

  },
  
  created() {
    const id = this.$route.params.id
    const mode = this.$route.query.mode
    
    if (id) {
      this.isEdit = mode === 'edit'
      this.isView = mode === 'view'
      this.loadData(id)
    } else {
      // 新增模式 - 确保状态正确
      this.isEdit = false
      this.isView = false
    }
    
    // 加载选项数据
    this.loadOptions()
  },
  
  methods: {
    async loadOptions() {
      try {
        // 并行加载行业树、险种类型选项和风险字典
        const [industryResponse, insuranceResponse, riskLevelResponse] = await Promise.all([
          getIndustryTree(),
          getInsureTypeMap(),
          getDicItems({ dicCode: 'elms.risk.level' })
        ])
        
        // 解析响应数据结构
        console.log('行业树原始响应:', industryResponse);
        
        let industryData = [];
        if (industryResponse && industryResponse.datas && Array.isArray(industryResponse.datas)) {
          industryData = industryResponse.datas;
          console.log('从response.datas获取行业数据');
        } else if (industryResponse && Array.isArray(industryResponse)) {
          industryData = industryResponse;
          console.log('从response直接获取行业数据');
        } else {
          console.warn('行业树数据格式不符合预期:', industryResponse);
          industryData = [];
        }
        
        console.log('解析后的行业数据:', industryData);
        console.log('行业数据长度:', industryData.length);
        
        this.industryOptions = industryData
        console.log('设置industryOptions完成，长度:', this.industryOptions.length);
        
        // 更新表单配置中的行业选项（使用树形结构）
        this.updateFieldOptions('industryCode', industryData)
        console.log('更新表单行业选项完成');
        
        // 解析险种类型响应数据（FormConfigService返回简单的键值对对象）
        console.log('险种类型响应:', insuranceResponse);
        
        let insuranceTypeData = [];
        if (insuranceResponse && typeof insuranceResponse === 'object' && !Array.isArray(insuranceResponse)) {
          // 如果返回的是Map格式，转换为数组：{"2":"团意险","3":"雇主险","4":"团体高医"}
          insuranceTypeData = Object.keys(insuranceResponse).map(key => ({
            label: insuranceResponse[key],
            value: key
          }));
        } else if (insuranceResponse && Array.isArray(insuranceResponse)) {
          // 如果是标准数组格式
          insuranceTypeData = insuranceResponse;
        } else if (insuranceResponse && insuranceResponse.data && Array.isArray(insuranceResponse.data)) {
          // 如果是嵌套数组格式
          insuranceTypeData = insuranceResponse.data;
        } else {
          console.warn('险种类型数据格式不符合预期:', insuranceResponse);
          insuranceTypeData = [];
        }
        
        this.insuranceTypeOptions = insuranceTypeData
        // 更新表单配置中的险种类型选项
        this.updateFieldOptions('insuranceTypes', insuranceTypeData)
        
        // 解析风险字典响应数据：标准字典数组格式
        console.log('风险字典响应:', riskLevelResponse);
        
        let riskLevelData = [];
        if (riskLevelResponse && Array.isArray(riskLevelResponse)) {
          // 标准字典数组格式，按dicOrder排序
          riskLevelData = riskLevelResponse
            .filter(item => item.isShow === 1 && item.isEnable === 1) // 过滤启用且显示的项
            .sort((a, b) => (a.dicOrder || 0) - (b.dicOrder || 0)) // 按排序字段排序
            .map(item => ({
              label: item.dicItemName,
              value: item.dicItemName // 使用名称作为值，保持与现有业务逻辑一致
            }));
        } else if (riskLevelResponse && riskLevelResponse.data && Array.isArray(riskLevelResponse.data)) {
          // 如果是嵌套格式
          riskLevelData = riskLevelResponse.data
            .filter(item => item.isShow === 1 && item.isEnable === 1)
            .sort((a, b) => (a.dicOrder || 0) - (b.dicOrder || 0))
            .map(item => ({
              label: item.dicItemName,
              value: item.dicItemName
            }));
        } else {
          console.warn('风险字典数据格式不符合预期:', riskLevelResponse);
          // 降级处理：使用默认选项
          riskLevelData = [
            { label: '低', value: '低' },
            { label: '中', value: '中' },
            { label: '高', value: '高' }
          ];
        }
        
        this.riskLevelOptions = riskLevelData;
        // 更新表单配置中的风险选项
        this.updateFieldOptions('probability', riskLevelData);
        this.updateFieldOptions('impact', riskLevelData);
        this.updateFieldOptions('level', riskLevelData);
        
        // 选项加载完成后，如果表单中已有数据，重新处理级联选择器回显
        console.log('所有选项加载完成，检查是否需要处理回显');
        console.log('当前表单industryCode:', this.form.industryCode);
        console.log('当前表单industryCode长度:', this.form.industryCode ? this.form.industryCode.length : 0);
        
        this.$nextTick(() => {
          if (this.form.industryCode && this.form.industryCode.length > 0) {
            console.log('选项加载完成，重新处理级联选择器回显');
            const originalCode = [...this.form.industryCode];
            const optimizedCode = this.optimizeIndustryCodeForCascader(this.form.industryCode);
            
            // 使用Vue的$set确保响应式更新
            this.$set(this.form, 'industryCode', optimizedCode);
            
            console.log('回显处理完成，原始值:', originalCode, '处理后值:', optimizedCode);
            
            // 强制更新组件
            this.$forceUpdate();
          } else {
            console.log('表单中没有industryCode数据，无需处理回显');
          }
        });
        
      } catch (error) {
        console.error('加载选项数据失败:', error)
        this.$message.error('加载选项数据失败')
      }
    },
    
    async loadData(id) {
      this.loading = true
      try {
        const response = await getOnlineProductConfigDetail(id)
        console.log('后端返回的原始数据:', response);
        
        // 直接使用响应数据
        this.form = { 
          ...response,
          enabled: response.enabled === true || response.enabled === 1,
          createTime: response.createTime || new Date().toLocaleString(),
          // 确保industryCode是数组格式，并处理级联选择的数据结构
          industryCode: this.normalizeIndustryCode(response.industryCode)
        }
        
        console.log('表单数据设置完成:', this.form);
        console.log('当前行业选项数量:', this.industryOptions.length);
        
        // 如果已加载行业选项，立即处理回显
        if (this.industryOptions.length > 0 && this.form.industryCode && this.form.industryCode.length > 0) {
          console.log('行业选项已加载，立即处理回显');
          this.form.industryName = this.getIndustryName(this.form.industryCode)
          // 优化级联选择器的回显数据
          const optimizedCode = this.optimizeIndustryCodeForCascader(this.form.industryCode);
          this.$set(this.form, 'industryCode', optimizedCode);
          console.log('立即处理回显完成，最终industryCode:', optimizedCode);
        } else if (this.form.industryCode && this.form.industryCode.length > 0) {
          // 如果行业选项还未加载，延迟处理
          console.log('行业选项未加载完成，等待加载...');
          this.$nextTick(() => {
            setTimeout(() => {
              if (this.industryOptions.length > 0) {
                console.log('延迟处理开始，行业选项数量:', this.industryOptions.length);
                this.form.industryName = this.getIndustryName(this.form.industryCode);
                const optimizedCode = this.optimizeIndustryCodeForCascader(this.form.industryCode);
                this.$set(this.form, 'industryCode', optimizedCode);
                console.log('延迟处理级联选择器回显完成，最终industryCode:', optimizedCode);
                
                // 强制更新组件
                this.$forceUpdate();
              } else {
                console.log('延迟处理后，行业选项仍未加载完成');
              }
            }, 100);
          });
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    async handleSave() {
      try {
        await this.$refs.universalForm.validate()
        this.loading = true
        
        // 处理提交的数据，确保行业代码格式正确
        const submitData = {
          ...this.form,
          industryCode: this.processCascaderData(this.form.industryCode)
        }
        
        let response
        
        if (this.isEdit) {
          // 更新模式
          response = await updateOnlineProductConfig(submitData)
        } else {
          // 新增模式
          response = await createOnlineProductConfig(submitData)
        }
        
        this.$message.success(this.isEdit ? '更新成功' : '创建成功')
        this.handleBack()
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    handleBack() {
      this.$router.push({ name: 'onlineProductConfig' })
    },
    
    handleBreadcrumbClick(item) {
      if (item.to) {
        this.$router.push(item.to)
      }
    },
    
    // 快速清空行业选择
    clearIndustrySelection() {
      this.form.industryCode = []
      this.$message.success('已清空行业选择')
    },
    

    
    findIndustryOption(options, value) {
      for (const option of options) {
        if (String(option.value) === String(value)) {
          return option
        }
        if (option.children && option.children.length > 0) {
          const found = this.findIndustryOption(option.children, value)
          if (found) return found
        }
      }
      return null
    },
    
    // 查找行业代码的完整路径
    findIndustryPath(options, targetCode, currentPath = []) {
      console.log('查找路径 - 目标代码:', targetCode, '当前路径:', currentPath, '选项数量:', options.length);
      
      for (const option of options) {
        const newPath = [...currentPath, option.value];
        console.log('检查选项:', option.value, '新路径:', newPath);
        
        if (String(option.value) === String(targetCode)) {
          console.log('找到匹配项，返回路径:', newPath);
          return newPath;
        }
        
        if (option.children && option.children.length > 0) {
          console.log('递归查找子选项，子选项数量:', option.children.length);
          const foundPath = this.findIndustryPath(option.children, targetCode, newPath);
          if (foundPath) {
            console.log('在子选项中找到路径:', foundPath);
            return foundPath;
          }
        }
      }
      
      console.log('未找到路径，返回null');
      return null;
    },
    
    // 检查代码是否存在于行业选项中
    checkCodeExists(options, targetCode) {
      for (const option of options) {
        if (String(option.value) === String(targetCode)) {
          return true;
        }
        
        if (option.children && option.children.length > 0) {
          if (this.checkCodeExists(option.children, targetCode)) {
            return true;
          }
        }
      }
      return false;
    },
    
    // 查找行业代码对应的节点信息
    findIndustryNode(options, targetCode) {
      for (const option of options) {
        if (String(option.value) === String(targetCode)) {
          return option;
        }
        
        if (option.children && option.children.length > 0) {
          const found = this.findIndustryNode(option.children, targetCode);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    
    // 获取节点的所有子级代码（递归）
    getAllChildren(node) {
      const children = [];
      
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          children.push(child.value);
          // 递归获取子级的子级
          if (child.children && child.children.length > 0) {
            children.push(...this.getAllChildren(child));
          }
        }
      }
      
      return children;
    },
    
    // 标准化行业代码数据格式
    normalizeIndustryCode(industryCode) {
      if (!industryCode) return []
      
      console.log('原始industryCode数据:', industryCode);
      
      let codes = [];
      
      // 如果已经是数组格式
      if (Array.isArray(industryCode)) {
        codes = industryCode;
      } else if (typeof industryCode === 'string') {
        // 如果是字符串格式，尝试解析
        try {
          // 尝试解析JSON数组
          if (industryCode.startsWith('[')) {
            codes = JSON.parse(industryCode);
          } else {
            // 单个值转为数组
            codes = [industryCode];
          }
        } catch (e) {
          console.warn('解析行业代码失败:', industryCode, e);
          codes = [industryCode];
        }
      } else {
        return [];
      }
      
      console.log('标准化后的codes:', codes);
      
      // 对于级联选择器回显，需要确保代码格式正确
      // 如果是父级代码，级联选择器应该能够正确处理
      return codes;
    },
    
    // 优化级联选择器的回显数据（反向智能化处理）
    optimizeIndustryCodeForCascader(codes) {
      if (!codes || !Array.isArray(codes) || codes.length === 0) {
        console.log('输入codes为空，直接返回空数组');
        return [];
      }
      
      console.log('优化级联选择器回显，输入codes:', codes);
      console.log('当前行业选项数量:', this.industryOptions.length);
      console.log('行业选项结构:', this.industryOptions);
      
      // 反向智能化处理：将父级代码展开为所有子级代码
      const expandedCodes = [];
      
      for (const code of codes) {
        
        // 查找该代码在行业选项中的信息
        const nodeInfo = this.findIndustryNode(this.industryOptions, code);
        
        if (nodeInfo) {
          console.log('找到节点信息:', nodeInfo);
          
          if (nodeInfo.children && nodeInfo.children.length > 0) {
            // 如果是父级节点，展开所有子级
            const allChildren = this.getAllChildren(nodeInfo);
            expandedCodes.push(...allChildren);
          } else {
            // 如果是叶子节点，直接添加
            expandedCodes.push(code);
          }
        } else {
          console.log('代码不存在于行业选项中，跳过:', code);
        }
      }
      
      // 去重
      const uniqueCodes = [...new Set(expandedCodes)];
      console.log('级联选择器回显优化结果（去重后）:', uniqueCodes);
      return uniqueCodes;
    },
    
    // 处理级联选择的数据结构
    processCascaderData(selectedValues) {
      if (!Array.isArray(selectedValues) || selectedValues.length === 0) {
        return selectedValues
      }
      
      console.log('原始选中值:', selectedValues)
      
      // 由于 emitPath: false，选中的值就是节点值，直接使用
      const nodeValues = selectedValues;
      
      console.log('节点值:', nodeValues)
      
      // 智能优化：检测完全选中的情况并向上合并
      const optimizedValues = this.optimizeSelectedValues(nodeValues)
      
      console.log('优化后的值:', optimizedValues)
      
      return optimizedValues
    },
    
    // 智能优化选中的值
    optimizeSelectedValues(selectedValues) {
      if (!this.industryOptions || this.industryOptions.length === 0) {
        return selectedValues
      }
      
      // 创建一个包含所有节点的扁平映射
      const allNodesMap = this.createNodesMap(this.industryOptions)
      
      // 从最深层级开始向上优化
      let optimized = [...selectedValues]
      
      // 多次迭代直到没有更多优化
      let hasOptimization = true
      while (hasOptimization) {
        hasOptimization = false
        const newOptimized = this.optimizeOneLevel(optimized, allNodesMap)
        if (newOptimized.length !== optimized.length || 
            !newOptimized.every(v => optimized.includes(v))) {
          optimized = newOptimized
          hasOptimization = true
        }
      }
      
      return optimized
    },
    
    // 创建节点映射
    createNodesMap(nodes) {
      const map = new Map()
      
      const traverse = (nodeList, parent = null) => {
        if (!nodeList) return
        
        nodeList.forEach(node => {
          map.set(node.value, {
            ...node,
            parent: parent,
            children: node.children || []
          })
          
          if (node.children && node.children.length > 0) {
            traverse(node.children, node.value)
          }
        })
      }
      
      traverse(nodes)
      return map
    },
    
    // 优化一个层级
    optimizeOneLevel(selectedValues, allNodesMap) {
      const result = []
      const processed = new Set()
      
      // 按父级分组
      const groupsByParent = new Map()
      
      selectedValues.forEach(value => {
        if (processed.has(value)) return
        
        const node = allNodesMap.get(value)
        if (!node) {
          result.push(value)
          processed.add(value)
          return
        }
        
        const parentValue = node.parent
        if (!parentValue) {
          // 没有父级，直接添加
          result.push(value)
          processed.add(value)
        } else {
          // 有父级，按父级分组
          if (!groupsByParent.has(parentValue)) {
            groupsByParent.set(parentValue, [])
          }
          groupsByParent.get(parentValue).push(value)
        }
      })
      
      // 检查每个父级下是否完全选中
      groupsByParent.forEach((childValues, parentValue) => {
        const parentNode = allNodesMap.get(parentValue)
        if (!parentNode) {
          // 父级节点不存在，保持原样
          childValues.forEach(v => {
            if (!processed.has(v)) {
              result.push(v)
              processed.add(v)
            }
          })
          return
        }
        
        const allChildValues = parentNode.children.map(child => child.value)
        
        // 检查是否选中了所有子级
        const selectedChildValues = childValues.filter(v => allChildValues.includes(v))
        
        if (selectedChildValues.length === allChildValues.length && allChildValues.length > 0) {
          // 完全选中，使用父级
          if (!processed.has(parentValue)) {
            result.push(parentValue)
            processed.add(parentValue)
          }
          // 标记所有子级为已处理
          childValues.forEach(v => processed.add(v))
        } else {
          // 部分选中，保持子级
          childValues.forEach(v => {
            if (!processed.has(v)) {
              result.push(v)
              processed.add(v)
            }
          })
        }
      })
      
      return result
    },
    
    // 通用的字段选项更新方法
    updateFieldOptions(fieldProp, options) {
      for (const group of this.formGroups) {
        for (const row of group.fields) {
          const field = row.find(item => item.prop === fieldProp)
          if (field) {
            field.options = options
            return
          }
        }
      }
    },
    
    // 获取行业名称（用于显示）
    getIndustryName(codes) {
      // 支持单个代码或代码数组
      if (!codes) return '';
      
      const codeArray = Array.isArray(codes) ? codes : [codes];
      const flatOptions = this.flattenIndustryTree(this.industryOptions);
      
      const names = codeArray.map(code => {
        const item = flatOptions.find(i => i.value === code);
        return item ? (item.fullName || item.label) : code;
      });
      
      return names.join('，');
    },
    
    // 扁平化行业树结构
    flattenIndustryTree(tree) {
      const result = [];
      const flatten = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return;
        nodes.forEach(node => {
          result.push({
            label: node.label,
            value: node.value,
            fullName: node.fullName,
            fullPath: node.fullPath,
            level: node.level
          });
          if (node.children && Array.isArray(node.children) && node.children.length > 0) {
            flatten(node.children);
          }
        });
      };
      flatten(tree || []);
      return result;
    }
  }
}
</script>

<style lang="less" scoped>
// 页面样式由EditPageContainer和UniversalForm组件提供
</style> 