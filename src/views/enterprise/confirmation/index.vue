<template>
  <div class="enterprise-confirmation-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="企业确权管理"
      subtitle="管理企业确权信息，处理重复企业记录"
      title-icon="el-icon-s-check"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="220"
      :search-label-width="'100px'"
      :show-add-button="false"
      empty-title="暂无企业确权数据"
      empty-description="暂无符合条件的企业确权信息"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >

    </UniversalTable>
  </div>
</template>

<script>
import { getConfirmationList } from '@/api/enterprise/confirmation'
import UniversalTable from '@/components/layouts/UniversalTable.vue'

export default {
  name: 'EnterpriseConfirmation',
  components: {
    UniversalTable
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        param: {
          enterpriseName: '',
          processStatus: null
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '企业名称',
          name: 'enterpriseName',
          type: 'input',
          placeholder: '请输入企业名称'
        },
        {
          label: '处理状态',
          name: 'processStatus',
          type: 'select',
          placeholder: '请选择处理状态',
          options: [
            { label: '未处理', value: 0 },
            { label: '已处理', value: 1 },
            { label: '已超时', value: 2 }
          ]
        }
      ],

      // 表格列配置
      tableColumns: [
        {
          prop: 'enterpriseName',
          label: '企业名称',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'creditCode',
          label: '社会统一信用代码',
          width: 180,
          align: 'center'
        },
        {
          prop: 'city',
          label: '所在地',
          width: 180,
          align: 'center'
        },
        {
          prop: 'staffScale',
          label: '人员规模',
          width: 130,
          align: 'center'
        },
        {
          prop: 'annualIncome',
          label: '营业收入',
          width: 130,
          align: 'center'
        },
        {
          prop: 'processStatusDesc',
          label: '处理状态',
          width: 150,
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '入库时间',
          width: 160,
          align: 'center'
        }
      ],

      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        }
      ]
    }
  },

  created() {
    this.loadData()
  },

  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            ...this.searchForm.param
          }
        }

        const response = await getConfirmationList(params)
        console.log('response:', response.list)
        if (response && response.list) {
          this.tableData = response.list || []
          this.pagination.total = response.total || 0
        }
      } catch (error) {
        console.error('加载企业确权数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch(searchData) {
      this.searchForm.param = { ...searchData.param }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm.param = {
        enterpriseName: '',
        processStatus: null
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },

    // 操作按钮点击
    handleAction({ action, row }) {
      if (action === 'view') {
        this.handleView(row)
      }
    },

    // 查看详情
    handleView(row) {
      this.$router.push({
        name: 'EnterpriseConfirmationDetail',
        params: { id: row.id }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.enterprise-confirmation-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

// 操作按钮样式
/deep/ .action-buttons {
  .view-btn {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;

    &:hover {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
    }
  }
}
</style>
