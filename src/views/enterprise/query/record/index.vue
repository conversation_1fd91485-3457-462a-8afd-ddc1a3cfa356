<template>
  <div class="enterprise-query-record-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="企业创建记录"
      subtitle="记录所有企业创建操作的详细信息，包括所属行业和城市信息"
      title-icon="el-icon-plus"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchForm"
      :pagination-data="pagination"
      :total="pagination.total"
      :search-label-width="'100px'"
      :show-add-button="false"
      empty-title="暂无创建记录"
      empty-description="暂无符合条件的企业创建记录"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
    </UniversalTable>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable'
import { getEnterpriseCreateRecordList } from '@/api/enterprise/create/record'

export default {
  name: 'EnterpriseCreateRecord',
  components: {
    UniversalTable
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        param: {
          agentName: '',
          enterpriseName: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },

      // 搜索表单配置
      searchFormConfig: [
        {
          label: '查询人',
          name: 'agentName',
          type: 'input',
          placeholder: '请输入查询人姓名'
        },
        {
          label: '企业名称',
          name: 'enterpriseName',
          type: 'input',
          placeholder: '请输入企业名称'
        }
      ],

      // 表格列配置
      tableColumns: [
        {
          prop: 'agentName',
          label: '代理人姓名',
          width: 120,
          align: 'center'
        },
        {
          prop: 'agentCode',
          label: '代理人编码',
          width: 120,
          align: 'center'
        },
        {
          prop: 'inputEnterpriseName',
          label: '输入企业名称',
          minWidth: 180,
          showOverflowTooltip: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 160,
          align: 'center',
          formatter: (row, column, cellValue) => {
            if (cellValue) {
              return new Date(cellValue).toLocaleString()
            }
            return '-'
          }
        },
        {
          prop: 'hasData',
          label: '是否有数据',
          width: 100,
          align: 'center',
          formatter: (row, column, cellValue) => {
            return cellValue ? '是' : '否'
          }
        },
        {
          prop: 'isVerify',
          label: '是否验真',
          width: 100,
          align: 'center',
          formatter: (row, column, cellValue) => {
            return cellValue ? '是' : '否'
          }
        },
        {
          prop: 'inputData.categoryName',
          label: '所属行业',
          width: 120,
          align: 'center',
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) => {
            return row.inputData?.categoryName || '-'
          }
        },
        {
          prop: 'inputData.city',
          label: '城市',
          width: 100,
          align: 'center',
          formatter: (row, column, cellValue) => {
            return row.inputData?.city || '-'
          }
        },
        {
          prop: 'isBlocked',
          label: '是否被阻止',
          width: 100,
          align: 'center',
          formatter: (row, column, cellValue) => {
            return cellValue ? '是' : '否'
          }
        }
      ],

      // 操作按钮配置
      tableActions: [
        {
          label: '查看',
          type: 'primary',
          size: 'small',
          key: 'view'
        }
      ]
    }
  },

  mounted() {
    this.loadData()
  },

  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            ...this.searchForm.param
          }
        }

        const response = await getEnterpriseCreateRecordList(params)
        if (response && response.list) {
          this.tableData = response.list || []
          this.pagination.total = response.total || 0
        }
      } catch (error) {
        console.error('加载企业创建记录失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch(searchData) {
      this.searchForm.param = { ...searchData.param }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm.param = {
        agentName: '',
        enterpriseName: ''
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },

    // 操作按钮点击
    handleAction(payload) {
      const { action, row } = payload
      if (action === 'view') {
        this.handleView(row)
      }
    },

    // 查看详情
    handleView(row) {
      this.$message.info('查看功能暂未实现')
      console.log('查看记录:', row)
    }
  }
}
</script>

<style lang="less" scoped>
.enterprise-query-record-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}
</style>
