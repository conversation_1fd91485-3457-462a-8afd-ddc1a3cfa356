<template>
  <InfoPageLayout
    title="企业创建记录详情"
    subtitle="查看企业创建记录的详细信息，包括顾问输入信息和验真返回记录"
    icon="el-icon-view"
    :breadcrumbItems="breadcrumbItems"
    @back="handleBack"
  >
    <div v-loading="loading">
      <!-- 基本信息卡片 -->
      <InfoCard title="基本信息" icon="el-icon-info" class="basic-info-card">
        <el-row :gutter="20">
          <el-col :span="8">
            <InfoItem label="代理人姓名" :value="recordData.agentName || '-'" />
          </el-col>
          <el-col :span="8">
            <InfoItem label="代理人编码" :value="recordData.agentCode || '-'" />
          </el-col>
          <el-col :span="8">
                      <InfoItem label="创建时间" :value="recordData.createTime || '-'" />
                    </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;">

          <el-col :span="8">
            <InfoItem label="是否有数据">
              <el-tag :type="recordData.hasData ? 'success' : 'info'" size="small">
                {{ recordData.hasData ? '是' : '否' }}
              </el-tag>
            </InfoItem>
          </el-col>
          <el-col :span="8">
            <InfoItem label="是否验真">
              <el-tag :type="recordData.isVerify ? 'success' : 'info'" size="small">
                {{ recordData.isVerify ? '是' : '否' }}
              </el-tag>
            </InfoItem>
          </el-col>
          <el-col :span="8">
                      <InfoItem label="是否被阻止">
                        <el-tag :type="recordData.isBlocked ? 'danger' : 'success'" size="small">
                          {{ recordData.isBlocked ? '是' : '否' }}
                        </el-tag>
                      </InfoItem>
                    </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;">

          <el-col :span="16" v-if="recordData.blockMsg">
            <InfoItem label="阻止原因" :value="recordData.blockMsg || '-'" />
          </el-col>
        </el-row>
      </InfoCard>

      <!-- 顾问输入信息卡片 -->
      <InfoCard title="顾问输入信息" icon="el-icon-edit" class="input-info-card">
        <div>
          <el-row :gutter="20">
            <el-col :span="8">
            <InfoItem label="输入企业名称" :value="recordData.inputEnterpriseName || '-'" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="社会统一信用代码" :value="getSafeValue(inputData, 'creditCode')" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="所属行业" :value="getSafeValue(inputData, 'categoryName')" />
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 10px;" >
            <el-col :span="8">
              <InfoItem label="企业规模" :value="getSafeValue(inputData, 'enterpriseScale')" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="所在城市" :value="getSafeValue(inputData, 'city')" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="人员规模" :value="getSafeValue(inputData, 'staffScale')" />
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 10px;">
            <el-col :span="8">
              <InfoItem label="企业年收入" :value="getSafeValue(inputData, 'annualIncome')" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="企业联系人" :value="getSafeValue(inputData, 'enterpriseContacter')" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="联系人电话" :value="getSafeValue(inputData, 'contacterPhone')" />
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 10px;">
            <el-col :span="8">
              <InfoItem label="备注信息" :value="getSafeValue(inputData, 'remark')" />
            </el-col>
          </el-row>
        </div>
      </InfoCard>

      <!-- 验真返回记录卡片 -->
      <InfoCard title="验真返回记录" icon="el-icon-document" class="third-party-info-card">
        <div v-if="thirdPartyData && Object.keys(thirdPartyData).length > 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <InfoItem label="企业名称" :value="thirdPartyData.name" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="社会统一信用代码" :value="thirdPartyData.creditCode" />
            </el-col>
            <el-col :span="8">
              <InfoItem label="企业所属行业" :value="thirdPartyData.categoryName" />
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 10px;">
            <el-col :span="8">
              <InfoItem label="企业所在地" :value="thirdPartyData.city" />
            </el-col>
            <el-col :span="8" v-if="thirdPartyData.latestBusinessIncome">
              <InfoItem label="企业年收入" :value="thirdPartyData.latestBusinessIncome" />
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 10px;">
            <el-col :span="8" v-if="thirdPartyData.staffNumRange">
              <InfoItem label="企业人员规模" :value="thirdPartyData.staffNumRange" />
            </el-col>
          </el-row>
        </div>
        <div v-else class="no-data">
          <el-empty description="暂无验真返回记录" :image-size="80"></el-empty>
        </div>
      </InfoCard>
    </div>
  </InfoPageLayout>
</template>

<script>
import InfoPageLayout from '@/components/layouts/InfoPageLayout'
import InfoCard from '@/components/layouts/InfoCard'
import InfoItem from '@/components/layouts/InfoItem'
import { getEnterpriseCreateRecordDetail } from '@/api/enterprise/create/record'

export default {
  name: 'EnterpriseCreateRecordDetail',
  components: {
    InfoPageLayout,
    InfoCard,
    InfoItem
  },
  data() {
    return {
      loading: false,
      recordData: {},
      breadcrumbItems: [
        { text: '企业创建记录', icon: 'el-icon-plus', to: { name: 'EnterpriseCreateRecord' } },
        { text: '记录详情', icon: 'el-icon-view' }
      ]
    }
  },
  computed: {
    // 输入数据
    inputData() {
      return this.recordData.inputData || {}
    },
    // 第三方数据
    thirdPartyData() {
      if (typeof this.recordData.thirdPartyData === 'string') {
        try {
          return JSON.parse(this.recordData.thirdPartyData)
        } catch (e) {
          return {}
        }
      }
      return this.recordData.thirdPartyData || {}
    },
    // 其他输入数据（除了已显示的字段）
    otherInputDataChunks() {
      const excludeKeys = ['categoryName', 'city', 'enterpriseType', 'contactPerson', 'contactPhone']
      const otherData = Object.keys(this.inputData)
        .filter(key => !excludeKeys.includes(key) && this.inputData[key])
        .map(key => ({
          key,
          label: this.getFieldLabel(key),
          value: this.inputData[key]
        }))

      // 按3个一组分块
      const chunks = []
      for (let i = 0; i < otherData.length; i += 3) {
        chunks.push(otherData.slice(i, i + 3))
      }
      return chunks
    },
    // 其他第三方数据（除了已显示的字段）
    otherThirdPartyDataChunks() {
      const excludeKeys = ['name', 'creditCode', 'minCategoryCode', 'districtCode', 'city', 'latestBusinessIncome', 'staffNumRange']
      const otherData = Object.keys(this.thirdPartyData)
        .filter(key => !excludeKeys.includes(key) && this.thirdPartyData[key])
        .map(key => ({
          key,
          label: this.getFieldLabel(key),
          value: this.thirdPartyData[key]
        }))

      // 按3个一组分块
      const chunks = []
      for (let i = 0; i < otherData.length; i += 3) {
        chunks.push(otherData.slice(i, i + 3))
      }
      return chunks
    }
  },
  created() {
    const { id } = this.$route.params
    if (id) {
      this.loadRecordDetail(id)
    } else {
      this.$message.error('记录ID不能为空')
      this.$router.back()
    }
  },
  methods: {
    // 加载记录详情
    async loadRecordDetail(id) {
      this.loading = true
      try {
        const response = await getEnterpriseCreateRecordDetail(id)
        if (response) {
          this.recordData = response
        }
      } catch (error) {
        console.error('加载记录详情失败:', error)
        this.$message.error('加载记录详情失败')
      } finally {
        this.loading = false
      }
    },
    // 返回上一页
    handleBack() {
      this.$router.back()
    },
    // 获取字段标签
    getFieldLabel(key) {
      const labelMap = {
        // 常见字段映射
        'companyName': '公司名称',
        'businessScope': '经营范围',
        'registeredCapital': '注册资本',
        'establishDate': '成立日期',
        'legalPerson': '法定代表人',
        'address': '地址',
        'phone': '电话',
        'email': '邮箱',
        'website': '网站',
        'industry': '行业',
        'scale': '规模',
        'status': '状态'
      }
      return labelMap[key] || key
    }
  }
}
</script>

<style lang="less" scoped>
.basic-info-card,
.input-info-card,
.third-party-info-card {
  margin-bottom: 20px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}
</style>
