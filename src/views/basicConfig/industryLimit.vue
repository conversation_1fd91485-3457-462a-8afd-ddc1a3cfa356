<template>
  <div class="industry-limit-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="行业限制管理"
      subtitle="配置行业限制规则，动态添加企业字段，设置匹配规则，指定可用服务流程"
      title-icon="el-icon-s-custom"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormConfig"
      :search-params="searchParamsWithParam"
      :pagination-data="pagination"
      :total="pagination.total"
      :action-column-width="240"
      :search-label-width="'100px'"
      add-button-text="新增规则"
      empty-title="暂无行业限制规则"
      empty-description="点击上方新增规则按钮开始创建"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 字段规则列插槽 -->
      <template #conditions="{ row }">
        <span v-if="row.conditions && row.conditions.length">
          <el-tag
            v-for="(field, idx) in row.conditions"
            :key="idx"
            type="info"
            size="small"
            style="margin: 2px;"
          >
            {{ field.description || (getFieldName(field.field || field.name) + (field.matchType === 'eq' ? '等于' : field.matchType === 'contains' ? '包含' : field.matchType === 'range' ? '区间' : field.matchType) + field.value) }}
          </el-tag>
        </span>
        <span v-else style="color:#bbb;">未配置</span>
      </template>
      <!-- 执行规则列插槽 -->
      <template #actionList="{ row }">
        <span v-if="row.actionList && row.actionList.length">
          <el-tag
            v-for="(action, idx) in row.actionList"
            :key="idx"
            type="info"
            size="small"
            style="margin: 2px;"
          >
            {{ action.type === 'applyRule' ? '执行' : '不执行' }}
            <template v-if="action.serviceIds && action.serviceIds.length">
              ：
              <span>
                {{ action.serviceIds.map(id => getServiceName(id)).join('，') }}
              </span>
            </template>
          </el-tag>
        </span>
        <span v-else style="color:#bbb;">未配置</span>
      </template>

      <template #createTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          {{ row.createTime }}
        </div>
      </template>
    </UniversalTable>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该规则？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script>
import { getBusinessProcessList } from '@/api/businessProcess/index.js'
import { getIndustryLimitRules, getFieldOptions, deleteIndustryLimitRule } from '@/api/industryLimit'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'IndustryLimit',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  computed: {
    // 添加一个计算属性，将searchForm转换为带param的结构
    searchParamsWithParam() {
      return {
        param: { ...this.searchForm }
      }
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        name: '',
        description: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'name',
          label: '规则名称',
          minWidth: 200,
          align: 'center'
        },
        {
          prop: 'description',
          label: '描述',
          minWidth: 300,
          align: 'center'
        },
        {
          prop: 'conditions',
          label: '规则',
          minWidth: 250,
          align: 'center'
        },
        {
          prop: 'actionList',
          label: '执行',
          minWidth: 250,
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center'
        }
      ],
      // 操作按钮配置
      tableActions: [
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'view-btn',
          size: 'mini'
        },
        {
          key: 'config',
          label: '配置',
          icon: 'el-icon-setting',
          class: 'config-btn',
          size: 'mini'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn',
          size: 'mini'
        }
      ],
      // 搜索表单配置
      searchFormConfig: [
        {
          label: '规则名称',
          name: 'name',
          type: 'input',
          placeholder: '请输入规则名称'
        },
        {
          label: '描述',
          name: 'description',
          type: 'input',
          placeholder: '请输入描述'
        }
      ],
      editDialogVisible: false,
      editForm: {
        id: '',
        name: '',
        description: '',
        fields: [],
        processId: ''
      },
      templateFields: [],
      processList: [],
      fieldOptions: [],
      serviceOptions: [],
      currentRow: null
    }
  },
  mounted() {
    // 先加载字段选项和业务流程列表，然后再加载数据
    Promise.all([
      this.loadFieldOptions(),
      this.loadProcessList()
    ]).then(() => {
      // 所有选项加载完成后再加载数据
      this.loadData()
    })
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const pageRequest = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: this.searchForm
        }
        const response = await getIndustryLimitRules(pageRequest)
        console.log('API响应:', response) // 调试日志
        
        // 直接使用响应数据，没有data包装层
        let pageData = response
        
        this.tableData = (pageData.list || []).map(rule => {
          return {
            id: rule.id,
            name: rule.name,
            description: rule.description,
            status: rule.status,
            statusName: rule.statusName,
            conditions: (rule.conditions || []).map(cond => {
              // 使用fieldOptions查找字段名称
              const fieldOption = this.fieldOptions.find(f => f.code === cond.field);
              const fieldName = fieldOption ? fieldOption.name : cond.field;

              return {
                field: cond.field,
                code: cond.field,
                name: fieldName,
                matchType: cond.operator,
                value: cond.value,
                description: `${fieldName} ${cond.operator === 'eq' ? '等于' : cond.operator === 'contains' ? '包含' : '区间'} ${cond.value}`
              }
            }),
            actionList: (rule.actionList || []).map(action => ({
              ...action,
              description: `${action.type === 'applyRule' ? '执行' : '不执行'}`
            })),
            createTime: rule.createTime || '2024-06-01 10:00:00',
            updateTime: rule.updateTime,
            createUser: rule.createUser,
            updateUser: rule.updateUser
          }
        })
        this.pagination.total = pageData.total || 0
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 处理操作按钮点击
    handleAction({ action, row }) {
      this.currentRow = row

      switch (action) {
        case 'view':
          this.handleView(row)
          break
        case 'config':
          this.handleConfig(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },

    handleSearch(searchData) {
      this.searchForm = { ...searchData }
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleReset() {
      this.searchForm = {
        name: '',
        description: ''
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    },
    async loadTemplateFields() {
      try {
        // 直接使用getFieldOptions API获取字段选项，而不是从数据模板中获取
        const res = await getFieldOptions();
        if (res && Array.isArray(res)) {
          this.templateFields = res;
        } else {
          console.error('加载字段选项失败: 数据格式不正确', res);
          this.templateFields = [];
        }
      } catch (error) {
        console.error('加载字段选项失败:', error);
        this.templateFields = [];
      }
    },
    async loadProcessList() {
      try {
        const res = await getBusinessProcessList()
        // 解析业务流程接口返回的数据结构
        if (res && res.datas && Array.isArray(res.datas)) {
          this.processList = res.datas
          // 同时设置服务选项，避免重复调用接口
          this.serviceOptions = res.datas
        } else if (res && Array.isArray(res)) {
          this.processList = res
          // 同时设置服务选项，避免重复调用接口
          this.serviceOptions = res
        } else {
          this.processList = []
          this.serviceOptions = []
        }
      } catch (error) {
        console.error('加载业务流程失败:', error)
        this.processList = []
        this.serviceOptions = []
      }
    },
    async loadFieldOptions() {
      try {
        const res = await getFieldOptions();
        console.log('字段选项响应:', res) // 调试日志
        
        // 直接使用响应数据，getFieldOptions返回的是数组格式
        if (res && Array.isArray(res)) {
          this.fieldOptions = res;
        } else {
          console.error('加载字段选项失败: 数据格式不正确', res);
          this.fieldOptions = [];
        }
      } catch (error) {
        console.error('加载字段选项失败:', error);
        this.fieldOptions = [];
      }
    },

    handleAdd() {
      // 跳转到配置页面，不传递id表示新增
      this.$router.push({ name: 'industryLimitConfig' })
    },
    handleDelete(row) {
      this.$refs.confirmDialog.show(`确定要删除规则「${row.name}」吗？`)
    },

    async confirmDelete() {
      try {
        const response = await deleteIndustryLimitRule(this.currentRow.id)
        console.log('删除响应:', response) // 调试日志
        
        // 检查响应结构（直接响应，没有data包装）
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.loadData()
        } else {
          this.$message.error(response.message || '删除失败')
        }
      } catch (error) {
        this.$message.error('删除失败')
        console.error('删除失败:', error)
      }
    },
    handleConfig(row) {
      // 跳转到配置页面，传递规则id
      this.$router.push({ name: 'industryLimitConfig', params: { id: row.id } })
    },
    handleView(row) {
      this.$router.push({
        name: 'industryLimitConfig',
        params: { id: row.id },
        query: { mode: 'view' }
      });
    },
    getFieldName(fieldCode) {
      // 根据字段代码获取字段名称
      if (!fieldCode) return '';
      const fieldOption = this.fieldOptions.find(f => f.code === fieldCode);
      return fieldOption ? fieldOption.name : fieldCode;
    },
    getServiceName(id) {
      const item = this.serviceOptions.find(s => 
        String(s.businessCode || s.id || s.processId) === String(id)
      );
      return item ? (item.businessName || item.name || item.processName) : id;
    },
    
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      if (isNaN(date.getTime())) return dateTimeStr;
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
}
</script>
