<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :loading="loading"
    :is-view="isView"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <!-- 基本信息表单 -->
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="basicFormGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />

    <!-- 风险矩阵配置 -->
    <div class="config-section">
      <DynamicConfigTable
        title="风险矩阵配置"
        subtitle="配置该行业的风险类型、等级和影响程度"
        icon="el-icon-s-grid"
        v-model="form.matrixConfig"
        :default-columns="defaultMatrixColumns"
        :readonly="isView"
        :show-stats="true"
        :show-move-actions="true"
        :validate-on-change="true"
        @change="handleMatrixChange"
        @columns-change="handleMatrixColumnsChange"
      />
    </div>

    <!-- 富文本配置 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <i class="section-icon el-icon-edit-outline"></i>
          <div class="header-content">
            <h3 class="section-title">详细说明配置</h3>
            <p class="section-subtitle">配置该行业风险的详细说明、操作指南和相关文档</p>
          </div>
        </div>
        <div class="header-right" v-if="!isView">
          <el-tooltip content="支持富文本格式，可插入图片、链接等" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </div>
      <div class="section-content">
        <QuillEditor
          v-model="form.richTextContent"
          :readonly="isView"
          :height="300"
          :max-length="5000"
          :show-word-count="true"
          :image-handler="handleImageUpload"
          placeholder="请输入该行业风险的详细说明，包括风险识别要点、评估方法、控制措施等..."
          @change="handleRichTextChange"
        />
      </div>
    </div>

  </EditPageContainer>
</template>
<script>
import { getIndustryRiskConfigByCode, saveIndustryRiskConfig } from '@/api/riskMatrix'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import DynamicConfigTable from '@/components/layouts/DynamicConfigTable.vue'
import QuillEditor from '@/components/layouts/QuillEditor.vue'

export default {
  name: 'IndustryRiskConfig',
  components: { EditPageContainer, UniversalForm, DynamicConfigTable, QuillEditor },
  data() {
    return {
      loading: false,
      isView: false,
      form: {
        id: '', // 记录ID，用于编辑模式下的更新操作
        industryLevel1Code: '',
        industryLevel1Name: '',
        industryLevel2Code: '',
        industryLevel2Name: '',
        matrixDesc: '',
        matrixConfig: {
          columnDefs: [],
          rowData: []
        },
        richTextContent: ''
      },
      // 默认风险矩阵列配置
      defaultMatrixColumns: [
      ],

      basicFormGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'industryLevel1Code', label: '一级行业编码', type: 'input', placeholder: '请输入一级行业编码', disabled: this.isEditMode },
              { prop: 'industryLevel1Name', label: '一级行业名称', type: 'input', placeholder: '请输入一级行业名称', disabled: this.isEditMode }
            ],
            [
              { prop: 'industryLevel2Code', label: '二级行业编码', type: 'input', placeholder: '请输入二级行业编码（如有）', disabled: this.isEditMode },
              { prop: 'industryLevel2Name', label: '二级行业名称', type: 'input', placeholder: '请输入二级行业名称（如有）', disabled: this.isEditMode }
            ],
            [
              { prop: 'matrixDesc', label: '矩阵说明', type: 'textarea', placeholder: '请输入该行业风险矩阵的核心说明...', maxlength: 500, rows: 4, showWordLimit: true, span: 24 }
            ]
          ]
        }
      ],
      formRules: {
        industryLevel1Code: [{ required: true, message: '请输入一级行业编码', trigger: 'blur' }],
        industryLevel1Name: [{ required: true, message: '请输入一级行业名称', trigger: 'blur' }],
        richTextContent: [
          {
            validator: (rule, value, callback) => {
              if (value && value.length > 5000) {
                callback(new Error('详细说明内容不能超过5000个字符'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.isEditMode) {
        return this.form.industryLevel1Name ? `${this.form.industryLevel1Name} - 风险配置` : '编辑行业风险配置'
      } else {
        return '新增行业风险配置'
      }
    },
    pageIcon() {
      return 'el-icon-s-flag'
    },
    isEditMode() {
      // 如果路由中有一级行业编码参数，说明是编辑模式
      return !!this.$route.params.industryLevel1Code
    },
    breadcrumbItems() {
      return [
        { text: '行业风险管理', to: { name: 'industryRisk' }, icon: 'el-icon-s-flag' },
        { text: this.pageTitle, icon: 'el-icon-edit' }
      ]
    }
  },
  created() {
    this.isView = this.$route.query.mode === 'view'
    this.loadIndustryData()
  },
  methods: {
    async loadIndustryData() {
      const industryLevel1Code = this.$route.params.industryLevel1Code

      // 如果没有一级行业编码，说明是新增场景
      if (!industryLevel1Code) {
        this.initializeNewConfig()
        return
      }

      try {
        this.loading = true

        // 调用后端API获取配置数据
        const response = await getIndustryRiskConfigByCode(industryLevel1Code)

        if (response.code === 200 && response.data) {
          const found = response.data

          // 保存记录ID，用于编辑模式下的更新操作
          this.form.id = found.id || ''
          console.log('加载编辑数据：记录ID =', this.form.id)
          this.form.industryLevel1Code = found.industryLevel1Code || ''
          this.form.industryLevel1Name = found.industryLevel1Name || ''
          this.form.industryLevel2Code = found.industryLevel2Code || ''
          this.form.industryLevel2Name = found.industryLevel2Name || ''
          this.form.matrixDesc = found.matrixDesc || ''

          // 加载矩阵配置
          if (found.matrixConfig) {
            this.form.matrixConfig = {
              columnDefs: found.matrixConfig.columnDefs || this.defaultMatrixColumns,
              rowData: found.matrixConfig.rowData || []
            }
          } else if (found.matrix) {
            // 兼容旧数据结构
            this.form.matrixConfig = {
              columnDefs: this.defaultMatrixColumns,
              rowData: found.matrix.map(item => ({
                type: item.type || '',
                level: item.level || '',
                description: item.desc || item.description || '',
                impact: item.impact || '',
                probability: item.probability || ''
              }))
            }
          } else {
            this.form.matrixConfig = {
              columnDefs: this.defaultMatrixColumns,
              rowData: []
            }
          }

          // 加载富文本内容
          this.form.richTextContent = found.richTextContent || ''
        } else {
          // 如果没有找到配置，创建默认配置
          this.$message.warning('未找到行业风险配置数据，将创建新配置')
          this.form.industryName = industryCode || ''
          this.form.industryCode = industryCode || ''
          this.form.matrixDesc = ''
          this.form.matrixConfig = {
            columnDefs: this.defaultMatrixColumns,
            rowData: []
          }
          this.form.richTextContent = ''
        }
      } catch (error) {
        console.error('加载行业风险配置失败:', error)
        this.$message.error('加载配置失败')

        // 设置默认值
        this.form.industryName = industryCode || ''
        this.form.industryCode = industryCode || ''
        this.form.matrixDesc = ''
        this.form.matrixConfig = {
          columnDefs: this.defaultMatrixColumns,
          rowData: []
        }
        this.form.richTextContent = ''
      } finally {
        this.loading = false
      }
    },
    handleBack() {
      this.$router.push({ name: 'industryRisk' })
    },
    handleBreadcrumbClick(item) {
      if (item.to) {
        this.$router.push(item.to)
      }
    },
    async handleSave() {

      try {
        // 验证必填字段
        if (!this.form.industryLevel1Code || !this.form.industryLevel1Code.trim()) {
          this.$message.error('请输入一级行业编码')
          return
        }

        if (!this.form.industryLevel1Name || !this.form.industryLevel1Name.trim()) {
          this.$message.error('请输入一级行业名称')
          return
        }

        // 验证表单
        const valid = await this.$refs.universalForm.validate()
        if (!valid) {
          this.$message.error('请检查表单填写是否正确')
          return
        }

        // 验证富文本内容长度
        if (this.form.richTextContent && this.form.richTextContent.length > 5000) {
          this.$message.error('详细说明内容不能超过5000个字符')
          return
        }

        this.loading = true

        // 判断是新增还是更新
        const isUpdate = this.isEditMode

        // 构建保存数据
        const saveData = {
          industryLevel1Code: this.form.industryLevel1Code ? this.form.industryLevel1Code.trim() : '',
          industryLevel1Name: this.form.industryLevel1Name ? this.form.industryLevel1Name.trim() : '',
          industryLevel2Code: this.form.industryLevel2Code ? this.form.industryLevel2Code.trim() : '',
          industryLevel2Name: this.form.industryLevel2Name ? this.form.industryLevel2Name.trim() : '',
          matrixDesc: this.form.matrixDesc || '',
          riskLevel: this.calculateOverallRiskLevel(),
          status: 'active',
          matrixConfig: this.form.matrixConfig,
          richTextContent: this.form.richTextContent,
          changeSummary: isUpdate ? '更新行业风险配置' : '新增行业风险配置'
        }

        // 如果是编辑模式，添加记录ID
        if (isUpdate && this.form.id) {
          saveData.id = this.form.id
          console.log('编辑模式：包含记录ID', this.form.id)
        } else {
          console.log('新增模式：不包含记录ID')
        }

        // 尝试使用新的保存接口
        let response
        try {
          response = await saveIndustryRiskConfig(saveData)
        } catch (error) {
          // 如果新接口失败，回退到旧接口
          console.warn('新接口保存失败，尝试使用旧接口:', error)
        }

        if (response.code === 200) {
          this.$message.success('保存成功')
          this.handleBack()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    },

    // ==================== 新增事件处理方法 ====================

    /**
     * 处理风险矩阵数据变更
     */
    handleMatrixChange(newValue) {
      console.log('风险矩阵数据变更:', newValue)
      // 可以在这里添加自动保存逻辑
    },

    /**
     * 处理风险矩阵列配置变更
     */
    handleMatrixColumnsChange(newColumns) {
      console.log('风险矩阵列配置变更:', newColumns)
      this.$message.success('风险矩阵列配置已更新')
    },

    /**
     * 处理富文本内容变更
     */
    handleRichTextChange(data) {
      console.log('富文本内容变更:', data)
      // 可以在这里添加自动保存逻辑或内容验证
      if (data.content && data.content.length > 5000) {
        this.$message.warning('内容长度不能超过5000个字符')
      }
    },

    /**
     * 处理图片上传
     */
    handleImageUpload(file, callback) {
      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        this.$message.error('只支持 JPG、PNG、GIF 格式的图片')
        return
      }

      // 验证文件大小（限制为2MB）
      const maxSize = 2 * 1024 * 1024
      if (file.size > maxSize) {
        this.$message.error('图片大小不能超过2MB')
        return
      }

      // 这里应该调用实际的图片上传接口
      // 示例：使用 FormData 上传到服务器
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'risk-config-image')

      // 模拟上传过程
      this.$message.info('正在上传图片...')

      // 实际项目中应该调用真实的上传接口
      // this.$http.post('/api/upload/image', formData).then(response => {
      //   callback(response.data.url)
      //   this.$message.success('图片上传成功')
      // }).catch(error => {
      //   this.$message.error('图片上传失败')
      //   console.error('上传失败:', error)
      // })

      // 临时方案：转换为 base64（仅用于演示，生产环境不推荐）
      const reader = new FileReader()
      reader.onload = (e) => {
        setTimeout(() => {
          callback(e.target.result)
          this.$message.success('图片上传成功')
        }, 1000)
      }
      reader.onerror = () => {
        this.$message.error('图片读取失败')
      }
      reader.readAsDataURL(file)
    },



    /**
     * 初始化新增配置
     */
    initializeNewConfig() {
      console.log('初始化新增配置')

      // 设置默认值
      this.form.id = '' // 新增时清空ID
      this.form.industryLevel1Code = ''
      this.form.industryLevel1Name = ''
      this.form.industryLevel2Code = ''
      this.form.industryLevel2Name = ''
      this.form.matrixDesc = ''
      this.form.matrixConfig = {
        columnDefs: this.defaultMatrixColumns,
        rowData: []
      }
      this.form.richTextContent = ''

      this.loading = false
      this.$message.info('请填写行业信息和风险配置')
    },

    /**
     * 计算整体风险等级
     */
    calculateOverallRiskLevel() {
      if (!this.form.matrixConfig || !this.form.matrixConfig.rowData || this.form.matrixConfig.rowData.length === 0) {
        return 'medium'
      }

      const riskCounts = {
        critical: 0,
        high: 0,
        medium: 0,
        low: 0
      }

      // 统计各风险等级数量
      this.form.matrixConfig.rowData.forEach(row => {
        const level = row.level
        if (riskCounts.hasOwnProperty(level)) {
          riskCounts[level]++
        }
      })

      const total = this.form.matrixConfig.rowData.length

      // 根据风险分布计算整体等级
      if (riskCounts.critical > 0 || riskCounts.high / total > 0.5) {
        return 'high'
      } else if (riskCounts.high > 0 || riskCounts.medium / total > 0.6) {
        return 'medium'
      } else {
        return 'low'
      }
    }
  }
}
</script>
<style lang="less" scoped>
.config-section {
  margin-top: 24px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:first-of-type {
    margin-top: 16px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;

    .header-left {
      display: flex;
      align-items: center;

      .section-icon {
        margin-right: 12px;
        color: #FF8030;
        font-size: 20px;
      }

      .header-content {
        .section-title {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .section-subtitle {
          margin: 0;
          font-size: 13px;
          color: #909399;
        }
      }
    }
  }

  .section-content {
    // 富文本编辑器内容区域样式
    .quill-editor-wrapper {
      border-radius: 6px;
      overflow: hidden;

      ::v-deep .quill-editor-container {
        border-color: #dcdfe6;

        &.is-focus {
          border-color: #FF8030;
          box-shadow: 0 0 0 2px rgba(255, 128, 48, 0.1);
        }
      }

      ::v-deep .quill-toolbar {
        background-color: #fafbfc;
        border-bottom-color: #ebeef5;
      }

      ::v-deep .ql-editor {
        font-size: 14px;
        line-height: 1.6;

        &.ql-blank::before {
          color: #c0c4cc;
          font-style: normal;
        }
      }

      ::v-deep .quill-word-count {
        background-color: #fafbfc;
        border-top-color: #ebeef5;

        .word-count-text {
          color: #909399;
        }
      }
    }
  }

  .header-right {
    .el-icon-question {
      color: #909399;
      font-size: 16px;
      cursor: help;

      &:hover {
        color: #FF8030;
      }
    }
  }
}

// 覆盖UniversalForm的样式，使其与配置区域保持一致
/deep/ .universal-form-container {
  .form-group {
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 16px;

    .group-title {
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid #ebeef5;

      .title-left {
        font-size: 16px;
        font-weight: 600;
        color: #303133;

        i {
          margin-right: 8px;
          color: #FF8030;
          font-size: 18px;
        }
      }
    }
  }
}
</style>
