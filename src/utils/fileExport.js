import axios from "axios";
import { rootPath } from "@/utils/globalParam";
import { Message } from "element-ui";
import { Loading } from 'element-ui';

/**
 * 导出excel文件
 * fileName 文件的名称 请自带xlsx文件后缀
 * url 请求全路径
 * param 参数
 */
let loadingInstance = null;
export default async (fileName, param = {}, url) => {
  loadingInstance = Loading.service({
    fullscreen: true,
    lock: true,
    text: "加载中...",
    target: document.getElementsByTagName("body")[0]
  });
  return axios
    .post(url, param, {
      responseType: "blob",
      baseURL: rootPath,
      timeout: 60000,
      withCredentials: true, // 是否允许带cookie这些
      headers: {
        // "content-Type": "multipart/form-data",
        access_token: sessionStorage.getItem("LoginAccessToken"),
        tenantId: sessionStorage.getItem("tenantId"),
        funcId: sessionStorage.getItem("funcId"),
      }
    })
    .then(res => {
      // console.log(res);
      if (loadingInstance) {
        loadingInstance.close();
      }
      //返回结果为空
      if (!res) {
        Message.error("发生错误！请重新操作");
        return;
      }
      //返回结果不是200，抛出错误提示
      if (res.status != "200") {
        Message.error("请求发生错误，请联系管理员");
        return;
      }
      // console.log(res);
      const content = res.data;
      const blob = new Blob([content]);
      if ("download" in document.createElement("a")) {
        // 非IE下载
        const elink = document.createElement("a");
        elink.download = fileName;
        elink.style.display = "none";
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink);
      } else {
        // IE10+下载
        navigator.msSaveBlob(blob, fileName);
      }
    });
};
