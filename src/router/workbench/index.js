export default [
  {
    path: "/workbenchList",
    name: "workbenchList",
    component: () => import("@/views/workbench/workbenchList"),
    meta: {
    }
  },
  {
    path: "/opportunityDetails",
    name: "opportunityDetails",
    component: () => import("@/views/workbench/opportunityDetails"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/changeManager",
    name: "changeManager",
    component: () => import("@/views/workbench/changeManager"),
    meta: {
      notCache: true
    },
  },
  {
    path: "/opportunityManage",
    name: "opportunityManage",
    component: () => import("@/views/workbench/opportunityManage"),
    meta: {
      notCache: true
    },
  },
]
