export default [
  {
    path: "/inquiryRecordList",
    name: "inquiryRecordList",
    component: () => import("@/views/onlineInquiry/inquiryRecordList"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/inquiryApprovalList",
    name: "inquiryApprovalList",
    component: () => import("@/views/onlineInquiry/inquiryApprovalList"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/inquiryApproval",
    name: "inquiryApproval",
    component: () => import("@/views/onlineInquiry/inquiryApproval"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/inquiryReportList",
    name: "inquiryReportList",
    component: () => import("@/views/onlineInquiry/inquiryReportList"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/inquiryDetail",
    name: "inquiryDetail",
    component: () => import("@/views/onlineInquiry/inquiryDetail"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/inquiryConfig",
    name: "inquiryConfig",
    component: () => import("@/views/onlineInquiry/inquiryConfig"),
    meta: {
      notCache: true
    }
  },
  {
    path: "/inquiryAdd",
    name: "inquiryAdd",
    component: () => import("@/views/onlineInquiry/components/inquiryAdd"),
    meta: {
      notCache: true
    }
  },
]
