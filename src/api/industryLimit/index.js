import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';

/**
 * 行业限制管理 API
 */

// 分页查询行业限制规则列表
export const getIndustryLimitRules = (pageRequest) => {
  return http.Axios.post(rootPath + "/api/industryLimit/page", pageRequest);
};

// 获取行业限制规则详情
export const getIndustryLimitRuleDetail = (id) => {
  return http.Axios.get(rootPath + `/api/industryLimit/${id}`);
};

// 创建行业限制规则
export const createIndustryLimitRule = (data) => {
  return http.Axios.post(rootPath + "/api/industryLimit/add", data);
};

// 更新行业限制规则
export const updateIndustryLimitRule = (data) => {
  return http.Axios.post(rootPath + "/api/industryLimit/update", data);
};

// 删除行业限制规则
export const deleteIndustryLimitRule = (id) => {
  return http.Axios.post(rootPath + `/api/industryLimit/delete/${id}`);
};

// 获取字段选项
export const getFieldOptions = () => {
  return http.Axios.get(rootPath + "/api/industryLimit/fieldOptions");
};

// 检查规则编码是否存在
export const checkCodeExists = (code, id) => {
  const params = new URLSearchParams();
  params.append('code', code);
  if (id) {
    params.append('id', id);
  }
  return http.Axios.get(rootPath + `/api/industryLimit/checkCode?${params.toString()}`);
};

// 获取操作符选项（本地数据，不需要请求后端）
export const getOperatorOptions = () => {
  return Promise.resolve({
    code: 200,
    message: "success",
    data: [
      { value: 'eq', label: '等于' },
      { value: 'contains', label: '包含' },
      { value: 'range', label: '区间' },
      { value: 'gt', label: '大于' },
      { value: 'lt', label: '小于' },
      { value: 'gte', label: '大于等于' },
      { value: 'lte', label: '小于等于' }
    ]
  });
};

// 获取动作类型选项（本地数据，不需要请求后端）
export const getActionTypeOptions = () => {
  return Promise.resolve({
    code: 200,
    message: "success",
    data: [
      { value: 'applyRule', label: '执行规则' },
      { value: 'noAction', label: '不执行' },
      { value: 'applyLimit', label: '应用限额' },
      { value: 'applyService', label: '应用服务' }
    ]
  });
}; 