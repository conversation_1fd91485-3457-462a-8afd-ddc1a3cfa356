// 企业确权管理相关 API
import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';

// 分页查询企业确权列表
export const getConfirmationList = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/confirmation/page", data);

// 获取企业确权详情
export const getConfirmationDetail = (id) =>
  http.Axios.get(rootPath + `/api/enterprise/confirmation/detail/${id}`);

// 保存处理记录
export const saveProcessRecord = (data) =>
  http.Axios.post(rootPath + "/api/enterprise/confirmation/process", data);
