import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';
import {mockDelay,mockServiceProcesses} from "@/mock";

// 获取数据模板分页列表
export const getDataTemplateList = (data) =>
  http.Axios.post(rootPath + "/api/dataTemplate/page", data);

// 删除数据模板
export const deleteDataTemplate = (data) =>
  http.Axios.post(rootPath + "/api/dataTemplate/delete", data);

// 新增数据模板
export const addDataTemplate = (data) =>
  http.Axios.post(rootPath + "/api/dataTemplate/add", data);

// 更新数据模板
export const updateDataTemplate = (data) =>
  http.Axios.post(rootPath + "/api/dataTemplate/update", data);

// 获取数据模板详情
export const getDataTemplateDetail = (id) => {
  return http.Axios.post(rootPath + "/api/dataTemplate/detail", { id });
};

// 获取模板字段列表
export const getTemplateFields = (templateCode) => {
  return http.Axios.post(rootPath + "/api/dataTemplate/fields", { templateCode: templateCode });
};

// 获取模板数据列表
export const getTemplateData = (data) => {
  return http.Axios.post(rootPath + "/api/dataTemplate/data/page", data);
};


// 导入字段列表（仅解析，不保存）
export const importTemplateFields = (templateId, file) => {
  const formData = new FormData();
  formData.append('templateId', templateId);
  formData.append('file', file);
  return http.Axios.post(rootPath + "/api/dataTemplate/fields/import", formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// ==================== 表单配置相关API ====================

// 获取表单配置分页列表
export const getFormConfigList = (data) =>
  http.Axios.post(rootPath + "/api/formConfig/page", data);

// 删除表单配置
export const deleteFormConfig = (data) =>
  http.Axios.post(rootPath + "/api/formConfig/delete", data);

// 新增表单配置
export const addFormConfig = (data) =>
  http.Axios.post(rootPath + "/api/formConfig/add", data);

// 更新表单配置
export const updateFormConfig = (data) =>
  http.Axios.post(rootPath + "/api/formConfig/update", data);

// 获取表单配置详情
export const getFormConfigDetail = (id) => {
  return http.Axios.get(rootPath + "/api/formConfig/get/" + id);
};

// 根据配置编码获取展示字段列表
export const getFormConfigFields = (configCode) => {
  return http.Axios.get(rootPath + "/api/formConfig/fields/" + configCode);
};

// 导出表单字段列表
export const exportFormConfigFields = (configId) => {
  return http.Axios.post(rootPath + "/api/formConfig/fields/export", { id: configId }, {
    responseType: 'blob'
  });
};

// 导入表单字段列表（仅解析，不保存）
export const importFormConfigFields = (configId, file) => {
  const formData = new FormData();
  formData.append('configId', configId);
  formData.append('file', file);
  return http.Axios.post(rootPath + "/api/formConfig/fields/import", formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};


// 获取服务流程列表
export const getServiceProcessList = async (params = {}) => {
  await mockDelay();

  let filteredData = [];
  if (Array.isArray(mockServiceProcesses) && mockServiceProcesses.length > 0) {
    filteredData = [...mockServiceProcesses];
  } else {
    filteredData = [...staticMockServiceProcesses];
  }

  // 根据参数过滤数据
  if (params.name) {
    filteredData = filteredData.filter(item =>
      item.name.toLowerCase().includes(params.name.toLowerCase())
    );
  }

  if (params.category) {
    filteredData = filteredData.filter(item => item.category === params.category);
  }

  // 修改状态筛选逻辑，只有当status是数字类型时才进行筛选
  if (params.status !== undefined && params.status !== '' && params.status !== null) {
    // 将字符串转换为数字
    const statusValue = parseInt(params.status);
    if (!isNaN(statusValue)) {
      filteredData = filteredData.filter(item => item.status === statusValue);
    }
  }

  // 分页处理
  const pageNum = params.pageNum || 1;
  const pageSize = params.pageSize || 10;
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = filteredData.slice(startIndex, endIndex);

  return {
    code: 200,
    message: "success",
    data: {
      list: paginatedData,
      total: filteredData.length,
      pageNum,
      pageSize,
      totalPages: Math.ceil(filteredData.length / pageSize)
    }
  };
};

// 获取服务流程详情
export const getServiceProcessDetail = async (id) => {
  await mockDelay();

  const process = mockServiceProcesses.find(item => item.id === parseInt(id));

  if (!process) {
    return {
      code: 404,
      message: "服务流程不存在",
      data: null
    };
  }

  return {
    code: 200,
    message: "success",
    data: process
  };
};

// 创建服务流程
export const createServiceProcess = async (data) => {
  await mockDelay();

  const newProcess = {
    id: Math.max(...mockServiceProcesses.map(p => p.id)) + 1,
    ...data,
    createTime: new Date().toLocaleString(),
    updateTime: new Date().toLocaleString(),
    createUser: 'admin',
    updateUser: 'admin'
  };

  mockServiceProcesses.push(newProcess);

  return {
    code: 200,
    message: "创建成功",
    data: newProcess
  };
};

// 更新服务流程
export const updateServiceProcess = async (id, data) => {
  await mockDelay();

  const index = mockServiceProcesses.findIndex(item => item.id === parseInt(id));

  if (index === -1) {
    return {
      code: 404,
      message: "服务流程不存在",
      data: null
    };
  }

  mockServiceProcesses[index] = {
    ...mockServiceProcesses[index],
    ...data,
    updateTime: new Date().toLocaleString(),
    updateUser: 'admin'
  };

  return {
    code: 200,
    message: "更新成功",
    data: mockServiceProcesses[index]
  };
};

// 删除服务流程
export const deleteServiceProcess = async (id) => {
  await mockDelay();

  const index = mockServiceProcesses.findIndex(item => item.id === parseInt(id));

  if (index === -1) {
    return {
      code: 404,
      message: "服务流程不存在",
      data: null
    };
  }

  mockServiceProcesses.splice(index, 1);

  return {
    code: 200,
    message: "删除成功",
    data: null
  };
};

// 获取模板分类列表
export const getTemplateCategoryList = async () => {
  await mockDelay();

  // 确保mockDataTemplates是一个数组
  if (!Array.isArray(mockDataTemplates)) {
    return {
      code: 200,
      message: "success",
      data: []
    };
  }

  const categories = [...new Set(mockDataTemplates.map(t => t.category))];

  return {
    code: 200,
    message: "success",
    data: categories.map(name => ({ name, value: name }))
  };
};

// 获取流程分类列表
export const getProcessCategoryList = async () => {
  await mockDelay();

  // Use the static backup if mockServiceProcesses is undefined
  let serviceProcesses = [];
  if (Array.isArray(mockServiceProcesses) && mockServiceProcesses.length > 0) {
    serviceProcesses = mockServiceProcesses;
  } else {
    serviceProcesses = staticMockServiceProcesses;
  }

  const categories = [...new Set(serviceProcesses.map(p => p.category))];

  return {
    code: 200,
    message: "success",
    data: categories.map(name => ({ name, value: name }))
  };
};

// 获取字段类型列表
export const getFieldTypeList = async () => {
  await mockDelay();

  return {
    code: 200,
    message: "success",
    data: [
      { name: '文本', value: 'text' },
      { name: '数字', value: 'number' },
      { name: '日期', value: 'date' },
      { name: '选择', value: 'select' },
      { name: '多行文本', value: 'textarea' }
    ]
  };
};

// 保存模板字段
export const saveTemplateFields = async (templateId, fields) => {
  await mockDelay();

  const template = mockDataTemplates.find(item => item.id === parseInt(templateId));

  if (!template) {
    return {
      code: 404,
      message: "数据模板不存在",
      data: null
    };
  }

  template.fields = fields;
  template.updateTime = new Date().toLocaleString();
  template.updateUser = 'admin';

  return {
    code: 200,
    message: "保存成功",
    data: template
  };
};

// 更新模板字段
export const updateTemplateFields = async (templateId, fields) => {
  return saveTemplateFields(templateId, fields);
};

// 获取流程步骤列表
export const getProcessSteps = async (processId) => {
  await mockDelay();

  const process = mockServiceProcesses.find(item => item.id === parseInt(processId));

  if (!process) {
    return {
      code: 404,
      message: "服务流程不存在",
      data: null
    };
  }

  return {
    code: 200,
    message: "success",
    data: process.steps || []
  };
};

// 保存流程步骤
export const saveProcessSteps = async (processId, steps) => {
  await mockDelay();

  const process = mockServiceProcesses.find(item => item.id === parseInt(processId));

  if (!process) {
    return {
      code: 404,
      message: "服务流程不存在",
      data: null
    };
  }

  process.steps = steps;
  process.updateTime = new Date().toLocaleString();
  process.updateUser = 'admin';

  return {
    code: 200,
    message: "保存成功",
    data: process
  };
};

// 更新流程步骤
export const updateProcessSteps = async (processId, steps) => {
  return saveProcessSteps(processId, steps);
};

// ==================== 表单配置服务相关API ====================

// 获取险种类型字典
export const getInsureTypeMap = () => {
  return http.Axios.get(rootPath + "/api/onlineProduct/getInsureTypeMap");
};

// 获取流程配置详情
export const getProcessConfig = async (processId) => {
  await mockDelay();

  const process = mockServiceProcesses.find(item => item.id === parseInt(processId));

  if (!process) {
    return {
      code: 404,
      message: "服务流程不存在",
      data: null
    };
  }

  return {
    code: 200,
    message: "success",
    data: {
      processInfo: process,
      nodeList: process.steps?.map((step, index) => ({
        nodeId: step.nodeId,
        nodeName: step.name,
        nodeType: step.code,
        type: step.code,
        logImg: getStepIcon(step.code),
        positionLeft: `${100 + index * 200}px`,
        positionTop: '200px',
        fixed: false,
        config: {
          description: step.description,
          estimatedTime: step.estimatedTime,
          unit: step.unit,
          required: step.required
        }
      })) || [],
      connectionList: []
    }
  };
};

// 保存流程配置
export const saveProcessConfig = async (processId, configData) => {
  await mockDelay();

  const process = mockServiceProcesses.find(item => item.id === parseInt(processId));

  if (!process) {
    return {
      code: 404,
      message: "服务流程不存在",
      data: null
    };
  }

  // 更新流程信息
  if (configData.processInfo) {
    Object.assign(process, configData.processInfo);
  }

  // 更新步骤信息
  if (configData.nodeList) {
    process.steps = configData.nodeList.map(node => ({
      id: parseInt(node.nodeId) || Date.now(),
      nodeId: node.nodeId,
      name: node.nodeName,
      code: node.nodeType,
      description: node.config?.description || '',
      order: parseInt(node.nodeId) || 1,
      required: node.config?.required || true,
      estimatedTime: node.config?.estimatedTime || 1,
      unit: node.config?.unit || '天'
    }));
  }

  process.updateTime = new Date().toLocaleString();
  process.updateUser = 'admin';

  return {
    code: 200,
    message: "保存成功",
    data: process
  };
};

// 保存字段映射关系
export const saveFieldMapping = async (templateId, apiConfig, mappings) => {
  await mockDelay();

  const template = mockDataTemplates.find(item => item.id === parseInt(templateId));

  if (!template) {
    return {
      code: 404,
      message: "数据模板不存在",
      data: null
    };
  }

  // 保存API配置和映射关系
  template.apiConfig = apiConfig;
  template.mappings = mappings;
  template.updateTime = new Date().toLocaleString();
  template.updateUser = 'admin';

  return {
    code: 200,
    message: "保存成功",
    data: {
      templateId,
      apiConfig,
      mappings
    }
  };
};

// 辅助函数：获取步骤图标
function getStepIcon(stepCode) {
  const iconMap = {
    'requirement_collection': 'el-icon-document',
    'solution_design': 'el-icon-edit',
    'solution_confirmation': 'el-icon-s-check',
    'contract_signing': 'el-icon-document-copy',
    'service_implementation': 'el-icon-s-operation',
    'risk_assessment': 'el-icon-warning-outline',
    'kyc': 'el-icon-search',
    'approval': 'el-icon-s-opportunity'
  };
  return iconMap[stepCode] || 'el-icon-s-unfold';
}
