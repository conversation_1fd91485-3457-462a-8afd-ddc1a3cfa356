package com.kbao.kbcelms.client;

import com.kbao.commons.web.Result;
import com.kbao.kbcelms.user.vo.ElmsUserFeignRequestVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025/8/14 15:07
 */
@FeignClient(name = "kbc-elms-web")
public interface ElmsUserClientWebService {

    @PostMapping("/api/nonuser/user/deleteByUserId")
    Result<String> syncDeleteElmsUser(@RequestBody ElmsUserFeignRequestVO request);

}
