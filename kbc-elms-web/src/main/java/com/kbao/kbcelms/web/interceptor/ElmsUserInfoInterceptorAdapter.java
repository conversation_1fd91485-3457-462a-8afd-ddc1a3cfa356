package com.kbao.kbcelms.web.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.user.vo.UserInfoVO;
import com.kbao.kbcelms.util.ElmsUserUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 * @date 2025/8/8 10:39
 */
@Slf4j
@Component
public class ElmsUserInfoInterceptorAdapter implements HandlerInterceptor {

    @Autowired
    @Lazy
    private UserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        ElmsUserUtils.clearUserInfo();
        UserInfoVO userInfo = userService.getCurrentUserInfo();
        if (EmptyUtils.isNotEmpty(userInfo)) {
            ElmsUserUtils.setUserInfo(userInfo);
            return true;
        }
        // 判断当前用户在云服是否为超管
        if(SysLoginUtils.isAdmin()) {
            return true;
        }

//        response.setHeader("Content-Type", "application/json;charset=UTF-8");
//        PrintWriter writer = response.getWriter();
//        writer.write(JSONObject.toJSONString(Result.failed("未配置企客用户")));
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
