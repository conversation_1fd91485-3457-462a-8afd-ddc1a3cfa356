package com.kbao.kbcelms.web.interceptor;

import com.kbao.commons.util.TokenUtil;
import com.kbao.kbcbsc.interceptor.UserAuthInterceptorAdapter;
import com.kbao.kbcbsc.user.dto.UserDTO;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.common.config.ElmsContext;import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class WebUserAuthInterceptor extends UserAuthInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        UserDTO userDTO;
        if(!super.preHandle(request, response, handler)
                || (userDTO = BscUserUtils.getUser().getUser()) == null){
            return false;
        }
        String accessToken = TokenUtil.getToken(request);
        ElmsContext.UserInfo user = ElmsContext.UserInfo.builder().userId(userDTO.getUserId()).userName(userDTO.getUserName())
                .tenantId(userDTO.getTenantId()).token(accessToken).nickName(userDTO.getNickName()).build();
        ElmsContext.setUser(user);
        ElmsContext.setTenantId(userDTO.getTenantId());
        return true;
    }
}
