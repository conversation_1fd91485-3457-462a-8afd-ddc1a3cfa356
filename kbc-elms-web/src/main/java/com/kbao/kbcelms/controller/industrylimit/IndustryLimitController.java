package com.kbao.kbcelms.controller.industrylimit;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.industrylimit.bean.IndustryLimitFieldOptionVO;
import com.kbao.kbcelms.industrylimit.bean.IndustryLimitQuery;
import com.kbao.kbcelms.industrylimit.bean.StatusRequest;
import com.kbao.kbcelms.industrylimit.service.IndustryLimitService;
import com.kbao.kbcelms.industrylimit.vo.IndustryLimitVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行业限制管理控制器
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequestMapping("/api/industryLimit")
@Api(tags = "行业限制管理")
public class IndustryLimitController extends BaseController {

    @Autowired
    private IndustryLimitService industryLimitService;

    /**
     * 分页查询行业限制规则
     */
    @PostMapping("/page")
    @LogAnnotation(module = "行业限制管理", recordRequestParam = true, action = "查询", desc = "分页查询行业限制规则列表")
    @ApiOperation(value = "分页查询行业限制规则", notes = "分页查询行业限制规则列表")
    public Result<PageInfo<IndustryLimitVO>> getPage(
            @ApiParam(value = "分页查询参数", required = true)
            @RequestBody @Validated PageRequest<IndustryLimitQuery> request) {
        try {
            PageInfo<IndustryLimitVO> result = industryLimitService.getPage(request);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("分页查询行业限制规则失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询行业限制规则详情
     */
    @GetMapping("/{id}")
    @LogAnnotation(module = "行业限制管理", recordRequestParam = true, action = "查询", desc = "查询行业限制规则详情")
    @ApiOperation(value = "查询行业限制规则详情", notes = "根据ID查询行业限制规则详情")
    public Result<IndustryLimitVO> getById(
            @ApiParam(value = "规则ID", required = true)
            @PathVariable Long id) {
        try {
            IndustryLimitVO result = industryLimitService.getById(id);
            if (result == null) {
                return Result.failed("规则不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("查询行业限制规则详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增行业限制规则
     */
    @PostMapping("/add")
    @LogAnnotation(module = "行业限制管理", recordRequestParam = true, action = "新增", desc = "新增行业限制规则")
    @ApiOperation(value = "新增行业限制规则", notes = "新增行业限制规则")
    public Result<IndustryLimitVO> add(
            @ApiParam(value = "规则信息", required = true)
            @RequestBody @Validated IndustryLimitVO vo) {
        try {
            IndustryLimitVO result = industryLimitService.add(vo);
            return Result.succeed(result, "新增成功");
        } catch (Exception e) {
            logger.error("新增行业限制规则失败", e);
            return Result.failed("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新行业限制规则
     */
    @PostMapping("/update")
    @LogAnnotation(module = "行业限制管理", recordRequestParam = true, action = "更新", desc = "更新行业限制规则")
    @ApiOperation(value = "更新行业限制规则", notes = "更新行业限制规则")
    public Result<IndustryLimitVO> update(
            @ApiParam(value = "规则信息", required = true)
            @RequestBody @Validated IndustryLimitVO vo) {
        try {
            if (vo.getId() == null) {
                return Result.failed("规则ID不能为空");
            }
            IndustryLimitVO result = industryLimitService.update(vo);
            return Result.succeed(result, "更新成功");
        } catch (Exception e) {
            logger.error("更新行业限制规则失败", e);
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除行业限制规则
     */
    @PostMapping("/delete/{id}")
    @LogAnnotation(module = "行业限制管理", recordRequestParam = true, action = "删除", desc = "删除行业限制规则")
    @ApiOperation(value = "删除行业限制规则", notes = "删除行业限制规则")
    public Result<String> delete(
            @ApiParam(value = "规则ID", required = true)
            @PathVariable Long id) {
        try {
            industryLimitService.delete(id);
            return Result.succeed("删除成功");
        } catch (Exception e) {
            logger.error("删除行业限制规则失败", e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 检查规则编码是否存在
     */
    @GetMapping("/checkCode")
    @LogAnnotation(module = "行业限制管理", recordRequestParam = true, action = "查询", desc = "检查规则编码是否存在")
    @ApiOperation(value = "检查规则编码是否存在", notes = "检查规则编码是否存在")
    public Result<Boolean> checkCodeExists(
            @ApiParam(value = "规则编码", required = true)
            @RequestParam String code,
            @ApiParam(value = "排除的ID")
            @RequestParam(required = false) Long id) {
        try {
            boolean exists = industryLimitService.checkCodeExists(code, id);
            return Result.succeed(exists, "查询成功");
        } catch (Exception e) {
            logger.error("检查规则编码失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 切换行业限制规则状态
     */
    @PostMapping("/toggleStatus/{id}")
    @LogAnnotation(module = "行业限制管理", recordRequestParam = true, action = "更新", desc = "切换行业限制规则状态")
    @ApiOperation(value = "切换行业限制规则状态", notes = "启用或禁用行业限制规则")
    public Result<String> toggleStatus(
            @ApiParam(value = "规则ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "状态信息", required = true)
            @RequestBody StatusRequest request) {
        try {
            industryLimitService.toggleStatus(id, request.getStatus());
            String statusText = request.getStatus() == 1 ? "启用" : "禁用";
            return Result.succeed("规则状态已切换为" + statusText);
        } catch (Exception e) {
            logger.error("切换规则状态失败", e);
            return Result.failed("状态切换失败：" + e.getMessage());
        }
    }

    /**
     * 获取字段选项列表
     */
    @GetMapping("/fieldOptions")
    @LogAnnotation(module = "行业限制管理", recordRequestParam = true, action = "查询", desc = "获取字段选项列表")
    @ApiOperation(value = "获取字段选项列表", notes = "获取可用的字段选项列表")
    public Result<List<IndustryLimitFieldOptionVO>> getFieldOptions() {
        try {
            List<IndustryLimitFieldOptionVO> result = industryLimitService.getFieldOptions();
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("获取字段选项列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
}
