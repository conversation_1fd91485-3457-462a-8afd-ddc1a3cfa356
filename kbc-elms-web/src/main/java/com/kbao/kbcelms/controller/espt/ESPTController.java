package com.kbao.kbcelms.controller.espt;

import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.espt.dto.ESPTProductQuery;
import com.kbao.kbcelms.espt.service.ESPTService;
import com.kbao.kbcespt.mongo.product.vo.EsProductInfoVO;
import com.kbao.kbcespt.mongo.product.vo.ProductInfoSelectVO;
import com.kbao.kbcespt.mongo.product.vo.ServiceItemVO;
import com.kbao.tool.util.EmptyUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = {"企业生态服务"})
@RestController
@RequestMapping("/api/espt/")
public class ESPTController {

    @Autowired
    private ESPTService esptService;

    /**
     * 获取产品列表
     * @return
     */
    @ApiOperation("生态产品列表")
    @PostMapping("/product/list")
    public Result<ProductInfoSelectVO> getProduct() {
        return Result.succeed(esptService.getProduct(), "查询成功");
    }

    /**
     * 获取产品详情
     * @return
     */
    @ApiOperation("获取产品详情")
    @PostMapping("/product/get")
    public Result<EsProductInfoVO> getProductDetail(@RequestBody ESPTProductQuery query) {
        if(EmptyUtils.isEmpty(query.productCode) && EmptyUtils.isEmpty(query.productId)){
            throw new BusinessException("产品编码和产品ID不能为空");
        }
        return Result.succeed(esptService.getProductDetail(query), "查询成功");
    }

    /**
     * 获取服务项目详情
     * @return
     */
    @ApiOperation("获取服务项目详情")
    @PostMapping("/product/item/get")
    public Result<ServiceItemVO> getServiceItem(@RequestBody ESPTProductQuery query) {
        if(EmptyUtils.isEmpty(query.serviceItemId)){
            throw new BusinessException("服务项目ID不能为空");
        }
        return Result.succeed(esptService.getServiceItem(query.serviceItemId), "查询成功");
    }
}