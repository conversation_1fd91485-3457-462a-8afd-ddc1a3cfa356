package com.kbao.kbcelms.controller.onlineproduct;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.onlineproduct.bean.OnlineProductConfigQuery;
import com.kbao.kbcelms.onlineproduct.service.OnlineProductConfigService;
import com.kbao.kbcelms.onlineproduct.vo.OnlineProductConfigVO;
import com.kbao.kbcelms.onlineproduct.vo.ToggleStatusRequest;
import com.kbao.kbcelms.formConfig.service.FormConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 线上产品配置管理控制器
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/onlineProduct")
@Api(tags = "线上产品配置管理")
public class OnlineProductConfigController extends BaseController {

    @Autowired
    private OnlineProductConfigService onlineProductConfigService;
    
    @Autowired
    private FormConfigService formConfigService;

    /**
     * 分页查询线上产品配置
     */
    @PostMapping("/page")
    @LogAnnotation(module = "线上产品配置管理", recordRequestParam = true, action = "查询", desc = "分页查询线上产品配置列表")
    @ApiOperation(value = "分页查询线上产品配置", notes = "分页查询线上产品配置列表")
    public Result<PageInfo<OnlineProductConfigVO>> getPage(
            @ApiParam(value = "分页查询参数", required = true)
            @RequestBody @Validated PageRequest<OnlineProductConfigQuery> request) {
        try {
            PageInfo<OnlineProductConfigVO> result = onlineProductConfigService.getPage(request);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("分页查询线上产品配置失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询线上产品配置详情
     */
    @GetMapping("/{id}")
    @LogAnnotation(module = "线上产品配置管理", recordRequestParam = true, action = "查询", desc = "查询线上产品配置详情")
    @ApiOperation(value = "查询线上产品配置详情", notes = "根据ID查询线上产品配置详情")
    public Result<OnlineProductConfigVO> getById(
            @ApiParam(value = "配置ID", required = true)
            @PathVariable Long id) {
        try {
            OnlineProductConfigVO result = onlineProductConfigService.getById(id);
            if (result == null) {
                return Result.failed("配置不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("查询线上产品配置详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增线上产品配置
     */
    @PostMapping("/add")
    @LogAnnotation(module = "线上产品配置管理", recordRequestParam = true, action = "新增", desc = "新增线上产品配置")
    @ApiOperation(value = "新增线上产品配置", notes = "新增线上产品配置")
    public Result<OnlineProductConfigVO> add(
            @ApiParam(value = "配置信息", required = true)
            @RequestBody @Validated OnlineProductConfigVO vo) {
        try {
            OnlineProductConfigVO result = onlineProductConfigService.add(vo);
            return Result.succeed(result, "新增成功");
        } catch (Exception e) {
            logger.error("新增线上产品配置失败", e);
            return Result.failed("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新线上产品配置
     */
    @PostMapping("/update")
    @LogAnnotation(module = "线上产品配置管理", recordRequestParam = true, action = "更新", desc = "更新线上产品配置")
    @ApiOperation(value = "更新线上产品配置", notes = "更新线上产品配置")
    public Result<OnlineProductConfigVO> update(
            @ApiParam(value = "配置信息", required = true)
            @RequestBody @Validated OnlineProductConfigVO vo) {
        try {
            if (vo.getId() == null) {
                return Result.failed("配置ID不能为空");
            }
            OnlineProductConfigVO result = onlineProductConfigService.update(vo);
            return Result.succeed(result, "更新成功");
        } catch (Exception e) {
            logger.error("更新线上产品配置失败", e);
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除线上产品配置
     */
    @PostMapping("/delete/{id}")
    @LogAnnotation(module = "线上产品配置管理", recordRequestParam = true, action = "删除", desc = "删除线上产品配置")
    @ApiOperation(value = "删除线上产品配置", notes = "删除线上产品配置")
    public Result<String> delete(
            @ApiParam(value = "配置ID", required = true)
            @PathVariable Long id) {
        try {
            onlineProductConfigService.delete(id);
            return Result.succeed("删除成功");
        } catch (Exception e) {
            logger.error("删除线上产品配置失败", e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 启用/禁用线上产品配置状态
     */
    @PostMapping("/toggleStatus")
    @LogAnnotation(module = "线上产品配置管理", recordRequestParam = true, action = "状态切换", desc = "启用/禁用线上产品配置")
    @ApiOperation(value = "启用/禁用线上产品配置状态", notes = "快速切换线上产品配置的启用/禁用状态")
    public Result<String> toggleStatus(
            @ApiParam(value = "状态切换请求", required = true)
            @RequestBody @Validated ToggleStatusRequest request) {
        try {
            onlineProductConfigService.toggleStatus(request.getId(), request.getEnabled());
            String actionText = request.getEnabled() ? "启用" : "禁用";
            return Result.succeed(actionText + "成功");
        } catch (Exception e) {
            logger.error("切换线上产品配置状态失败", e);
            return Result.failed("状态切换失败：" + e.getMessage());
        }
    }

    /**
     * 检查配置是否存在
     */
    @GetMapping("/checkConfig")
    @LogAnnotation(module = "线上产品配置管理", recordRequestParam = true, action = "查询", desc = "检查配置是否存在")
    @ApiOperation(value = "检查配置是否存在", notes = "检查配置是否存在")
    public Result<Boolean> checkConfigExists(
            @ApiParam(value = "行业代码", required = true) @RequestParam List<String> industryCode,
            @ApiParam(value = "风险概率", required = true) @RequestParam String probability,
            @ApiParam(value = "风险影响", required = true) @RequestParam String impact,
            @ApiParam(value = "风险等级", required = true) @RequestParam String level,
            @ApiParam(value = "排除的ID") @RequestParam(required = false) Long id) {
        try {
            boolean exists = onlineProductConfigService.checkConfigExists(industryCode, probability, impact, level, id);
            return Result.succeed(exists, "查询成功");
        } catch (Exception e) {
            logger.error("检查配置失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取险种类型映射字典（通过FormConfigService）
     */
    @GetMapping("/getInsureTypeMap")
    @LogAnnotation(module = "线上产品配置管理", recordRequestParam = true, action = "查询", desc = "获取险种类型映射字典")
    @ApiOperation(value = "获取险种类型映射字典", notes = "通过FormConfigService获取险种类型ID与名称的映射字典")
    public Result<Map<Integer, String>> getInsureTypeMap() {
        try {
            Map<Integer, String> result = formConfigService.getInsureTypeMap();
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("获取险种类型映射字典失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

}
