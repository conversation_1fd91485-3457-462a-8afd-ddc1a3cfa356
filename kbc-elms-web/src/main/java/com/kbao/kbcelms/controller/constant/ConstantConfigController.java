package com.kbao.kbcelms.controller.constant;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.constant.dto.ConstantConfigDTO;
import com.kbao.kbcelms.constant.dto.ConstantConfigQueryDTO;
import com.kbao.kbcelms.constant.service.ConstantConfigService;
import com.kbao.kbcelms.constant.vo.ConstantConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 常数配置管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/constantConfig")
@Validated
@Api(tags = "常数配置管理")
public class ConstantConfigController extends BaseController {

    @Autowired
    private ConstantConfigService constantConfigService;

    @PostMapping("/list")
    @ApiOperation("分页查询常数配置列表")
    @LogAnnotation(module = "常数配置管理", recordRequestParam = true, action = "查询", desc = "分页查询常数配置列表")
    public Result<PageInfo<ConstantConfigVO>> getConstantConfigList(
            @RequestBody PageRequest<ConstantConfigQueryDTO> pageRequest
    ) {
        PageInfo<ConstantConfigVO> result = constantConfigService.getConstantConfigList(pageRequest);
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getAllConstant")
    @ApiOperation("查询已启用常数")
    @LogAnnotation(module = "常数配置管理", recordRequestParam = true, action = "查询", desc = "查询已启用常数")
    public Result<List<ConstantConfigVO>> getAllConstant() {
        List<ConstantConfigVO> allConstant = constantConfigService.getAllConstant();
        return Result.succeed(allConstant, ResultStatusEnum.SUCCESS.getMsg());
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询常数配置详情")
    @LogAnnotation(module = "常数配置管理", recordRequestParam = true, action = "查询", desc = "查询常数配置详情")
    public Result<ConstantConfigVO> getConstantConfigDetail(
            @ApiParam("常数配置ID") @PathVariable @NotNull Long id
    ) {
        ConstantConfigVO constantConfig = constantConfigService.getConstantConfigDetail(id);
        return Result.succeed(constantConfig, "查询成功");
    }

    @PostMapping
    @ApiOperation("创建常数配置")
    @LogAnnotation(module = "常数配置管理", recordRequestParam = true, action = "新增", desc = "创建常数配置")
    public Result<Long> createConstantConfig(
            @ApiParam("常数配置数据") @RequestBody @Valid ConstantConfigDTO constantConfigDTO
    ) {
        Long id = constantConfigService.createConstantConfig(constantConfigDTO);
        return Result.succeed(id, "创建成功");
    }

    @PutMapping("/{id}")
    @ApiOperation("更新常数配置")
    @LogAnnotation(module = "常数配置管理", recordRequestParam = true, action = "修改", desc = "更新常数配置")
    public Result<Boolean> updateConstantConfig(
            @ApiParam("常数配置ID") @PathVariable @NotNull Long id,
            @ApiParam("常数配置数据") @RequestBody @Valid ConstantConfigDTO constantConfigDTO
    ) {
        Boolean result = constantConfigService.updateConstantConfig(id, constantConfigDTO);
        return Result.succeed(result, "更新成功");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除常数配置")
    @LogAnnotation(module = "常数配置管理", recordRequestParam = true, action = "删除", desc = "删除常数配置")
    public Result<Boolean> deleteConstantConfig(
            @ApiParam("常数配置ID") @PathVariable @NotNull Long id
    ) {
        Boolean result = constantConfigService.deleteConstantConfig(id);
        return Result.succeed(result, "删除成功");
    }

    @PutMapping("/{id}/status")
    @ApiOperation("更新常数配置状态")
    @LogAnnotation(module = "常数配置管理", recordRequestParam = true, action = "修改", desc = "更新常数配置状态")
    public Result<Boolean> updateConstantConfigStatus(
            @ApiParam("常数配置ID") @PathVariable @NotNull Long id,
            @ApiParam("状态") @RequestParam @NotNull Integer status
    ) {
        Boolean result = constantConfigService.updateConstantConfigStatus(id, status);
        return Result.succeed(result, "状态更新成功");
    }


















}
