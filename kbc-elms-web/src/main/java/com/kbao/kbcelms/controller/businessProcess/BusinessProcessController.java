package com.kbao.kbcelms.controller.businessProcess;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.businessProcess.bean.BusinessProcessRequest;
import com.kbao.kbcelms.businessProcess.bean.BusinessProcessVO;
import com.kbao.kbcelms.businessProcess.service.BusinessProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 业务流程配置控制器
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("/api/businessProcess")
@Api(tags = "业务流程配置管理")
@Validated
public class BusinessProcessController extends BaseController {

    @Autowired
    private BusinessProcessService businessProcessService;

    /**
     * 获取业务流程配置列表
     */
    @ApiOperation(value = "获取业务流程配置列表", notes = "获取所有业务的流程配置")
    @PostMapping("/list")
    @LogAnnotation(module = "业务流程配置", recordRequestParam = true, action = "查询", desc = "获取业务流程配置列表")
    public Result<List<BusinessProcessVO>> getBusinessProcessList() {
        try {
            List<BusinessProcessVO> result = businessProcessService.getAllBusinessProcess();
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取业务流程配置列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 保存业务流程配置
     */
    @ApiOperation(value = "保存业务流程配置", notes = "批量保存业务流程配置")
    @PostMapping("/save")
    @LogAnnotation(module = "业务流程配置", recordRequestParam = true, action = "保存", desc = "保存业务流程配置")
    public Result<String> saveBusinessProcess(@RequestBody @Valid List<BusinessProcessRequest> requestList) {
        try {
            businessProcessService.saveBatchBusinessProcess(requestList);
            return Result.succeed("保存成功");
        } catch (Exception e) {
            log.error("保存业务流程配置失败", e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }

    /**
     * 根据业务编码获取流程配置
     */
    @ApiOperation(value = "根据业务编码获取流程配置", notes = "获取指定业务的流程配置列表")
    @PostMapping("/getByBusinessCode")
    @LogAnnotation(module = "业务流程配置", recordRequestParam = true, action = "查询", desc = "根据业务编码获取流程配置")
    public Result<List<String>> getProcessConfigsByBusinessCode(@RequestParam String businessCode) {
        try {
            List<String> result = businessProcessService.getProcessConfigsByBusinessCode(businessCode);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("根据业务编码获取流程配置失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 检查流程是否启用
     */
    @ApiOperation(value = "检查流程是否启用", notes = "检查指定业务是否启用了某个流程")
    @PostMapping("/checkProcessEnabled")
    @LogAnnotation(module = "业务流程配置", recordRequestParam = true, action = "查询", desc = "检查流程是否启用")
    public Result<Boolean> checkProcessEnabled(@RequestParam String businessCode, @RequestParam String processCode) {
        try {
            boolean result = businessProcessService.isProcessEnabled(businessCode, processCode);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("检查流程是否启用失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
}
