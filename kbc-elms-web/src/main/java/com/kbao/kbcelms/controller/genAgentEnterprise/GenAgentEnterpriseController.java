package com.kbao.kbcelms.controller.genAgentEnterprise;

import com.github.pagehelper.PageInfo;import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;import com.kbao.commons.web.Result;import com.kbao.kbcbsc.log.annotation.LogAnnotation;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListResVo;import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.opportunity.entity.Opportunity;import com.kbao.kbcelms.opportunity.service.OpportunityService;import com.kbao.kbcelms.opportunity.vo.EmployeeLeadVO;import io.swagger.annotations.ApiOperation;import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;import org.springframework.web.bind.annotation.RequestBody;import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 顾问企业表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@RestController
@RequestMapping("/api/agentEnterprise")
public class GenAgentEnterpriseController extends BaseController {

    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;
    @Autowired
    private OpportunityService opportunityService;


    @LogAnnotation(module = "企业客户管理", recordRequestParam = true, action = "查询", desc = "分页查询企业客户统计")
    @ApiOperation(value = "分页查询企业客户统计", notes = "根据姓名、工号查询企业客户统计信息")
    @PostMapping("/stat")
    public Result<PageInfo<EmployeeLeadVO>> statAgentEnterprise(@RequestBody PageRequest<Opportunity> page) {
        PageInfo<EmployeeLeadVO> pageInfo = opportunityService.statAgentEnterprise(page);
        return Result.succeed(pageInfo, "查询成功");
    }

    @LogAnnotation(module = "企业客户管理", recordRequestParam = true, action = "查询", desc = "分页查询顾问企业列表")
    @ApiOperation(value = "分页查询顾问企业列表", notes = "根据顾问编码查询企业列表")
    @PostMapping("/page")
    public Result<PageInfo<AgentEnterpriseListResVo>> page(@RequestBody PageRequest<AgentEnterpriseListReqVo> page) {
        PageInfo<AgentEnterpriseListResVo> pageInfo = genAgentEnterpriseService.getAgentEnterpriseList(page);
        return Result.succeed(pageInfo, "查询成功");
    }
}
