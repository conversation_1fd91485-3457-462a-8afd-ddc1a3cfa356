package com.kbao.kbcelms.controller.opportunityprocesslog;

import com.kbao.commons.web.Result;
import com.kbao.kbcbpm.BpmWebClientService;
import com.kbao.kbcbpm.process.vo.TaskCountQueryVO;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.constants.ElmsConstants;
import com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityProcessLogRequestVO;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityProcessLogResponseVO;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityCloseReasonQueryRequestVO;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityCloseReasonQueryResponseVO;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kbao.kbcelms.bpm.ElmsBpmWebService;

/**
 * 机会流程日志控制器
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
@Slf4j
@Api(tags = "机会流程日志")
@RestController
@RequestMapping("/api/opportunityProcessLog")
public class OpportunityProcessLogController {

    @Autowired
    private OpportunityProcessLogService opportunityProcessLogService;

    @Autowired
    private ElmsBpmWebService elmsBpmWebService;

    @LogAnnotation(module = "机会流程日志", recordRequestParam = true, action = "查询用户参与机会数量", desc = "查询用户参与过的机会数量和待完成任务数量")
    @ApiOperation(value = "查询用户参与过的机会数量和待完成任务数量", notes = "查询用户参与过的机会数量和待完成任务数量")
    @PostMapping("/countUserParticipatedOpportunities")
    public Result<OpportunityProcessLogResponseVO> countUserParticipatedOpportunities(@RequestBody OpportunityProcessLogRequestVO requestVO) {
        //参与过的机会数
        int opportunityCount = opportunityProcessLogService.countUserParticipatedOpportunities(requestVO.getUserId(), SysLoginUtils.getUser().getTenantId());
        
        TaskCountQueryVO vo = new TaskCountQueryVO();
        vo.setUserId(requestVO.getUserId());
        vo.setBusinessKey(ElmsConstants.BUSINESS_KEY);
        vo.setTenantId(SysLoginUtils.getUser().getTenantId());
        //待完成的任务数
        Result<Long> res = elmsBpmWebService.getClaimedUnprocessedTaskCount(vo);
        
        // 构造响应对象
        OpportunityProcessLogResponseVO responseVO = new OpportunityProcessLogResponseVO();
        responseVO.setOpportunityCount(opportunityCount);
        responseVO.setTaskCount(res.getDatas() != null ? res.getDatas() : 0L);

        return Result.succeed(responseVO, "查询成功");
    }

    @LogAnnotation(module = "机会流程日志", recordRequestParam = true, action = "查询机会关闭原因", desc = "根据机会ID查询关闭类型和关闭原因")
    @ApiOperation(value = "查询机会关闭原因", notes = "根据机会ID查询关闭类型和关闭原因")
    @PostMapping("/getCloseReason")
    public Result<OpportunityCloseReasonQueryResponseVO> getCloseReason(@RequestBody OpportunityCloseReasonQueryRequestVO requestVO) {
        OpportunityCloseReasonQueryResponseVO result = opportunityProcessLogService.getCloseReasonByOpportunityId(
            requestVO.getOpportunityId(), 
            SysLoginUtils.getUser().getTenantId()
        );
        
        if (result == null) {
            return Result.succeed(null, "未找到该机会的关闭原因信息");
        }
        
        return Result.succeed(result, "查询成功");
    }
    
} 