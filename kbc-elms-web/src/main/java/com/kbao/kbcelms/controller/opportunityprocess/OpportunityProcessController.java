package com.kbao.kbcelms.controller.opportunityprocess;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService;
import com.kbao.kbcelms.opportunityprocess.vo.OpportunityProcessStartRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机会流程控制器
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Api(tags = "机会流程管理")
@RestController
@RequestMapping("/api/opportunityProcess")
public class OpportunityProcessController {

    @Autowired
    private OpportunityProcessService opportunityProcessService;

    @LogAnnotation(module = "机会流程", recordRequestParam = true, action = "启动流程", desc = "根据机会ID启动对应的流程")
    @ApiOperation(value = "启动机会流程", notes = "根据机会ID和公司类型启动对应的流程")
    @PostMapping("/startProcess")
    public Result<Void> startProcess(@RequestBody OpportunityProcessStartRequestVO requestVO) {
        try {
            opportunityProcessService.startProcess(requestVO.getOpportunityId(), requestVO.getCompanyType());
            return Result.succeed(null, "流程启动成功");
        } catch (Exception e) {
            log.error("启动流程失败，机会ID：{}，公司类型：{}，错误信息：{}", 
                requestVO.getOpportunityId(), requestVO.getCompanyType(), e.getMessage(), e);
            return Result.succeed(null, "流程启动失败：" + e.getMessage());
        }
    }
} 