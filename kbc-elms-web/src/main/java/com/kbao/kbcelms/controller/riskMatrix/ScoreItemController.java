package com.kbao.kbcelms.controller.riskMatrix;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemOptionQuery;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemQuery;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemRequest;
import com.kbao.kbcelms.riskMatrix.vo.ScoreItemOptionVO;
import com.kbao.kbcelms.riskMatrix.vo.ScoreItemVO;
import com.kbao.kbcelms.riskMatrix.service.ScoreItemService;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 核心评分项管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/api/elms/scoreItem")
public class ScoreItemController extends BaseController {
    
    @Autowired
    private ScoreItemService scoreItemService;
    
    /**
     * 分页查询评分项列表
     */
    @PostMapping("/page")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "分页查询评分项列表")
    public Result<PageInfo<ScoreItemVO>> getPage(@RequestBody PageRequest<ScoreItemQuery> request) {
        try {
            PageInfo<ScoreItemVO> result = scoreItemService.getPage(request);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("分页查询评分项列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询评分项详情
     */
    @GetMapping("/{id}")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "查询评分项详情")
    public Result<ScoreItemVO> getById(@PathVariable Long id) {
        try {
            ScoreItemVO result = scoreItemService.getById(id);
            if (result == null) {
                return Result.failed("评分项不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("查询评分项详情失败，ID：{}", id, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存评分项（新增或更新）
     */
    @PostMapping("/save")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "保存", desc = "保存评分项")
    public Result<ScoreItemVO> save(@RequestBody @Validated ScoreItemRequest request) {
        try {
            // 检查编码是否重复
            if (scoreItemService.existsByCode(request.getCode(), request.getId())) {
                return Result.failed("评分项编码已存在，请修改后重试");
            }
            
            String currentUser = getCurrentUser();
            ScoreItemVO result = scoreItemService.save(request, currentUser);
            
            String action = request.getId() != null ? "更新" : "新增";
            return Result.succeed(result, action + "成功");
        } catch (Exception e) {
            log.error("保存评分项失败", e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID删除评分项
     */
    @DeleteMapping("/{id}")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "删除", desc = "删除评分项")
    public Result<Void> deleteById(@PathVariable Long id) {
        try {
            boolean success = scoreItemService.deleteById(id);
            if (success) {
                return Result.succeed(null, "删除成功");
            } else {
                return Result.failed("删除失败，评分项不存在");
            }
        } catch (Exception e) {
            log.error("删除评分项失败，ID：{}", id, e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
    

    
    /**
     * 根据编码查询评分项
     */
    @GetMapping("/code/{code}")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "根据编码查询评分项")
    public Result<ScoreItemVO> getByCode(@PathVariable String code) {
        try {
            ScoreItemVO result = scoreItemService.getByCode(code);
            if (result == null) {
                return Result.failed("评分项不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("根据编码查询评分项失败，编码：{}", code, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    

    

    


    /**
     * 获取评分项下拉选项
     */
    @PostMapping("/options")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "获取评分项下拉选项")
    public Result<List<ScoreItemOptionVO>> getOptions(@RequestBody ScoreItemOptionQuery request) {
        try {
            List<ScoreItemOptionVO> result = scoreItemService.getOptions(request);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取评分项下拉选项失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        return SysLoginUtils.getUserId();
    }
}
