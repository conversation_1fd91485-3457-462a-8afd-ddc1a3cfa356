package com.kbao.kbcelms.controller.enterprise.query;

import com.github.pagehelper.PageInfo;import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.enterprise.query.config.bean.EnterpriseQueryConfigRequest;
import com.kbao.kbcelms.enterprise.query.config.bean.EnterpriseQueryConfigVO;
import com.kbao.kbcelms.enterprise.query.config.bean.QueryUsageStatisticsVO;
import com.kbao.kbcelms.enterprise.query.config.service.EnterpriseQueryConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 企业查询配置控制器
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("/api/enterprise/query/config")
@Api(tags = "企业查询配置管理")
public class EnterpriseQueryConfigController extends BaseController {

    @Autowired
    private EnterpriseQueryConfigService enterpriseQueryConfigService;

    /**
     * 获取查询使用统计列表
     */
    @ApiOperation(value = "获取查询使用统计列表", notes = "获取顾问查询使用情况统计")
    @PostMapping("/statistics")
    @LogAnnotation(module = "企业查询配置", recordRequestParam = true, action = "查询", desc = "获取查询使用统计列表")
    public Result<PageInfo<QueryUsageStatisticsVO>> getStatistics(@RequestBody PageRequest<QueryUsageStatisticsVO> pageRequest) {
        try {
            QueryUsageStatisticsVO param = pageRequest.getParam();
            String agentName = param != null ? param.getAgentName() : null;
            String agentCode = param != null ? param.getAgentCode() : null;
            
            PageInfo<QueryUsageStatisticsVO> result = enterpriseQueryConfigService.getQueryUsageStatistics(
                pageRequest.getPageNum(),
                pageRequest.getPageSize(),
                agentName,
                agentCode
            );
            
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取查询使用统计列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取通用配置
     */
    @ApiOperation(value = "获取通用配置", notes = "获取系统通用查询限制配置")
    @GetMapping("/general")
    @LogAnnotation(module = "企业查询配置", recordRequestParam = true, action = "查询", desc = "获取通用配置")
    public Result<EnterpriseQueryConfigVO> getGeneralConfig() {
        try {
            EnterpriseQueryConfigVO result = enterpriseQueryConfigService.getGeneralConfig();
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取通用配置失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新通用配置
     */
    @ApiOperation(value = "更新通用配置", notes = "更新系统通用查询限制配置")
    @PostMapping("/general/update")
    @LogAnnotation(module = "企业查询配置", recordRequestParam = true, action = "更新", desc = "更新通用配置")
    public Result<String> updateGeneralConfig(@RequestBody @Valid EnterpriseQueryConfigRequest request) {
        try {
            enterpriseQueryConfigService.updateGeneralConfig(request);
            return Result.succeed("更新成功");
        } catch (Exception e) {
            log.error("更新通用配置失败", e);
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 设置个人配置
     */
    @ApiOperation(value = "设置个人配置", notes = "设置顾问个人查询限制配置")
    @PostMapping("/personal/set")
    @LogAnnotation(module = "企业查询配置", recordRequestParam = true, action = "设置", desc = "设置个人配置")
    public Result<String> setPersonalConfig(@RequestBody @Valid EnterpriseQueryConfigRequest request) {
        try {
            enterpriseQueryConfigService.setPersonalConfig(request);
            return Result.succeed("设置成功");
        } catch (Exception e) {
            log.error("设置个人配置失败", e);
            return Result.failed("设置失败：" + e.getMessage());
        }
    }

    /**
     * 删除个人配置
     */
    @ApiOperation(value = "删除个人配置", notes = "删除顾问个人配置，恢复使用通用配置")
    @PostMapping("/personal/remove")
    @LogAnnotation(module = "企业查询配置", recordRequestParam = true, action = "删除", desc = "删除个人配置")
    public Result<String> removePersonalConfig(@RequestParam String agentCode) {
        try {
            enterpriseQueryConfigService.removePersonalConfig(agentCode);
            return Result.succeed("删除成功");
        } catch (Exception e) {
            log.error("删除个人配置失败", e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
}
