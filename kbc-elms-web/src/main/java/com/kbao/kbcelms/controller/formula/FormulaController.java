package com.kbao.kbcelms.controller.formula;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.formula.dto.FormulaCalculationDTO;
import com.kbao.kbcelms.formula.dto.FormulaDTO;
import com.kbao.kbcelms.formula.dto.FormulaQueryDTO;
import com.kbao.kbcelms.formula.entity.FormulaCalculationLog;
import com.kbao.kbcelms.formula.service.FormulaService;
import com.kbao.kbcelms.formula.vo.FormulaCalculationResultVO;
import com.kbao.kbcelms.formula.vo.FormulaVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 公式管理Controller
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/formula")
@Api(tags = "公式管理")
public class FormulaController extends BaseController {

    @Autowired
    private FormulaService formulaService;

    @PostMapping("/list")
    @ApiOperation("分页查询公式列表")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "查询", desc = "分页查询公式列表")
    public Result<PageInfo<FormulaVO>> getFormulaList(
            @ApiParam("分页查询参数") @RequestBody @Valid PageRequest<FormulaQueryDTO> pageRequest
    ) {
        PageInfo<FormulaVO> result = formulaService.getFormulaList(pageRequest);
        return Result.succeed(result, "查询成功");
    }

    @GetMapping("/{id}")
    @ApiOperation("获取公式详情")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "查询", desc = "获取公式详情")
    public Result<FormulaVO> getFormulaDetail(
            @ApiParam("公式ID") @PathVariable @NotNull Long id
    ) {
        FormulaVO result = formulaService.getFormulaDetail(id);
        return Result.succeed(result, "查询成功");
    }

    @PostMapping
    @ApiOperation("创建公式")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "新增", desc = "创建公式")
    public Result<Boolean> createFormula(
            @ApiParam("公式信息") @RequestBody @Valid FormulaDTO formulaDTO
    ) {
        Boolean result = formulaService.createFormula(formulaDTO);
        return Result.succeed(result, "创建成功");
    }

    @PutMapping("/{id}")
    @ApiOperation("更新公式")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "修改", desc = "更新公式")
    public Result<Boolean> updateFormula(
            @ApiParam("公式ID") @PathVariable @NotNull Long id,
            @ApiParam("公式信息") @RequestBody @Valid FormulaDTO formulaDTO
    ) {
        Boolean result = formulaService.updateFormula(id, formulaDTO);
        return Result.succeed(result, "更新成功");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除公式")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "删除", desc = "删除公式")
    public Result<Boolean> deleteFormula(
            @ApiParam("公式ID") @PathVariable @NotNull Long id
    ) {
        Boolean result = formulaService.deleteFormula(id);
        return Result.succeed(result, "删除成功");
    }

    @PostMapping("/calculate")
    @ApiOperation("计算公式")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "计算", desc = "计算公式")
    public Result<FormulaCalculationResultVO> calculateFormula(
            @ApiParam("计算参数") @RequestBody @Valid FormulaCalculationDTO calculationDTO
    ) {
        FormulaCalculationResultVO result = formulaService.calculateFormula(calculationDTO);
        return Result.succeed(result, "计算完成");
    }

    @PostMapping("/validate")
    @ApiOperation("验证公式语法")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "验证", desc = "验证公式语法")
    public Result<Boolean> validateFormula(
            @ApiParam("公式内容") @RequestParam String formula
    ) {
        Boolean result = formulaService.validateFormula(formula);
        return Result.succeed(result, "验证完成");
    }

    @GetMapping("/categories")
    @ApiOperation("获取公式分类选项")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "查询", desc = "获取公式分类选项")
    public Result<List<String>> getFormulaCategoryList() {
        List<String> result = formulaService.getFormulaCategoryList();
        return Result.succeed(result, "查询成功");
    }

    @GetMapping("/{id}/calculation-logs")
    @ApiOperation("获取公式计算记录")
    @LogAnnotation(module = "公式管理", recordRequestParam = true, action = "查询", desc = "获取公式计算记录")
    public Result<List<FormulaCalculationLog>> getFormulaCalculationLogs(
            @ApiParam("公式ID") @PathVariable @NotNull Long id,
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit
    ) {
        List<FormulaCalculationLog> result = formulaService.getFormulaCalculationLogs(id, limit);
        return Result.succeed(result, "查询成功");
    }
}
