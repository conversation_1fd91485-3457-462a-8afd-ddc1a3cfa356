package com.kbao.kbcelms.controller.opportunity;


import com.kbao.commons.web.Result;
import com.kbao.kbcelms.opportunityfiles.model.OpportunityFiles;
import com.kbao.kbcelms.opportunityfiles.service.OpportunityFilesService;
import com.kbao.tool.model.SysUser;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/opportunity/file")
@Api(tags = "机会项目资料文件接口")
public class OpportunityFileController {

    @Autowired
    private OpportunityFilesService opportunityFilesService;

    @ApiOperation(value = "获取机会项目资料文件", notes = "获取机会项目资料文件")
    @PostMapping("/list")
    public Result<List<OpportunityFiles>> list(@RequestBody OpportunityFiles files){
        List<OpportunityFiles> list = opportunityFilesService.findByOpportunityId(files.getOpportunityId());

        return Result.succeed(list,"操作成功");
    }

    @ApiOperation(value = "删除资料文件", notes = "删除资料文件")
    @PostMapping("/remove")
    public Result remove(@RequestBody OpportunityFiles files){
        opportunityFilesService.remove(files.getId());
        return Result.succeed("操作成功");
    }

    @ApiOperation(value = "资料文件上传", notes = "资料文件上传")
    @PostMapping("/upload")
    public Result upload( @RequestParam("opportunityId") Integer opportunityId, @RequestParam("fileType") String fileType, @RequestParam("companyId") String companyId, @RequestParam("companyName") String companyName, @RequestParam("file") MultipartFile file){
        SysUser user = SysLoginUtils.getUser();
        opportunityFilesService.upload("elmsWeb",file,opportunityId,fileType,companyId,companyName,user.getUserId(),user.getNickName());
        return Result.succeed("操作成功");
    }


}
