package com.kbao.kbcelms.controller.enterprise.query;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.enterprise.query.record.bean.EnterpriseCreateRecordVO;
import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseCreateRecord;
import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseCreateRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 企业创建记录控制器
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@RestController
@RequestMapping("/api/enterprise/create/record")
@Api(tags = "企业创建记录管理")
public class EnterpriseCreateRecordController extends BaseController {

    @Autowired
    private EnterpriseCreateRecordService enterpriseCreateRecordService;

    /**
     * 分页查询企业创建记录
     */
    @ApiOperation(value = "分页查询企业创建记录", notes = "根据条件分页查询企业创建记录列表，支持按所属行业、城市、时间等条件查询")
    @PostMapping("/page")
    @LogAnnotation(module = "企业创建记录", recordRequestParam = true, action = "查询", desc = "分页查询企业创建记录")
    public Result<PageInfo<EnterpriseCreateRecord>> page(@RequestBody PageRequest<EnterpriseCreateRecordVO> pageRequest) {
        try {
            PageInfo<EnterpriseCreateRecord> result = enterpriseCreateRecordService.pageCreateRecords(pageRequest);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("分页查询企业创建记录失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
}
