package com.kbao.kbcelms.controller.dataTemplate;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.export.ExcelUtils;import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.model.RequestPage;import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.dataTemplate.bean.DataReqVo;
import com.kbao.kbcelms.dataTemplate.entity.DataTemplate;
import com.kbao.kbcelms.dataTemplate.model.DataTemplateField;
import com.kbao.kbcelms.dataTemplate.service.DataTemplateFieldService;
import com.kbao.kbcelms.dataTemplate.service.DataTemplateService;
import com.kbao.tool.util.DateUtils;import com.kbao.tool.util.SysLoginUtils;
import org.springframework.validation.annotation.Validated;import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageInfo;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> jie
 * @Description 管理
 * @Date 2025-07-22
*/
@RestController
@RequestMapping("/api/dataTemplate")
public class DataTemplateController extends BaseController {

	@Autowired
	private DataTemplateService dataTemplateService;
	@Autowired
	private DataTemplateFieldService dataTemplateFieldService;

	@PostMapping("/page")
	@LogAnnotation(module = "管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<DataTemplate>> page(@RequestBody RequestObjectPage<DataTemplate> page) {
		PageInfo<DataTemplate> dataTemplatePage = dataTemplateService.page(page);
		Result<PageInfo<DataTemplate>> result = Result.succeed(dataTemplatePage,ResultStatusEnum.SUCCESS.getMsg());
		return result;
	}

	@PostMapping("/add")
	@LogAnnotation(module = "管理", action = "新增", desc = "新增")
	public Result saveWithFields(@RequestBody DataTemplate dataTemplate){
		dataTemplateService.addWithFields(dataTemplate);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/update")
	@LogAnnotation(module = "管理", action = "修改", desc = "修改")
	public Result updateWithFields(@RequestBody DataTemplate dataTemplate){
		dataTemplateService.updateWithFields(dataTemplate);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/detail")
	@LogAnnotation(module = "管理", action = "查看明细", desc = "查看明细")
	public Result detail(@RequestBody DataTemplate dataTemplate){
		DataTemplate data = dataTemplateService.getById(dataTemplate.getId());
		return Result.succeed(data,ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/delete")
	@LogAnnotation(module = "管理", action = "删除", desc = "删除")
	public Result delete(@RequestBody DataTemplate dataTemplate) {
		dataTemplateService.delete(dataTemplate.getId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

    @PostMapping("/fields/export")
    @LogAnnotation(module = "管理", action = "导出", desc = "导出")
    public void exportFields(@RequestBody @Validated DataTemplate dataTemplate, HttpServletResponse response) throws Exception {
        List<DataTemplateField> fieldList = dataTemplateFieldService.getExportFieldList(dataTemplate.getId());
        ExcelUtils<DataTemplateField> exportsExcelUtils = new ExcelUtils<>(DataTemplateField.class);
        exportsExcelUtils.writeExcel(fieldList, "字段列表_" + DateUtils.thisDate(), response);
    }

    @RequestMapping(value = "/fields/import", method = RequestMethod.POST)
    public Result importFields(@RequestParam(value = "templateId") Integer templateId,
                                      @RequestParam("file") MultipartFile file) {
        List<DataTemplateField> importedFields = dataTemplateFieldService.importFields(templateId, file);
        return Result.succeed(importedFields, "导入成功，数据已回显");
    }

	@PostMapping("/fields")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询模板字段列表")
	public Result<List<DataTemplateField>> getTemplateFields(@RequestBody DataReqVo reqVo) {
		List<DataTemplateField> fields = dataTemplateService.getFieldList(reqVo.getTemplateCode());
		return Result.succeed(fields, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/data/page")
	@LogAnnotation(module = "管理", action = "查询", desc = "分页查询模板数据")
	public Result<PageInfo<Map>> getTemplateData(@RequestBody PageRequest<DataReqVo> reqVo) {
		PageInfo<Map> dataPage = dataTemplateService.getDataList(reqVo);
		return Result.succeed(dataPage, ResultStatusEnum.SUCCESS.getMsg());
	}



}