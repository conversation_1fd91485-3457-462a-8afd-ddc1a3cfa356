package com.kbao.kbcelms.controller.opportunity;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.opportunitylog.model.OpportunityLog;
import com.kbao.kbcelms.opportunitylog.service.OpportunityLogService;
import com.kbao.tool.util.EmptyUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.InvocationTargetException;

/**
 * 机会日志控制器类，处理与机会日志相关的 HTTP 请求。
 * @Author: luobb
 */
@Slf4j
@Api(tags = {"机会日志管理"})
@RestController
@RequestMapping("/api/opportunity/log")
public class OpportunityLogController {

    @Autowired
    private OpportunityLogService opportunityLogService;

    /**
     * 分页查询机会日志记录
     * @param requestPage 分页请求参数，包含查询条件和分页信息
     * @return 包含机会日志记录列表和分页信息的 Pagination 对象
     */
    @ApiOperation("分页查询机会日志记录")
    @PostMapping("/page")
    public Result<PageInfo<OpportunityLog>> pageOpportunityLog(
            @ApiParam(value = "分页请求参数（含查询条件和分页信息）", required = true)
            @RequestBody PageRequest<OpportunityLog> requestPage) {
        PageInfo<OpportunityLog> pageInfo = this.opportunityLogService.pageOpportunityLog(requestPage);
        if(EmptyUtils.isEmpty(requestPage.getParam()) || EmptyUtils.isEmpty(requestPage.getParam().getOpportunityId())) {
           throw new BusinessException("缺少opportunityId参数");
        }
        return Result.succeed(pageInfo, "查询成功");
    }

    /**
     * 新增机会日志记录
     * @param opportunityLog 要新增的机会日志对象
     * @return 保存后的机会日志对象
     */
    @ApiOperation("新增机会日志记录")  // 方法级别注解：描述接口功能
    @PostMapping("/add")
    public Result<OpportunityLog> addOpportunityLog(
            @ApiParam(value = "新增的机会日志对象", required = true)
            @RequestBody OpportunityLog opportunityLog) {
        OpportunityLog savedLog = this.opportunityLogService.addOpportunityLog(opportunityLog);
        return Result.succeed(savedLog, "新增成功");
    }

    /**
     * 修改机会日志记录
     * @param opportunityLog 要修改的机会日志对象，包含新的属性值和主键
     * @return 更新后的机会日志对象，若记录不存在则返回 null
     */
    @ApiOperation("修改机会日志记录")  // 方法级别注解：描述接口功能
    @PostMapping("/update")
    public Result<OpportunityLog> updateOpportunityLog(
            @ApiParam(value = "修改的机会日志对象（含主键和新属性值）", required = true)  // 参数注解：描述请求体含义
            @RequestBody OpportunityLog opportunityLog) throws InvocationTargetException, IllegalAccessException {
        OpportunityLog updatedLog = this.opportunityLogService.updateOpportunityLog(opportunityLog);
        return Result.succeed(updatedLog, "更新成功");
    }

    /**
     * 删除机会日志记录
     * @return 删除成功返回 true，若记录不存在则返回 false
     */
    @ApiOperation("删除机会日志记录")
    @PostMapping("/delete")
    public Result deleteOpportunityLog(
            @ApiParam(value = "包含日志ID的对象", required = true)
            @RequestBody OpportunityLog opportunityLog) {
        this.opportunityLogService.deleteOpportunityLog(opportunityLog.getId());
        return Result.succeed(true, "删除成功");
    }

    /**
     * 根据主键查询机会日志记录
     * @return 查询到的机会日志对象，若记录不存在则返回 null
     */
    @ApiOperation("根据主键查询机会日志记录")
    @PostMapping("/get")
    public Result<OpportunityLog> getOpportunityLog(@RequestBody OpportunityLog opportunityLog) {
        OpportunityLog log = this.opportunityLogService.getOpportunityLog(opportunityLog.getId());
        return Result.succeed(log, "查询成功");
    }
}