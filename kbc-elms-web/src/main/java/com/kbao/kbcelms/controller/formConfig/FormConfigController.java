package com.kbao.kbcelms.controller.formConfig;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.export.ExcelUtils;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcelms.formConfig.entity.FormConfig;
import com.kbao.kbcelms.formConfig.model.FormConfigField;
import com.kbao.kbcelms.formConfig.service.FormConfigService;
import com.kbao.kbcelms.formConfig.service.FormConfigFieldService;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.tool.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.github.pagehelper.PageInfo;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 表单配置管理
 * @Date 2025-08-15
 */
@RestController
@RequestMapping("/api/formConfig")
public class FormConfigController extends BaseController {

    @Autowired
    private FormConfigService formConfigService;

    @Autowired
    private FormConfigFieldService formConfigFieldService;

    /**
     * 分页查询表单配置列表
     */
    @PostMapping("/page")
    @LogAnnotation(module = "表单配置管理", action = "查询", desc = "分页查询列表")
    public Result<PageInfo<FormConfig>> page(@RequestBody RequestObjectPage<FormConfig> page) {
        PageInfo<FormConfig> formConfigPage = formConfigService.page(page);
        return Result.succeed(formConfigPage, ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 新增表单配置
     */
    @PostMapping("/add")
    @LogAnnotation(module = "表单配置管理", action = "新增", desc = "新增表单配置")
    public Result add(@RequestBody FormConfig formConfig) {
        formConfigService.addWithFields(formConfig);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 更新表单配置
     */
    @PostMapping("/update")
    @LogAnnotation(module = "表单配置管理", action = "修改", desc = "更新表单配置")
    public Result update(@RequestBody FormConfig formConfig) {
        formConfigService.updateWithFields(formConfig);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 根据ID查询表单配置详情
     */
    @GetMapping("/get/{id}")
    @LogAnnotation(module = "表单配置管理", action = "查询", desc = "查询表单配置详情")
    public Result<FormConfig> getById(@PathVariable("id") Integer id) {
        FormConfig formConfig = formConfigService.getById(id);
        return Result.succeed(formConfig, ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 删除表单配置
     */
    @PostMapping("/delete")
    @LogAnnotation(module = "表单配置管理", action = "删除", desc = "删除表单配置")
    public Result delete(@RequestBody FormConfig formConfig) {
        formConfigService.delete(formConfig.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 导出字段列表
     */
    @PostMapping("/fields/export")
    @LogAnnotation(module = "表单配置管理", action = "导出", desc = "导出字段列表")
    public void exportFields(@RequestBody @Validated FormConfig formConfig, HttpServletResponse response) throws Exception {
        List<FormConfigField> fieldList = formConfigFieldService.getExportFieldList(formConfig.getId());
        ExcelUtils<FormConfigField> exportsExcelUtils = new ExcelUtils<>(FormConfigField.class);
        exportsExcelUtils.writeExcel(fieldList, "表单字段列表_" + DateUtils.thisDate(), response);
    }

    /**
     * 导入字段列表（仅解析，不保存）
     */
    @RequestMapping(value = "/fields/import", method = RequestMethod.POST)
    @LogAnnotation(module = "表单配置管理", action = "导入", desc = "导入字段列表")
    public Result importFields(@RequestParam(value = "configId") Integer configId,
                              @RequestParam("file") MultipartFile file) {
        List<FormConfigField> importedFields = formConfigFieldService.importFields(configId, file);
        return Result.succeed(importedFields, "导入成功，数据已回显");
    }
}
