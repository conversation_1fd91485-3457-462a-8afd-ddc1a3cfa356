package com.kbao.kbcelms.controller.upload;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.upload.UploadFileService;
import com.kbao.kbcufs.file.vo.client.FileUploadRequest;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.EmptyUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/8/1 10:16
 */
@RestController
@Api(tags = "文件上传管理")
@Slf4j
public class UploadFileController {

    @Autowired
    private UploadFileService uploadFileService;

    @PostMapping("/api/upload/file")
    @ApiOperation("上传文件")
    public Result<String> uploadImage(
            @ApiParam(value = "上传的文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "文件类型", required = true) @RequestParam("fileType") String fileType) throws IOException {
        String url = uploadFileService.upload(file,fileType);
        return Result.succeed(url, "上传成功");
    }

}
