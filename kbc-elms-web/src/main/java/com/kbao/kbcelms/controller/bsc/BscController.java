package com.kbao.kbcelms.controller.bsc;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.company.model.CompanyListForWebVo;
import com.kbao.kbcbsc.dicitems.entity.DicItems;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.organization.entity.Organization;
import com.kbao.kbcbsc.user.model.UserIdReq;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.bsc.BscClientService;
import com.kbao.kbcelms.bsc.vo.BscWebUser;
import com.kbao.kbcelms.bsc.vo.DicCodeRequestVO;
import com.kbao.kbcelms.outinf.vo.OrgCodesRequestVO;
import com.kbao.kbcelms.outinf.vo.TenantIdRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:09
 */
@RestController
@RequestMapping("/api/bsc")
@Api(tags = "快保云服接口控制器")
public class BscController extends BaseController {

    @Autowired
    private BscClientService bscClientService;

    @PostMapping("/dic/getDicItems")
    public Result<List<DicItems>> getDicItems(@Validated @RequestBody DicCodeRequestVO req, BindingResult bindRes) {
        checkValidator("getDicItems", bindRes, req);
        Result<List<DicItems>> dicItems = bscClientService.getDicItems(req.getDicCode());
        return dicItems;
    }

    @PostMapping("/getWebUserInfo")
    public Result<BscWebUser> getWebUserInfo() {
        BscWebUser bscWwebUser = new BscWebUser();
        bscWwebUser.setIsAdmin(BscUserUtils.getUser().getUser().getIsAdmin());
        bscWwebUser.setFuncAuthDTO(BscUserUtils.getUser().getFunction().getFuncAuths());
        bscWwebUser.setAppTenantListVo(bscClientService.getWebUserTenants().getDatas());
        return Result.succeed(bscWwebUser, "获取登录用户信息成功");
    }

    @PostMapping("/tenant/getTenantUsers")
    public Result<List<UserIdReq>> getTenantUsers() {
        return bscClientService.getTenantUsers();
    }


    /**
     * 查询所有法人分公司
     * @param requestVO 请求参数，包含租户ID（可选）
     * @return
     */
    @ApiOperation(value = "查询法人公司", notes = "查询法人公司")
    @LogAnnotation(module = "外部接口", recordRequestParam = true, action = "查询", desc = "查询法人公司")
    @PostMapping("/getLegalList")
    public Result<List<Organization>> getLegalList(@RequestBody(required = false) TenantIdRequestVO requestVO) {
        return bscClientService.getLegalList(requestVO);
    }

    @ApiOperation(value = "查询营业部", notes = "根据组织机构编码数组查询其下层节点中orgType为4的营业部")
    @LogAnnotation(module = "外部接口", recordRequestParam = true, action = "查询", desc = "根据组织机构编码数组查询营业部")
    @PostMapping("/getTransactionsList")
    public Result<List<Organization>> getTransactions(@RequestBody OrgCodesRequestVO requestVO) {

        return bscClientService.getTransactions(requestVO);
    }

    @ApiOperation(value = "查询所有保险公司", notes = "查询所有保险公司")
    @PostMapping("/getCompanyList")
    public Result<List<CompanyListForWebVo>> getCompanyList() {
        return bscClientService.getCompanyList();
    }
}
