package com.kbao.kbcelms.controller.enterprise.query;

import com.github.pagehelper.PageInfo;import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.enterprise.query.record.bean.EnterpriseQueryRecordVO;
import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseQueryRecord;import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseQueryRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 企业查询记录控制器
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@RestController
@RequestMapping("/api/enterprise/query/record")
@Api(tags = "企业查询记录管理")
public class EnterpriseQueryRecordController extends BaseController {

    @Autowired
    private EnterpriseQueryRecordService enterpriseQueryRecordService;

    /**
     * 分页查询企业查询记录
     */
    @ApiOperation(value = "分页查询企业查询记录", notes = "根据条件分页查询企业查询记录列表")
    @PostMapping("/page")
    @LogAnnotation(module = "企业查询记录", recordRequestParam = true, action = "查询", desc = "分页查询企业查询记录")
    public Result<PageInfo<EnterpriseQueryRecord>> page(@RequestBody PageRequest<EnterpriseQueryRecordVO> pageRequest) {
        try {
            PageInfo<EnterpriseQueryRecord> result = enterpriseQueryRecordService.pageQueryRecords(pageRequest);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("分页查询企业查询记录失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
}
