package com.kbao.kbcelms.controller.feign;

import com.kbao.commons.web.Result;
import com.kbao.kbcelms.client.ElmsUserClientWebService;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.user.vo.ElmsUserFeignRequestVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/8/14 15:14
 */
@RestController
@Api(tags = "用户feign接口")
@Slf4j
public class UserClientWebController implements ElmsUserClientWebService {

    @Autowired
    private UserService userService;

    @Override
    public Result<String> syncDeleteElmsUser(ElmsUserFeignRequestVO request) {
        userService.syncDeleteElmsUser(request);
        return Result.succeed("删除成功");
    }
}
