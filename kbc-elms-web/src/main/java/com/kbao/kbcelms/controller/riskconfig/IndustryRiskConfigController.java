package com.kbao.kbcelms.controller.riskconfig;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.riskconfig.bean.IndustryRiskConfigQuery;
import com.kbao.kbcelms.riskconfig.bean.IndustryRiskConfigRequest;
import com.kbao.kbcelms.riskconfig.entity.RiskConfigHistory;
import com.kbao.kbcelms.riskconfig.service.IndustryRiskConfigService;
import com.kbao.kbcelms.riskconfig.vo.IndustryRiskConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 行业风险配置控制器
 * <AUTHOR>
 * @date 2025-08-11
 */
@Api(tags = "行业风险配置管理")
@RestController
@RequestMapping("/api/elms/industryRiskConfig")
@Validated
public class IndustryRiskConfigController extends BaseController {

    @Autowired
    private IndustryRiskConfigService industryRiskConfigService;

    /**
     * 分页查询行业风险配置列表
     */
    @ApiOperation("分页查询行业风险配置列表")
    @PostMapping("/page")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "查询", desc = "分页查询行业风险配置列表")
    public Result<PageInfo<IndustryRiskConfigVO>> getPage(
            @RequestBody @Valid PageRequest<IndustryRiskConfigQuery> request) {
        try {
            PageInfo<IndustryRiskConfigVO> result = industryRiskConfigService.getPage(request);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("分页查询行业风险配置失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据一级行业编码获取配置详情
     */
    @ApiOperation("根据一级行业编码获取配置详情")
    @GetMapping("/{industryLevel1Code}")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "查询", desc = "获取行业风险配置详情")
    public Result<IndustryRiskConfigVO> getByIndustryLevel1Code(
            @ApiParam("一级行业编码") @PathVariable @NotBlank String industryLevel1Code) {
        try {
            IndustryRiskConfigVO result = industryRiskConfigService.getByIndustryLevel1Code(industryLevel1Code);
            if (result == null) {
                return Result.failed("配置不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("获取行业风险配置详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取配置详情
     */
    @ApiOperation("根据ID获取配置详情")
    @GetMapping("/detail/{id}")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "查询", desc = "根据ID获取配置详情")
    public Result<IndustryRiskConfigVO> getById(
            @ApiParam("配置ID") @PathVariable @NotBlank String id) {
        try {
            IndustryRiskConfigVO result = industryRiskConfigService.getById(id);
            if (result == null) {
                return Result.failed("配置不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("获取行业风险配置详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 保存行业风险配置
     */
    @ApiOperation("保存行业风险配置")
    @PostMapping("/save")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "保存", desc = "保存行业风险配置")
    public Result<IndustryRiskConfigVO> save(@RequestBody @Valid IndustryRiskConfigRequest request) {
        try {
            IndustryRiskConfigVO result = industryRiskConfigService.save(request);
            return Result.succeed(result, "保存成功");
        } catch (Exception e) {
            logger.error("保存行业风险配置失败", e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }

    /**
     * 更新行业风险配置（兼容旧接口）
     */
    @ApiOperation("更新行业风险配置")
    @PutMapping("/{industryCode}")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "更新", desc = "更新行业风险配置")
    public Result<IndustryRiskConfigVO> updateByIndustryCode(
            @ApiParam("行业编码") @PathVariable @NotBlank String industryCode,
            @RequestBody Map<String, Object> updateData) {
        try {
            IndustryRiskConfigVO result = industryRiskConfigService.updateByIndustryCode(industryCode, updateData);
            return Result.succeed(result, "更新成功");
        } catch (Exception e) {
            logger.error("更新行业风险配置失败", e);
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除行业风险配置
     */
    @ApiOperation("删除行业风险配置")
    @DeleteMapping("/{id}")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "删除", desc = "删除行业风险配置")
    public Result<Void> delete(@ApiParam("配置ID") @PathVariable @NotBlank String id) {
        try {
            industryRiskConfigService.delete(id);
            return Result.succeed(null, "删除成功");
        } catch (Exception e) {
            logger.error("删除行业风险配置失败", e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除行业风险配置
     */
    @ApiOperation("批量删除行业风险配置")
    @DeleteMapping("/batch")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "批量删除", desc = "批量删除行业风险配置")
    public Result<Void> batchDelete(@RequestBody @NotEmpty List<String> ids) {
        try {
            industryRiskConfigService.batchDelete(ids);
            return Result.succeed(null, "批量删除成功");
        } catch (Exception e) {
            logger.error("批量删除行业风险配置失败", e);
            return Result.failed("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取行业列表
     */
    @ApiOperation("获取行业列表")
    @GetMapping("/industries")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "查询", desc = "获取行业列表")
    public Result<List<Map<String, Object>>> getIndustries(
            @ApiParam("租户ID") @RequestParam(required = false) String tenantId) {
        try {
            List<Map<String, Object>> result = industryRiskConfigService.getIndustries(tenantId);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("获取行业列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取统计信息
     */
    @ApiOperation("获取统计信息")
    @GetMapping("/statistics")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "查询", desc = "获取统计信息")
    public Result<Map<String, Object>> getStatistics(
            @ApiParam("租户ID") @RequestParam(required = false) String tenantId) {
        try {
            Map<String, Object> result = industryRiskConfigService.getStatistics(tenantId);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取配置变更历史
     */
    @ApiOperation("获取配置变更历史")
    @GetMapping("/{id}/history")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "查询", desc = "获取配置变更历史")
    public Result<PageInfo<RiskConfigHistory>> getHistory(
            @ApiParam("配置ID") @PathVariable @NotBlank String id,
            @ApiParam("页码") @RequestParam(defaultValue = "1") int pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int pageSize) {
        try {
            PageInfo<RiskConfigHistory> result = industryRiskConfigService.getHistory(id, pageNum, pageSize);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("获取配置变更历史失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 检查一级行业编码是否存在
     */
    @ApiOperation("检查一级行业编码是否存在")
    @GetMapping("/checkIndustryLevel1Code")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "检查", desc = "检查一级行业编码是否存在")
    public Result<Boolean> checkIndustryLevel1Code(
            @ApiParam("一级行业编码") @RequestParam @NotBlank String industryLevel1Code,
            @ApiParam("排除的ID") @RequestParam(required = false) String excludeId,
            @ApiParam("租户ID") @RequestParam(required = false) String tenantId) {
        try {
            boolean exists = industryRiskConfigService.getDao().existsByIndustryLevel1Code(industryLevel1Code, excludeId, tenantId);
            return Result.succeed(exists, "检查完成");
        } catch (Exception e) {
            logger.error("检查一级行业编码是否存在失败，编码：{}", industryLevel1Code, e);
            return Result.failed("检查失败：" + e.getMessage());
        }
    }

    /**
     * 验证一级行业编码格式
     */
    @ApiOperation("验证一级行业编码格式")
    @GetMapping("/validateIndustryLevel1CodeFormat")
    @LogAnnotation(module = "行业风险配置", recordRequestParam = true, action = "验证", desc = "验证一级行业编码格式")
    public Result<String> validateIndustryLevel1CodeFormat(
            @ApiParam("一级行业编码") @RequestParam @NotBlank String industryLevel1Code) {
        try {
            industryRiskConfigService.validateIndustryLevel1CodeFormat(industryLevel1Code);
            return Result.succeed("格式正确", "验证通过");
        } catch (Exception e) {
            logger.error("验证一级行业编码格式失败，编码：{}", industryLevel1Code, e);
            return Result.failed("验证失败：" + e.getMessage());
        }
    }
}
