package com.kbao.kbcelms.controller.operationlog;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.operationlog.entity.OperationLog;
import com.kbao.kbcelms.operationlog.service.OperationLogService;
import com.kbao.kbcelms.operationlog.dto.OperationLogQuery;
import com.kbao.tool.util.EmptyUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机会操作日志管理
 * <AUTHOR>
 */
@Slf4j
@Api(tags = {"机会操作日志管理"})
@RestController
@RequestMapping("/api/operation/log")
public class OperationLogController {

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 分页查询操作日志记录
     * @param requestPage 分页请求参数，包含查询条件和分页信息
     * @return 包含操作日志记录列表和分页信息的 Pagination 对象
     */
    @ApiOperation("分页查询操作日志记录")
    @PostMapping("/page")
    public Result<PageInfo<OperationLog>> pageOperationLog(
            @ApiParam(value = "分页请求参数（含查询条件和分页信息）", required = true)
            @RequestBody PageRequest<OperationLogQuery> requestPage) {
        PageInfo<OperationLog> pageInfo = this.operationLogService.pageOperationLog(requestPage);
        return Result.succeed(pageInfo, "查询成功");
    }

    /**
     * 根据ID查询操作日志记录
     * @param query 查询参数，包含操作日志ID
     * @return 包含操作日志记录的 Result 对象
     */
    @ApiOperation("查年操作日志记录")
    @PostMapping("/get")
    public Result<OperationLog> getOperationLog(@ApiParam(value = "查看请求参数", required = true)
            @RequestBody OperationLogQuery query) {
        if (EmptyUtils.isEmpty(query.getId())) {
            return Result.failed("查询参数不能为空");
        }
        OperationLog operationLog = this.operationLogService.findById(query.getId());
        return Result.succeed(operationLog, "查询成功");
    }
}
