package com.kbao.kbcelms.industrylimit.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitAction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行业限制执行动作 Mapper 接口
 * <AUTHOR>
 * @date 2025-08-08
 */
@Mapper
public interface IndustryLimitActionMapper extends BaseMapper<IndustryLimitAction, Long> {

    /**
     * 根据规则ID查询执行动作列表
     * @param ruleId 规则ID
     * @return 执行动作列表
     */
    List<IndustryLimitAction> selectByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 根据规则ID删除执行动作
     * @param ruleId 规则ID
     * @return 删除数量
     */
    int deleteByRuleId(@Param("ruleId") Long ruleId);
}
