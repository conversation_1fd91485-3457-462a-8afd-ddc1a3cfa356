package com.kbao.kbcelms.industrylimit.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.formConfig.model.FormConfigField;
import com.kbao.kbcelms.industrylimit.bean.IndustryLimitFieldOptionVO;
import com.kbao.kbcelms.industrylimit.bean.IndustryLimitQuery;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitActionMapper;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitConditionMapper;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitMapper;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimit;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitAction;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition;
import com.kbao.kbcelms.industrylimit.vo.IndustryLimitActionVO;
import com.kbao.kbcelms.industrylimit.vo.IndustryLimitConditionVO;
import com.kbao.kbcelms.industrylimit.vo.IndustryLimitVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.tool.util.SysLoginUtils;
import com.kbao.kbcelms.formConfig.service.FormConfigService;
import com.kbao.kbcelms.common.constants.ElmsConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.LinkedHashMap;

/**
 * 行业限制规则服务实现类
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
public class IndustryLimitService extends BaseSQLServiceImpl<IndustryLimit, Long, IndustryLimitMapper> {

    @Autowired
    private IndustryLimitConditionMapper conditionMapper;

    @Autowired
    private IndustryLimitActionMapper actionMapper;
    
    @Autowired
    private FormConfigService formConfigService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 分页查询行业限制规则
     * @param request 分页请求
     * @return 分页结果
     */
    public PageInfo<IndustryLimitVO> getPage(PageRequest<IndustryLimitQuery> request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        
        // 使用JOIN查询一次性获取所有数据
        List<IndustryLimitVO> rawList = mapper.selectIndustryLimitListWithDetails(request.getParam());
        
        // 处理JOIN查询结果，合并重复的规则记录
        List<IndustryLimitVO> list = processJoinQueryResults(rawList);

        // 为每个VO设置状态名称
        for (IndustryLimitVO vo : list) {
            vo.setStatusName(vo.getStatus() == 1 ? "启用" : "禁用");
        }

        return new PageInfo<>(list);
    }

    /**
     * 处理JOIN查询结果，合并重复的规则记录
     * @param rawList JOIN查询的原始结果
     * @return 合并后的规则列表
     */
    private List<IndustryLimitVO> processJoinQueryResults(List<IndustryLimitVO> rawList) {
        if (CollectionUtils.isEmpty(rawList)) {
            return new ArrayList<>();
        }

        // 使用LinkedHashMap保持插入顺序
        Map<Long, IndustryLimitVO> ruleMap = new LinkedHashMap<>();
        
        for (IndustryLimitVO item : rawList) {
            Long ruleId = item.getId();
            
            // 如果规则不存在，创建新的规则记录
            if (!ruleMap.containsKey(ruleId)) {
                IndustryLimitVO rule = new IndustryLimitVO();
                BeanUtils.copyProperties(item, rule);
                rule.setConditions(new ArrayList<>());
                rule.setActionList(new ArrayList<>());
                ruleMap.put(ruleId, rule);
            }
            
            IndustryLimitVO rule = ruleMap.get(ruleId);
            
            // 添加条件（避免重复）
            if (item.getConditions() != null && !item.getConditions().isEmpty()) {
                for (IndustryLimitConditionVO condition : item.getConditions()) {
                    if (condition != null && condition.getId() != null) {
                        boolean exists = rule.getConditions().stream()
                            .anyMatch(c -> c.getId().equals(condition.getId()));
                        if (!exists) {
                            rule.getConditions().add(condition);
                        }
                    }
                }
            }
            
            // 添加动作（避免重复）
            if (item.getActionList() != null && !item.getActionList().isEmpty()) {
                for (IndustryLimitActionVO action : item.getActionList()) {
                    if (action != null && action.getId() != null) {
                        boolean exists = rule.getActionList().stream()
                            .anyMatch(a -> a.getId().equals(action.getId()));
                        if (!exists) {
                            // 处理serviceIds字段的JSON转换
                            convertActionServiceIds(action);
                            rule.getActionList().add(action);
                        }
                    }
                }
            }
        }
        
        return new ArrayList<>(ruleMap.values());
    }

    /**
     * 转换动作中的serviceIds字段
     * @param action 动作对象
     */
    private void convertActionServiceIds(IndustryLimitActionVO action) {
        // 从serviceIdsJson字段转换为serviceIds列表
        try {
            if (StringUtils.hasText(action.getServiceIdsJson())) {
                List<String> serviceIds = objectMapper.readValue(action.getServiceIdsJson(), 
                    new TypeReference<List<String>>() {});
                action.setServiceIds(serviceIds);
            } else {
                action.setServiceIds(new ArrayList<>());
            }
        } catch (Exception e) {
            action.setServiceIds(new ArrayList<>());
        }
    }

    /**
     * 根据ID查询行业限制规则详情
     * @param id 规则ID
     * @return 规则详情
     */
    public IndustryLimitVO getById(Long id) {
        IndustryLimitVO vo = mapper.selectIndustryLimitById(id);
        if (vo != null) {
            // 查询条件列表
            List<IndustryLimitCondition> conditions = conditionMapper.selectByRuleId(id);
            vo.setConditions(convertConditionsToVO(conditions));

            // 查询执行动作列表
            List<IndustryLimitAction> actions = actionMapper.selectByRuleId(id);
            vo.setActionList(convertActionsToVO(actions));

            // 设置状态名称
            vo.setStatusName(vo.getStatus() == 1 ? "启用" : "禁用");
        }
        return vo;
    }

    /**
     * 新增行业限制规则
     * @param vo 规则信息
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public IndustryLimitVO add(IndustryLimitVO vo) {
        // 检查编码是否重复
        if (checkCodeExists(vo.getCode(), null)) {
            throw new RuntimeException("规则编码已存在");
        }

        // 保存主表
        IndustryLimit entity = new IndustryLimit();
        BeanUtils.copyProperties(vo, entity);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreateUser(BscUserUtils.getUserId());
        entity.setUpdateUser(BscUserUtils.getUserId());
        entity.setTenantId(SysLoginUtils.getUser().getTenantId());
        entity.setIsDeleted(0);

        insertSelective(entity);

        // 保存条件
        saveConditions(entity.getId(), vo.getConditions());

        // 保存执行动作
        saveActions(entity.getId(), vo.getActionList());

        return getById(entity.getId());
    }

    /**
     * 更新行业限制规则
     * @param vo 规则信息
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public IndustryLimitVO update(IndustryLimitVO vo) {
        // 检查编码是否重复
        if (checkCodeExists(vo.getCode(), vo.getId())) {
            throw new RuntimeException("规则编码已存在");
        }

        // 更新主表
        IndustryLimit entity = new IndustryLimit();
        BeanUtils.copyProperties(vo, entity);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());

        updateByPrimaryKeySelective(entity);

        // 删除原有条件和动作
        conditionMapper.deleteByRuleId(vo.getId());
        actionMapper.deleteByRuleId(vo.getId());

        // 保存新的条件和动作
        saveConditions(vo.getId(), vo.getConditions());
        saveActions(vo.getId(), vo.getActionList());

        return getById(vo.getId());
    }

    /**
     * 删除行业限制规则
     * @param id 规则ID
     */
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        // 逻辑删除主表
        IndustryLimit entity = new IndustryLimit();
        entity.setId(id);
        entity.setIsDeleted(1);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());
        updateByPrimaryKeySelective(entity);

        // 删除条件和动作
        int result = conditionMapper.deleteByRuleId(id);
        if(result >= 0) { // 修改条件，允许删除0条记录（当没有条件时）
            return actionMapper.deleteByRuleId(id);
        }
        return result;
    }

    /**
     * 检查规则编码是否存在
     * @param code 规则编码
     * @param id 排除的ID
     * @return 是否存在
     */
    public boolean checkCodeExists(String code, Long id) {
        return mapper.checkCodeExists(code, id) > 0;
    }

    /**
     * 保存条件列表
     */
    private void saveConditions(Long ruleId, List<IndustryLimitConditionVO> conditions) {
        if (!CollectionUtils.isEmpty(conditions)) {
            for (int i = 0; i < conditions.size(); i++) {
                IndustryLimitConditionVO conditionVO = conditions.get(i);
                IndustryLimitCondition condition = new IndustryLimitCondition();
                BeanUtils.copyProperties(conditionVO, condition);
                condition.setRuleId(ruleId);
                condition.setSortOrder(i + 1);
                condition.setCreateTime(LocalDateTime.now());
                condition.setTenantId(SysLoginUtils.getUser().getTenantId());
                conditionMapper.insert(condition);
            }
        }
    }

    /**
     * 保存执行动作列表
     */
    private void saveActions(Long ruleId, List<IndustryLimitActionVO> actions) {
        if (!CollectionUtils.isEmpty(actions)) {
            for (int i = 0; i < actions.size(); i++) {
                IndustryLimitActionVO actionVO = actions.get(i);
                IndustryLimitAction action = new IndustryLimitAction();
                BeanUtils.copyProperties(actionVO, action);
                action.setRuleId(ruleId);
                action.setSortOrder(i + 1);
                action.setCreateTime(LocalDateTime.now());
                action.setTenantId(SysLoginUtils.getUser().getTenantId());

                // 转换服务ID列表为JSON字符串
                if (!CollectionUtils.isEmpty(actionVO.getServiceIds())) {
                    try {
                        action.setServiceIds(objectMapper.writeValueAsString(actionVO.getServiceIds()));
                    } catch (Exception e) {
                        throw new RuntimeException("服务ID列表转换失败", e);
                    }
                }

                actionMapper.insert(action);
            }
        }
    }

    /**
     * 转换条件列表为VO
     */
    private List<IndustryLimitConditionVO> convertConditionsToVO(List<IndustryLimitCondition> conditions) {
        List<IndustryLimitConditionVO> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(conditions)) {
            for (IndustryLimitCondition condition : conditions) {
                IndustryLimitConditionVO vo = new IndustryLimitConditionVO();
                BeanUtils.copyProperties(condition, vo);
                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 转换执行动作列表为VO
     */
    private List<IndustryLimitActionVO> convertActionsToVO(List<IndustryLimitAction> actions) {
        List<IndustryLimitActionVO> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(actions)) {
            for (IndustryLimitAction action : actions) {
                IndustryLimitActionVO vo = new IndustryLimitActionVO();
                BeanUtils.copyProperties(action, vo);

                // 转换JSON字符串为服务ID列表
                if (StringUtils.hasText(action.getServiceIds())) {
                    try {
                        List<String> serviceIds = objectMapper.readValue(action.getServiceIds(), 
                            new TypeReference<List<String>>() {});
                        vo.setServiceIds(serviceIds);
                    } catch (Exception e) {
                        vo.setServiceIds(new ArrayList<>());
                    }
                }

                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 获取字段选项列表
     * @return 字段选项列表
     */
    public List<IndustryLimitFieldOptionVO> getFieldOptions() {
        List<IndustryLimitFieldOptionVO> options = new ArrayList<>();
        
        try {
            // 调用FormConfigService获取代理企业补充表单的字段列表
            List<FormConfigField> fieldList =
                formConfigService.getFieldList(ElmsConstant.FORM_CONFIG_AGENT_ENTERPRISE);
            
            if (fieldList != null && !fieldList.isEmpty()) {
                // 将FormConfigField转换为IndustryLimitFieldOptionVO
                for (FormConfigField field : fieldList) {
                    options.add(new IndustryLimitFieldOptionVO(
                        field.getFieldCode(), 
                        field.getFieldName(),
                        field.getShowType(),
                        field.getAdditional()
                    ));
                }
            } else {
                // 如果没有获取到字段，抛出异常
                throw new RuntimeException("没有获取到字段");
            }
        } catch (Exception e) {
            // 如果调用失败，抛出异常
            throw new RuntimeException("获取表单配置字段失败", e);
        }
        
        return options;
    }

    /**
     * 根据规则ID获取条件列表
     * @param ruleId 规则ID
     * @return 条件列表
     */
    public List<IndustryLimitCondition> getConditionsByRuleId(Long ruleId) {
        return conditionMapper.selectByRuleId(ruleId);
    }

    /**
     * 根据规则ID获取执行动作列表
     * @param ruleId 规则ID
     * @return 执行动作列表
     */
    public List<IndustryLimitAction> getActionsByRuleId(Long ruleId) {
        return actionMapper.selectByRuleId(ruleId);
    }

    /**
     * 切换行业限制规则状态
     * @param id 规则ID
     * @param status 状态：0-禁用，1-启用
     */
    @Transactional(rollbackFor = Exception.class)
    public void toggleStatus(Long id, Integer status) {
        if (id == null) {
            throw new RuntimeException("规则ID不能为空");
        }
        
        if (status == null || (status != 0 && status != 1)) {
            throw new RuntimeException("状态值无效，应为0或1");
        }
        
        // 检查规则是否存在
        IndustryLimit existingRule = this.selectByPrimaryKey(id);
        if (existingRule == null) {
            throw new RuntimeException("规则不存在: " + id);
        }
        
        // 更新状态
        IndustryLimit updateRule = new IndustryLimit();
        updateRule.setId(id);
        updateRule.setStatus(status);
        updateRule.setUpdateTime(LocalDateTime.now());
        updateRule.setUpdateUser(SysLoginUtils.getUserId());
        
        int result = this.updateByPrimaryKeySelective(updateRule);
        if (result <= 0) {
            throw new RuntimeException("状态更新失败");
        }
    }

}
