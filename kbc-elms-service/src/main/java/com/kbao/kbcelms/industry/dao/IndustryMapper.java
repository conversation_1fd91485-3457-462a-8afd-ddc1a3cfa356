package com.kbao.kbcelms.industry.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.industry.entity.Industry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 行业分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface IndustryMapper extends BaseMapper<Industry, Integer> {

    /**
     * 检查行业代码是否存在
     * @param code 行业代码
     * @param id 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    int isExistCode(@Param("code") String code, @Param("id") Integer id);

    /**
     * 根据行业代码查询行业信息
     * @param code 行业代码
     * @return 行业信息
     */
    Industry selectByCode(@Param("code") String code);

    /**
     * 根据父级代码查询子级行业列表
     * @param parentCode 父级行业代码
     * @return 子级行业列表
     */
    List<Industry> selectByParentCode(@Param("parentCode") String parentCode);

    /**
     * 根据级别查询行业列表
     * @param level 行业级别
     * @return 行业列表
     */
    List<Industry> selectByLevel(@Param("level") Integer level);

    /**
     * 批量插入行业数据
     * @param industryList 行业数据列表
     * @return 插入的数量
     */
    int batchInsert(@Param("list") List<Industry> industryList);

    /**
     * 批量更新行业数据
     * @param industryList 行业数据列表
     * @return 更新的数量
     */
    int batchUpdate(@Param("list") List<Industry> industryList);

    /**
     * 清空所有行业数据
     * @return 删除的数量
     */
    int truncateTable();
}
