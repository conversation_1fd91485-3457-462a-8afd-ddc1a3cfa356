package com.kbao.kbcelms.wechat;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.client.TenantApiClientService;
import com.kbao.kbcbsc.client.WechatServiceClientService;
import com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo;
import com.kbao.kbcbsc.tenant.bean.TenantVo;
import com.kbao.kbcbsc.tenant.entity.Tenant;
import com.kbao.kbcbsc.tenantinfo.entity.TenantInfo;
import com.kbao.kbcbsc.wechat.request.AppParam;
import com.kbao.kbcbsc.wechat.request.WechatParam;
import com.kbao.kbcbsc.wechat.response.SignatureVO;
import com.kbao.kbcelms.bsc.KbcBscService;
import com.kbao.kbcelms.bsc.config.KbcBscConfig;
import com.kbao.kbcelms.common.config.ElmsContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Service
public class WxAuthService {
    @Autowired
    private KbcBscService kbcBscService;
    @Autowired
    private KbcBscConfig kbcBscConfig;

    @Autowired
    private TenantApiClientService tenantApiClientService;

    @Autowired
    private WechatServiceClientService wechatServiceClientService;


    public SignatureVO getSignature(String url) {
        //获取头信息
        WechatParam wechatParam = new WechatParam();
        wechatParam.setAuthUrl(url);
        AppParam appParam = kbcBscService.getAppParam(wechatParam, ElmsContext.getTenantId(), kbcBscConfig.getAppCode(), "2");
        return kbcBscService.getSignature(appParam);
    }

    public void wechatAuth(String redirectUrl, HttpServletRequest request, HttpServletResponse response){
        String tenantId = ElmsContext.getTenantId();
        TenantVo vo = new TenantVo();
        vo.setTenantId(tenantId);
        String userAgent = request.getHeader("User-Agent");
        try {
            if (!StringUtils.contains(userAgent, "MicroMessenger")) {
                response.sendRedirect(redirectUrl);
                return;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        Result<TenantConfigInfoVo> tenantConfigInfo = tenantApiClientService.getTenantConfigInfo(vo);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(tenantConfigInfo.getResp_code())) {
            String tenantName = "";
            String targetName = "";
            TenantConfigInfoVo tenantConfig = tenantConfigInfo.getDatas();
            if (null != tenantConfig) {
                Tenant tenant = tenantConfig.getTenant();
                TenantInfo tenantInfo = tenantConfig.getTenantInfo();
                if (null != tenant && StringUtils.isNotEmpty(tenant.getTenantName())) {
                    tenantName = tenant.getTenantName();
                }
                if (null != tenantInfo && StringUtils.isNotEmpty(tenantInfo.getTargetName())) {
                    targetName = tenantInfo.getTargetName();
                }
            }
            String appCode = kbcBscConfig.getAppCode();
            Map<String,String> headerMap = new HashMap<>();
            headerMap.put("tenantId", tenantId);
            headerMap.put("tenantName", tenantName);
            headerMap.put("appCode", appCode);
            headerMap.put("appName", targetName);
            FeignRequestHeader.Header.set(headerMap);
            AppParam param = new AppParam();
            param.setTenantId(tenantId);
            param.setAppCode(appCode);
            param.setServiceType("2");
            WechatParam wechatParam = new WechatParam();
            wechatParam.setRedirectUrl(redirectUrl);
            param.setWechatParam(wechatParam);
            Result<Map> rs = wechatServiceClientService.authorize(param);
            if(ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())) {
                try {
                    response.sendRedirect(String.valueOf(rs.getDatas().get("url")));
                }catch (Exception e){
                    e.printStackTrace();
                }
            }else{
                throw new BusinessException("获取微信授权失败");
            }
        } else {
            throw new BusinessException("获取微信授权失败");
        }
    }
}
