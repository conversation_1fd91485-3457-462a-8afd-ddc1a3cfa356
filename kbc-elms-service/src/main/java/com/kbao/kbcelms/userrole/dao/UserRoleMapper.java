package com.kbao.kbcelms.userrole.dao;

import java.util.*;
import java.util.Map;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.userrole.entity.UserRole;
public interface UserRoleMapper  extends BaseMapper<UserRole, Integer>{

    UserRole findByUserIdAndRoleId(Map<String, Object> queryParam);
	
	/**
	 * 根据用户ID查询所有角色ID
	 * @param userId 用户ID
	 * @return 角色ID列表
	 */
	List<String> findRolesByUserId(String userId);

	/**
	 * 删除用户指定角色
	 *
	 * @param userRole
	 * <AUTHOR>
	 * @Date 2025/7/31 18:13
	 */
	void deleteByUserIdAndRoleId(UserRole userRole);

}