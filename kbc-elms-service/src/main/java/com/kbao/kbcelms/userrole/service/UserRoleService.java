package com.kbao.kbcelms.userrole.service;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Maps;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.userrole.entity.UserRole;
import com.kbao.kbcelms.userrole.dao.UserRoleMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;


@Service
public class UserRoleService extends BaseSQLServiceImpl<UserRole, Integer,UserRoleMapper> {

	/**
	 * 根据用户ID获取所有角色ID
	 * @param userId 用户ID
	 * @return 角色ID列表
	 */
	public List<String> getRoleIdsByUserId(String userId) {
		if (userId == null || userId.trim().isEmpty()) {
			return new java.util.ArrayList<>();
		}
		return mapper.findRolesByUserId(userId);
	}

	public List<UserRole> selectRolesByUserId(String userId,String tenantId) {
		Map<String,Object> param = Maps.newHashMap();
		param.put("userId", userId);
		param.put("tenantId", tenantId);
		return mapper.selectAll(param);
	}

	public void deleteByUserIdAndRoleId(String userId,Integer roleId) {
		UserRole userRole = new UserRole();
		userRole.setUserId(userId);
		userRole.setRoleId(roleId);
		userRole.setUpdateId(SysLoginUtils.getUserId());
		userRole.setUpdateTime(DateUtils.getCurrentDate());
		mapper.deleteByUserIdAndRoleId(userRole);
	}

}
