package com.kbao.kbcelms.upload;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.appfilechannel.bean.AppFileChannelListVo;
import com.kbao.kbcbsc.tenant.entity.Tenant;
import com.kbao.kbcelms.bsc.BscClientService;
import com.kbao.kbcucs.context.RequestContext;
import com.kbao.kbcufs.adapter.FileClientAdapter;
import com.kbao.kbcufs.enums.FileTypeEnum;
import com.kbao.kbcufs.file.vo.client.FileUploadRequest;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.kbcufs.utils.SignatureUtil;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2025/8/1 10:17
 */
@Service
public class UploadFileService {

    private final static String APP_CODE = "elmsWeb";

    @Autowired
    private BscClientService bscClientService;

    @Autowired
    private FileClientAdapter fileClientAdapter;

    public String upload(MultipartFile file, String fileType) {

        String tenantId = "T0001";
        String nickeName = "sys";
        if (EmptyUtils.isEmpty(SysLoginUtils.getUser())) {
            tenantId = RequestContext.TenantId.get();
            nickeName = RequestContext.UserId.get();
        } else {
            tenantId = SysLoginUtils.getUser().getTenantId();
            nickeName = SysLoginUtils.getUser().getNickName();
        }

        AppFileChannelListVo config = bscClientService.getFileChannelConfig(tenantId, APP_CODE, "1");

        FileUploadRequest request = this.buildUploadReuqest(file, tenantId, nickeName, fileType, config.getAccount(), config.getSecretKey());

        Result<FileUploadResponse> response = fileClientAdapter.upload(request);

        if (ResultStatusEnum.SUCCESS.getStatus().equals(response.getResp_code())) {
            return response.getDatas().getAbsolutePath();
        } else {
            throw new BusinessException("上传失败，失败原因:" + response.getResp_msg());
        }
    }


    /**
     * 文件上传实体构建
     *
     * @param file
     * @param tenantId
     * @param nickeName
     * @param fileType
     * @param userName
     * @param secretKey
     * <AUTHOR>
     * @Date 2025/8/1 10:51
     */
    private FileUploadRequest buildUploadReuqest(MultipartFile file, String tenantId, String nickeName, String fileType, String userName, String secretKey) {
        FileUploadRequest request = new FileUploadRequest();
        request.setType(FileTypeEnum.FILE.getType());
        request.setBusinessNo(IdWorker.getIdStr());
        request.setBusinessTenantId(tenantId);
        Tenant tenant = bscClientService.getTenant(tenantId);
        request.setBusinessTenantName(tenant.getShortName());
        request.setCreateUser(nickeName);
        request.setTimestamp(DateUtils.getCurrentTime());
        request.setUserName(userName);
        request.setPath(StringUtil.join(IdWorker.get32UUID().substring(0, 6), "/", file.getOriginalFilename()));
        request.setSign(SignatureUtil.getSign(request, secretKey));
        request.setRename(false);
        request.setFile(file);
        request.setFileType(fileType);
        request.setNetwork("sts");
        return request;
    }
}
