package com.kbao.kbcelms.bpm;


import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.Result;
import com.kbao.kbcbpm.adapter.BpmWebClientAdapter;
import com.kbao.kbcbpm.process.vo.*;
import com.kbao.kbcelms.constants.ElmsConstants;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@Service
public class ElmsBpmWebService {

    @Autowired
    private BpmWebClientAdapter clientAdapter;

    public static String getBpmId(Object id){
        return ElmsConstants.BUSINESS_KEY +"_" + id.toString();
    }

    /**
     * 部署流程定义
     * @param var1 流程定义JSON
     * @return 部署结果
     */
    public Result deployProcessByJsonWithModel(ProcessDefinitionJsonVO var1) {
        if (var1.getProcessKey()!=null && !var1.getProcessKey().startsWith(ElmsConstants.BUSINESS_KEY)){
            var1.setProcessKey(getBpmId(var1.getProcessKey()));
        }
        if (EmptyUtils.isEmpty(var1.getBusinessKey())){
            var1.setBusinessKey(ElmsConstants.BUSINESS_KEY);
        }
        return clientAdapter.deployProcessByJsonWithModel(var1);
    }

    /**
     * 启动流程实例
     * @param var1 流程启动参数
     * @return 流程实例信息
     */
    public Result<ProcessInstanceVO> startProcessInstanceWithBusinessKey(ProcessStartVO var1) {
        if (var1.getProcessDefinitionKey()!=null && !var1.getProcessDefinitionKey().startsWith(ElmsConstants.BUSINESS_KEY)){
            var1.setProcessDefinitionKey(getBpmId(var1.getProcessDefinitionKey()));
        }
        if (EmptyUtils.isEmpty(var1.getBusinessKey())){
            var1.setBusinessKey(ElmsConstants.BUSINESS_KEY);
        }
        return clientAdapter.startProcessInstanceWithBusinessKey(var1);
    }

    /**
     * 设置任务处理人
     * @param var1 任务用户更新参数
     * @return 任务列表
     */
    public Result<List<TaskVO>> setTaskDealUserById(TaskUserUpdateVO var1) {
        return clientAdapter.setTaskDealUserById(var1);
    }

    /**
     * 根据流程实例ID获取当前任务信息
     * @param var1 流程实例ID参数
     * @return 任务列表
     */
    public Result<List<TaskVO>> getCurrentTaskInfoById(ProcessInstanceIdVO var1) {
        return clientAdapter.getCurrentTaskInfoById(var1);
    }

    /**
     * 完成当前任务
     * @param var1 任务完成参数
     * @return 完成结果
     */
    public Result<ProcessExecutionStatusVO> completeCurrentTasksByProcessInstanceId(TaskCompleteVO var1) {
        return clientAdapter.completeCurrentTasksByProcessInstanceId(var1);
    }

    /**
     * 获取用户待办任务
     * @param var1 任务查询参数
     * @return 分页任务列表
     */
    public Result<PageInfo<TaskVO>> getTodoTasksByUserAndCandidate(TaskQueryVO var1) {
        if (var1.getProcessDefinitionKey()!=null && !var1.getProcessDefinitionKey().startsWith(ElmsConstants.BUSINESS_KEY)){
            var1.setProcessDefinitionKey(getBpmId(var1.getProcessDefinitionKey()));
        }
        if (EmptyUtils.isEmpty(var1.getBusinessKey())){
            var1.setBusinessKey(ElmsConstants.BUSINESS_KEY);
        }
        return clientAdapter.getTodoTasksByUserAndCandidate(var1);
    }

    /**
     * 获取用户参与的任务历史
     * @param var1 用户任务历史查询参数
     * @return 分页任务列表
     */
    public Result<PageInfo<TaskVO>> getUserParticipatedTasks(UserTaskHistoryQueryVO var1) {
        return clientAdapter.getUserParticipatedTasks(var1);
    }

    /**
     * 获取用户已领取未处理的任务数量
     * @param var1 任务数量查询参数
     * @return 任务数量
     */
    public Result<Long> getClaimedUnprocessedTaskCount(TaskCountQueryVO var1) {
        if (EmptyUtils.isEmpty(var1.getBusinessKey())){
            var1.setBusinessKey(ElmsConstants.BUSINESS_KEY);
        }
        return clientAdapter.getClaimedUnprocessedTaskCount(var1);
    }

    /**
     * 回滚流程到指定活动
     * @param var1 流程回滚参数
     * @return 任务列表
     */
    public Result<List<TaskVO>> rollbackProcessToActivity(ProcessRollbackVO var1) {
        // 如果方法不存在，返回空结果或抛出异常
        return clientAdapter.rollbackProcessToActivity(var1);
    }

    /**
     * 根据自定义属性获取节点信息
     * @param var1 节点查询参数
     * @return 节点信息列表
     */
    public Result<List<NodeInfoVO>> getNodesByCustomProperty(NodeQueryByPropertyVO var1) {
        // 如果方法不存在，返回空结果或抛出异常
        return clientAdapter.getNodesByCustomProperty(var1);
    }

    /**
     * 根据自定义属性回滚流程
     * @param var1 流程回滚参数
     * @return 任务列表
     */
    public Result<List<TaskVO>> rollbackProcessByCustomProperty(ProcessRollbackByPropertyVO var1) {
        // 如果方法不存在，返回空结果或抛出异常
        return clientAdapter.rollbackProcessByCustomProperty(var1);
    }

    /**
     * 根据流程实例ID获取当前流程执行进度
     *
     * @param processInstanceId 流程实例ID
     * @return 流程进度信息
     */
    public Result<ProcessProgressVO> getProcessProgress(String processInstanceId){
        return clientAdapter.getProcessProgress(processInstanceId);
    }

    public Result<List<TaskVO>> setBatchTaskDealUserById(BatchTaskUserUpdateVO batchTaskUserUpdateVO) {
        return clientAdapter.setBatchTaskDealUserById(batchTaskUserUpdateVO);
    }

}
