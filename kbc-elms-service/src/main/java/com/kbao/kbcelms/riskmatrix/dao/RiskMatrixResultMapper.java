package com.kbao.kbcelms.riskmatrix.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.riskmatrix.entity.RiskMatrixResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风险矩阵计算结果Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface RiskMatrixResultMapper extends BaseMapper<RiskMatrixResult, Long> {
    
    /**
     * 根据矩阵ID查询结果列表
     * 
     * @param matrixId 矩阵ID
     * @return 结果列表
     */
    List<RiskMatrixResult> selectByMatrixId(@Param("matrixId") Long matrixId);
    
    /**
     * 根据企业ID查询结果列表
     * 
     * @param enterpriseId 企业ID
     * @return 结果列表
     */
    List<RiskMatrixResult> selectByEnterpriseId(@Param("enterpriseId") Long enterpriseId);
    
    /**
     * 根据矩阵ID和企业ID查询结果
     * 
     * @param matrixId 矩阵ID
     * @param enterpriseId 企业ID
     * @return 结果
     */
    RiskMatrixResult selectByMatrixIdAndEnterpriseId(@Param("matrixId") Long matrixId, @Param("enterpriseId") Long enterpriseId);
    
    /**
     * 根据矩阵ID删除结果
     * 
     * @param matrixId 矩阵ID
     * @return 删除数量
     */
    int deleteByMatrixId(@Param("matrixId") Long matrixId);
    
    /**
     * 根据企业ID删除结果
     * 
     * @param enterpriseId 企业ID
     * @return 删除数量
     */
    int deleteByEnterpriseId(@Param("enterpriseId") Long enterpriseId);
    
    /**
     * 批量删除
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    int deleteByIds(@Param("list") List<Long> ids);
    
    /**
     * 根据企业类型查询结果列表
     * 
     * @param enterpriseType 企业类型
     * @return 结果列表
     */
    List<RiskMatrixResult> selectByEnterpriseType(@Param("enterpriseType") String enterpriseType);
    
    /**
     * 根据风险等级查询结果列表
     * 
     * @param riskLevel 风险等级
     * @return 结果列表
     */
    List<RiskMatrixResult> selectByRiskLevel(@Param("riskLevel") String riskLevel);
}
