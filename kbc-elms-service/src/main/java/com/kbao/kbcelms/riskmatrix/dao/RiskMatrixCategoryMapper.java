package com.kbao.kbcelms.riskmatrix.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.riskmatrix.entity.RiskMatrixCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风险矩阵类别Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface RiskMatrixCategoryMapper extends BaseMapper<RiskMatrixCategory, Long> {
    
    /**
     * 根据矩阵ID查询类别列表
     * 
     * @param matrixId 矩阵ID
     * @return 类别列表
     */
    List<RiskMatrixCategory> selectByMatrixId(@Param("matrixId") Long matrixId);
    
    /**
     * 根据矩阵ID删除类别
     * 
     * @param matrixId 矩阵ID
     * @return 删除数量
     */
    int deleteByMatrixId(@Param("matrixId") Long matrixId);
    
    /**
     * 批量插入类别
     * 
     * @param categories 类别列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<RiskMatrixCategory> categories);
    
    /**
     * 批量删除
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    int deleteByIds(@Param("list") List<Long> ids);
}
