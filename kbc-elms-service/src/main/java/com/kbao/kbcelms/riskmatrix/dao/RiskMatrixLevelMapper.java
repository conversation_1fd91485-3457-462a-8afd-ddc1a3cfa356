package com.kbao.kbcelms.riskmatrix.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.riskmatrix.entity.RiskMatrixLevel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风险矩阵档次配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface RiskMatrixLevelMapper extends BaseMapper<RiskMatrixLevel, Long> {
    
    /**
     * 根据类别ID查询档次列表
     * 
     * @param categoryId 类别ID
     * @return 档次列表
     */
    List<RiskMatrixLevel> selectByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 根据类别ID删除档次
     * 
     * @param categoryId 类别ID
     * @return 删除数量
     */
    int deleteByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 批量插入档次
     * 
     * @param levels 档次列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<RiskMatrixLevel> levels);
    
    /**
     * 批量删除
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    int deleteByIds(@Param("list") List<Long> ids);
    
    /**
     * 根据类别ID和分值范围查询档次
     * 
     * @param categoryId 类别ID
     * @param score 分值
     * @return 档次
     */
    RiskMatrixLevel selectByCategoryIdAndScore(@Param("categoryId") Long categoryId, @Param("score") Double score);
}
