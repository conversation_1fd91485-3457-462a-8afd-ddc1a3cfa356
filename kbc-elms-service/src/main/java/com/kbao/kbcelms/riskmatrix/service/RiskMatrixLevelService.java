package com.kbao.kbcelms.riskmatrix.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskmatrix.entity.RiskMatrixLevel;
import com.kbao.kbcelms.riskmatrix.dao.RiskMatrixLevelMapper;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixRequest;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixLevelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 风险矩阵档次配置服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class RiskMatrixLevelService extends BaseSQLServiceImpl<RiskMatrixLevel, Long, RiskMatrixLevelMapper> {
    
    /**
     * 根据类别ID查询档次列表
     *
     * @param categoryId 类别ID
     * @return 档次列表
     */
    public List<RiskMatrixLevel> getByCategoryId(Long categoryId) {
        return mapper.selectByCategoryId(categoryId);
    }

    /**
     * 根据类别ID查询档次列表并转换为VO对象
     *
     * @param categoryId 类别ID
     * @return 档次VO列表
     */
    public List<RiskMatrixLevelVO> getLevelVOsByCategoryId(Long categoryId) {
        List<RiskMatrixLevel> levels = getByCategoryId(categoryId);
        return levels.stream()
                .map(this::convertLevelToVO)
                .collect(Collectors.toList());
    }

    /**
     * 转换档次实体为VO对象
     *
     * @param level 档次实体
     * @return 档次VO对象
     */
    private RiskMatrixLevelVO convertLevelToVO(RiskMatrixLevel level) {
        RiskMatrixLevelVO vo = new RiskMatrixLevelVO();
        BeanUtils.copyProperties(level, vo);

        // 设置数值字段
        if (level.getMinValue() != null) {
            vo.setMinValue(level.getMinValue().doubleValue());
        }
        if (level.getMaxValue() != null) {
            vo.setMaxValue(level.getMaxValue().doubleValue());
        }

        // 设置区间范围显示
        if (vo.getMinValue() != null && vo.getMaxValue() != null) {
            vo.setRangeDisplay(vo.getMinValue() + " ~ " + vo.getMaxValue());
        }

        return vo;
    }
    
    /**
     * 保存档次配置
     *
     * @param categoryId 类别ID
     * @param levels 档次列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLevels(Long categoryId, List<RiskMatrixLevel> levels) {
        if (categoryId == null || CollectionUtils.isEmpty(levels)) {
            return false;
        }

        // 先删除原有档次配置
        mapper.deleteByCategoryId(categoryId);

        // 设置类别ID和时间
        LocalDateTime now = LocalDateTime.now();
        for (RiskMatrixLevel level : levels) {
            level.setCategoryId(categoryId);
            level.setCreateTime(now);
            level.setUpdateTime(now);
        }

        // 批量插入新的档次配置
        return mapper.batchInsert(levels) > 0;
    }

    /**
     * 保存档次配置（从请求参数）
     *
     * @param categoryId 类别ID
     * @param levelRequests 档次请求参数列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLevelsFromRequests(Long categoryId, List<RiskMatrixRequest.LevelRequest> levelRequests) {
        if (categoryId == null || CollectionUtils.isEmpty(levelRequests)) {
            return false;
        }

        // 转换请求参数为实体对象
        List<RiskMatrixLevel> levels = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        for (int i = 0; i < levelRequests.size(); i++) {
            RiskMatrixRequest.LevelRequest levelRequest = levelRequests.get(i);
            RiskMatrixLevel level = new RiskMatrixLevel();
            BeanUtils.copyProperties(levelRequest, level);

            level.setCategoryId(categoryId);
            level.setMinValue(levelRequest.getMinValue() != null ?
                    BigDecimal.valueOf(levelRequest.getMinValue()) : null);
            level.setMaxValue(levelRequest.getMaxValue() != null ?
                    BigDecimal.valueOf(levelRequest.getMaxValue()) : null);
            level.setSortOrder(i + 1);
            level.setCreateTime(now);
            level.setUpdateTime(now);

            levels.add(level);
        }

        // 调用原有的保存方法
        return saveLevels(categoryId, levels);
    }
    
    /**
     * 根据类别ID删除档次配置
     *
     * @param categoryId 类别ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return;
        }
        mapper.deleteByCategoryId(categoryId);
    }
    
    /**
     * 批量删除档次配置
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return mapper.deleteByIds(ids);
    }
    
    /**
     * 根据类别ID和分值查询对应档次
     *
     * @param categoryId 类别ID
     * @param score 分值
     * @return 档次
     */
    public RiskMatrixLevel getLevelByScore(Long categoryId, Double score) {
        if (categoryId == null || score == null) {
            return null;
        }
        return mapper.selectByCategoryIdAndScore(categoryId, score);
    }

    /**
     * 根据类别ID和分值查询对应档次（BigDecimal版本）
     *
     * @param categoryId 类别ID
     * @param score 分值
     * @return 档次
     */
    public RiskMatrixLevel getLevelByScore(Long categoryId, BigDecimal score) {
        if (categoryId == null || score == null) {
            return null;
        }
        return mapper.selectByCategoryIdAndScore(categoryId, score.doubleValue());
    }

    /**
     * 批量根据类别ID和分值查询对应档次
     *
     * @param categoryScoreMap 类别ID和分值的映射
     * @return 类别ID到档次的映射
     */
    public Map<Long, RiskMatrixLevel> batchGetLevelsByScore(Map<Long, BigDecimal> categoryScoreMap) {
        if (CollectionUtils.isEmpty(categoryScoreMap)) {
            return new HashMap<>();
        }

        Map<Long, RiskMatrixLevel> resultMap = new HashMap<>();

        for (Map.Entry<Long, BigDecimal> entry : categoryScoreMap.entrySet()) {
            Long categoryId = entry.getKey();
            BigDecimal score = entry.getValue();

            if (categoryId != null && score != null) {
                RiskMatrixLevel level = getLevelByScore(categoryId, score);
                if (level != null) {
                    resultMap.put(categoryId, level);
                } else {
                    log.warn("未找到类别 {} 分数 {} 对应的档次级别", categoryId, score);
                }
            }
        }

        return resultMap;
    }
    
    /**
     * 更新档次配置
     * 
     * @param level 档次配置
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLevel(RiskMatrixLevel level) {
        if (level == null || level.getId() == null) {
            return false;
        }
        
        level.setUpdateTime(LocalDateTime.now());
        return mapper.updateByPrimaryKeySelective(level) > 0;
    }
    
    /**
     * 新增档次配置
     * 
     * @param level 档次配置
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean insertLevel(RiskMatrixLevel level) {
        if (level == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        level.setCreateTime(now);
        level.setUpdateTime(now);
        
        return mapper.insertSelective(level) > 0;
    }

    /**
     * 根据类别ID查询档次列表（返回VO对象）
     *
     * @param categoryId 类别ID
     * @return 档次VO列表
     */
    public List<RiskMatrixLevelVO> getLevelsByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return new ArrayList<>();
        }

        List<RiskMatrixLevel> levels = mapper.selectByCategoryId(categoryId);
        List<RiskMatrixLevelVO> result = new ArrayList<>();

        for (RiskMatrixLevel level : levels) {
            RiskMatrixLevelVO vo = new RiskMatrixLevelVO();
            BeanUtils.copyProperties(level, vo);

            // 转换BigDecimal为Double
            if (level.getMinValue() != null) {
                vo.setMinValue(level.getMinValue().doubleValue());
            }
            if (level.getMaxValue() != null) {
                vo.setMaxValue(level.getMaxValue().doubleValue());
            }

            // 设置区间范围显示
            if (vo.getMinValue() != null && vo.getMaxValue() != null) {
                vo.setRangeDisplay(String.format("[%.2f, %.2f]", vo.getMinValue(), vo.getMaxValue()));
            }

            result.add(vo);
        }

        return result;
    }
}
