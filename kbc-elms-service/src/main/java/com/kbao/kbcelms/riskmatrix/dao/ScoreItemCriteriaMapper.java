package com.kbao.kbcelms.riskmatrix.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.riskmatrix.entity.ScoreItemCriteria;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评分项标准Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface ScoreItemCriteriaMapper extends BaseMapper<ScoreItemCriteria, Long> {
    
    /**
     * 根据评分项ID查询标准列表
     * 
     * @param scoreItemId 评分项ID
     * @return 标准列表
     */
    List<ScoreItemCriteria> selectByScoreItemId(@Param("scoreItemId") Long scoreItemId);
    
    /**
     * 根据评分项ID删除标准
     * 
     * @param scoreItemId 评分项ID
     * @return 删除数量
     */
    int deleteByScoreItemId(@Param("scoreItemId") Long scoreItemId);
    
    /**
     * 批量插入标准
     * 
     * @param criteriaList 标准列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<ScoreItemCriteria> criteriaList);
    
    /**
     * 批量删除
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    int deleteByIds(@Param("list") List<Long> ids);
}
