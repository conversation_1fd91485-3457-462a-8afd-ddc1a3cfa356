package com.kbao.kbcelms.riskmatrix.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskmatrix.dao.CategoryScoreItemMapper;
import com.kbao.kbcelms.riskmatrix.entity.CategoryScoreItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 类别与评分项关联表服务类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class CategoryScoreItemService extends BaseSQLServiceImpl<CategoryScoreItem, Long, CategoryScoreItemMapper> {
    
    /**
     * 保存类别与评分项的关联关系
     * 
     * @param categoryId 类别ID
     * @param scoreItemIds 评分项ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCategoryScoreItems(Long categoryId, List<Long> scoreItemIds) {
        if (categoryId == null) {
            log.warn("类别ID为空，跳过保存关联关系");
            return;
        }
        
        // 先删除原有的关联关系
        mapper.deleteByCategoryId(categoryId);
        
        // 如果评分项列表为空，则只删除不新增
        if (CollectionUtils.isEmpty(scoreItemIds)) {
            log.info("评分项列表为空，已删除类别 {} 的所有关联关系", categoryId);
            return;
        }
        
        // 批量插入新的关联关系
        List<CategoryScoreItem> relations = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < scoreItemIds.size(); i++) {
            Long scoreItemId = scoreItemIds.get(i);
            if (scoreItemId != null) {
                CategoryScoreItem relation = CategoryScoreItem.builder()
                        .categoryId(categoryId)
                        .scoreItemId(scoreItemId)
                        .createTime(now)
                        .build();
                relations.add(relation);
            }
        }
        
        if (!relations.isEmpty()) {
            int insertCount = mapper.batchInsert(relations);
            log.info("成功保存类别 {} 与 {} 个评分项的关联关系", categoryId, insertCount);
        }
    }
    
    /**
     * 批量保存多个类别与评分项的关联关系
     * 
     * @param categoryScoreItemMap 类别ID与评分项ID列表的映射
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveCategoryScoreItems(java.util.Map<Long, List<Long>> categoryScoreItemMap) {
        if (CollectionUtils.isEmpty(categoryScoreItemMap)) {
            log.warn("类别评分项映射为空，跳过批量保存");
            return;
        }
        
        // 先删除所有相关的关联关系
        List<Long> categoryIds = new ArrayList<>(categoryScoreItemMap.keySet());
        if (!categoryIds.isEmpty()) {
            mapper.deleteByCategoryIds(categoryIds);
            log.info("已删除 {} 个类别的原有关联关系", categoryIds.size());
        }
        
        // 批量插入新的关联关系
        List<CategoryScoreItem> allRelations = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (java.util.Map.Entry<Long, List<Long>> entry : categoryScoreItemMap.entrySet()) {
            Long categoryId = entry.getKey();
            List<Long> scoreItemIds = entry.getValue();
            
            if (categoryId != null && !CollectionUtils.isEmpty(scoreItemIds)) {
                for (int i = 0; i < scoreItemIds.size(); i++) {
                    Long scoreItemId = scoreItemIds.get(i);
                    if (scoreItemId != null) {
                        CategoryScoreItem relation = CategoryScoreItem.builder()
                                .categoryId(categoryId)
                                .scoreItemId(scoreItemId)
                                .createTime(now)
                                .build();
                        allRelations.add(relation);
                    }
                }
            }
        }
        
        if (!allRelations.isEmpty()) {
            int insertCount = mapper.batchInsert(allRelations);
            log.info("成功批量保存 {} 条类别与评分项的关联关系", insertCount);
        }
    }
    
    /**
     * 根据类别ID查询关联的评分项ID列表
     * 
     * @param categoryId 类别ID
     * @return 评分项ID列表
     */
    public List<Long> getScoreItemIdsByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return new ArrayList<>();
        }
        return mapper.selectScoreItemIdsByCategoryId(categoryId);
    }
    
    /**
     * 根据类别ID列表查询关联关系
     * 
     * @param categoryIds 类别ID列表
     * @return 关联关系列表
     */
    public List<CategoryScoreItem> getByCategoryIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        return mapper.selectByCategoryIds(categoryIds);
    }
    
    /**
     * 删除类别的所有关联关系
     * 
     * @param categoryId 类别ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCategoryId(Long categoryId) {
        if (categoryId != null) {
            int deleteCount = mapper.deleteByCategoryId(categoryId);
            log.info("删除类别 {} 的 {} 条关联关系", categoryId, deleteCount);
        }
    }
    
    /**
     * 删除评分项的所有关联关系
     * 
     * @param scoreItemId 评分项ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByScoreItemId(Long scoreItemId) {
        if (scoreItemId != null) {
            int deleteCount = mapper.deleteByScoreItemId(scoreItemId);
            log.info("删除评分项 {} 的 {} 条关联关系", scoreItemId, deleteCount);
        }
    }
}
