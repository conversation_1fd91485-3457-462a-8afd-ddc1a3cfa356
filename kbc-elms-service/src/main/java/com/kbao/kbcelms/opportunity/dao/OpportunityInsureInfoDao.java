package com.kbao.kbcelms.opportunity.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseQueryRecord;
import com.kbao.kbcelms.opportunity.model.OpportunityInsureInfo;import org.springframework.stereotype.Repository;

/**
 * 企业查询记录数据访问层
 * <AUTHOR>
 * @date 2025-08-12
 */
@Repository
public class OpportunityInsureInfoDao extends BaseMongoDaoImpl<OpportunityInsureInfo, String> {

}
