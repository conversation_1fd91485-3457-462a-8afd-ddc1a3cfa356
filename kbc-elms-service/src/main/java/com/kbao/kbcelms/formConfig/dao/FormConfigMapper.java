package com.kbao.kbcelms.formConfig.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.formConfig.entity.FormConfig;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 表单配置Mapper接口
 * @Date 2025-08-15
 */
public interface FormConfigMapper extends BaseMapper<FormConfig, Integer> {

    /**
     * 根据条件查询所有记录
     * @param params 查询参数
     * @return 表单配置列表
     */
    List<FormConfig> selectAll(Map<String, Object> params);

    /**
     * 根据条件统计数量
     * @param params 查询参数
     * @return 数量
     */
    int count(Map<String, Object> params);

    /**
     * 根据配置编码查询
     * @param configCode 配置编码
     * @return 表单配置
     */
    FormConfig selectByConfigCode(@Param("configCode") String configCode);

    List<FormConfig> selectByType(@Param("type") String type);

    /**
     * 检查配置编码是否存在
     * @param configCode 配置编码
     * @param id 排除的ID
     * @return 数量
     */
    int isExistConfigCode(@Param("configCode") String configCode, @Param("id") Integer id);
}
