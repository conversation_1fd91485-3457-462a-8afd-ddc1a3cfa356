package com.kbao.kbcelms.riskMatrix.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemQuery;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemRequest;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemOptionQuery;
import com.kbao.kbcelms.riskMatrix.entity.ScoreItem;
import com.kbao.kbcelms.riskMatrix.entity.ScoreItemCriteria;
import com.kbao.kbcelms.riskMatrix.vo.ScoreItemVO;
import com.kbao.kbcelms.riskMatrix.vo.ScoreItemOptionVO;
import com.kbao.kbcelms.riskMatrix.dao.ScoreItemMapper;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评分项服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class ScoreItemService extends BaseSQLServiceImpl<ScoreItem, Long, ScoreItemMapper> {
    
    @Autowired
    private ScoreItemCriteriaService criteriaService;
    
    
    public PageInfo<ScoreItemVO> getPage(PageRequest<ScoreItemQuery> request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<ScoreItem> list = mapper.selectByQuery(request.getParam());
        PageInfo<ScoreItem> pageInfo = new PageInfo<>(list);
        
        List<ScoreItemVO> voList = list.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        PageInfo<ScoreItemVO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(voList);
        
        return result;
    }
    
    
    public ScoreItemVO getById(Long id) {
        ScoreItem entity = mapper.selectByPrimaryKey(id);
        if (entity == null) {
            return null;
        }
        
        ScoreItemVO vo = convertToVO(entity);
        
        // 查询评分标准
        List<ScoreItemCriteria> criteria = criteriaService.getByScoreItemId(id);
        if (!CollectionUtils.isEmpty(criteria)) {
            List<ScoreItemVO.CriteriaVO> criteriaVOs = criteria.stream()
                    .map(this::convertCriteriaToVO)
                    .collect(Collectors.toList());
            vo.setCriteria(criteriaVOs);
        }
        
        return vo;
    }
    
    
    @Transactional(rollbackFor = Exception.class)
    public ScoreItemVO save(ScoreItemRequest request, String currentUser) {
        ScoreItem entity = new ScoreItem();
        BeanUtils.copyProperties(request, entity);
        
        // 处理企业类型
        if (!CollectionUtils.isEmpty(request.getEnterpriseTypes())) {
            entity.setEnterpriseTypes(String.join(",", request.getEnterpriseTypes()));
        }
        
        // 处理权重和系数
        entity.setWeight(request.getWeight() != null ? 
                BigDecimal.valueOf(request.getWeight()) : BigDecimal.ONE);
        entity.setCoefficient(request.getCoefficient() != null ? 
                BigDecimal.valueOf(request.getCoefficient()) : BigDecimal.ONE);
        
        LocalDateTime now = LocalDateTime.now();
        
        if (request.getId() != null) {
            // 更新
            entity.setUpdateTime(now);
            entity.setUpdateUser(currentUser);
            mapper.updateByPrimaryKeySelective(entity);
        } else {
            // 新增
            entity.setStatus(1);
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setCreateUser(currentUser);
            entity.setUpdateUser(currentUser);
            mapper.insertSelective(entity);
        }
        
        // 处理评分标准
        if (!CollectionUtils.isEmpty(request.getCriteria())) {
            saveCriteria(entity.getId(), request.getCriteria());
        }
        
        return getById(entity.getId());
    }
    
    
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        // 删除评分标准
        criteriaService.deleteByScoreItemId(id);
        
        // 删除主记录
        return mapper.deleteByPrimaryKey(id) > 0;
    }
    
    

    
    
    public ScoreItemVO getByCode(String code) {
        ScoreItem entity = mapper.selectByCode(code);
        return entity != null ? convertToVO(entity) : null;
    }
    
    
    public boolean existsByCode(String code, Long excludeId) {
        ScoreItem entity = mapper.selectByCode(code);
        if (entity == null) {
            return false;
        }
        return excludeId == null || !excludeId.equals(entity.getId());
    }

    /**
     * 获取评分项下拉选项
     */
    public List<ScoreItemOptionVO> getOptions(ScoreItemOptionQuery request) {
        if (EmptyUtils.isNotEmpty(request.getEnterpriseTypes()) && request.getEnterpriseTypes().contains("、")) {
            request.setEnterpriseTypes(request.getEnterpriseTypes().replaceAll("、", ","));
        }
        if (EmptyUtils.isNotEmpty(request.getEnterpriseTypeList())){
            request.setEnterpriseTypes(String.join(",", request.getEnterpriseTypeList()));
        }
        List<ScoreItem> list = mapper.selectForOptions(request);
        return list.stream()
                .map(vo -> {
                    ScoreItemOptionVO optionVO = new ScoreItemOptionVO();
                    BeanUtils.copyProperties(vo, optionVO);
                    return optionVO;
                })
                .collect(Collectors.toList());
    }



    
    


    /**
     * 根据评分项ID列表获取评分项名称列表
     *
     * @param ids 评分项ID列表
     * @return 评分项名称列表
     */
    public List<String> getScoreItemNamesByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        List<ScoreItem> list = mapper.selectByIds(ids);
        return list.stream()
                .map(ScoreItem::getName)
                .collect(Collectors.toList());
    }
    
    /**
     * 保存评分标准
     */
    private void saveCriteria(Long scoreItemId, List<ScoreItemRequest.CriteriaRequest> criteriaRequests) {
        // 先删除原有标准
        criteriaService.deleteByScoreItemId(scoreItemId);
        
        // 插入新标准
        List<ScoreItemCriteria> criteriaList = new ArrayList<>();
        for (int i = 0; i < criteriaRequests.size(); i++) {
            ScoreItemRequest.CriteriaRequest criteriaRequest = criteriaRequests.get(i);
            ScoreItemCriteria criteria = new ScoreItemCriteria();
            BeanUtils.copyProperties(criteriaRequest, criteria);
            criteria.setScoreItemId(scoreItemId);
            criteria.setSortOrder(i + 1);
            criteria.setCreateTime(LocalDateTime.now());
            criteria.setUpdateTime(LocalDateTime.now());
            criteriaList.add(criteria);
        }
        
        if (!CollectionUtils.isEmpty(criteriaList)) {
            criteriaService.batchInsert(criteriaList);
        }
    }
    
    /**
     * 转换为VO对象
     */
    private ScoreItemVO convertToVO(ScoreItem entity) {
        ScoreItemVO vo = new ScoreItemVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 处理权重和系数
        if (entity.getWeight() != null) {
            vo.setWeight(entity.getWeight().doubleValue());
        }
        if (entity.getCoefficient() != null) {
            vo.setCoefficient(entity.getCoefficient().doubleValue());
        }
        
        // 处理企业类型
        if (StringUtils.hasText(entity.getEnterpriseTypes())) {
            vo.setEnterpriseTypes(Arrays.asList(entity.getEnterpriseTypes().split(",")));
            vo.setEnterpriseTypesDisplay(entity.getEnterpriseTypes().replace(",", "、"));
        }
        
        // 处理状态名称
        vo.setStatusName(entity.getStatus() == 1 ? "启用" : "禁用");
        
        return vo;
    }
    
    /**
     * 转换评分标准为VO对象
     */
    private ScoreItemVO.CriteriaVO convertCriteriaToVO(ScoreItemCriteria criteria) {
        ScoreItemVO.CriteriaVO vo = new ScoreItemVO.CriteriaVO();
        BeanUtils.copyProperties(criteria, vo);
        return vo;
    }
}
