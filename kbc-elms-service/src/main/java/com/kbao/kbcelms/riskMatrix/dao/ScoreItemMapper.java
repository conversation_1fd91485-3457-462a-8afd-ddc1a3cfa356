package com.kbao.kbcelms.riskMatrix.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemQuery;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemOptionQuery;
import com.kbao.kbcelms.riskMatrix.entity.ScoreItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评分项Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface ScoreItemMapper extends BaseMapper<ScoreItem, Long> {
    
    /**
     * 根据编码查询评分项
     * 
     * @param code 评分项编码
     * @return 评分项
     */
    ScoreItem selectByCode(@Param("code") String code);
    
    /**
     * 根据查询条件分页查询评分项列表
     * 
     * @param query 查询条件
     * @return 评分项列表
     */
    List<ScoreItem> selectByQuery(ScoreItemQuery query);

    /**
     * 根据ID列表查询评分项
     *
     * @param ids ID列表
     * @return 评分项列表
     */
    List<ScoreItem> selectByIds(@Param("list") List<Long> ids);
    


    /**
     * 查询评分项下拉选项（支持分页和搜索）
     *
     * @param query 查询条件
     * @return 评分项列表
     */
    List<ScoreItem> selectForOptions(ScoreItemOptionQuery query);
}
