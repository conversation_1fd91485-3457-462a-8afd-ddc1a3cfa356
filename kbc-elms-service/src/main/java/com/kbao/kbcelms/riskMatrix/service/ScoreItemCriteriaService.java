package com.kbao.kbcelms.riskMatrix.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskMatrix.entity.ScoreItemCriteria;
import com.kbao.kbcelms.riskMatrix.dao.ScoreItemCriteriaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评分项标准服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class ScoreItemCriteriaService extends BaseSQLServiceImpl<ScoreItemCriteria, Long, ScoreItemCriteriaMapper> {
    
    /**
     * 根据评分项ID查询标准列表
     * 
     * @param scoreItemId 评分项ID
     * @return 标准列表
     */
    public List<ScoreItemCriteria> getByScoreItemId(Long scoreItemId) {
        return mapper.selectByScoreItemId(scoreItemId);
    }
    
    /**
     * 保存评分标准
     * 
     * @param scoreItemId 评分项ID
     * @param criteriaList 标准列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCriteria(Long scoreItemId, List<ScoreItemCriteria> criteriaList) {
        if (scoreItemId == null || CollectionUtils.isEmpty(criteriaList)) {
            return false;
        }
        
        // 先删除原有标准
        mapper.deleteByScoreItemId(scoreItemId);
        
        // 设置评分项ID和时间
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < criteriaList.size(); i++) {
            ScoreItemCriteria criteria = criteriaList.get(i);
            criteria.setScoreItemId(scoreItemId);
            criteria.setSortOrder(i + 1);
            criteria.setCreateTime(now);
            criteria.setUpdateTime(now);
        }
        
        // 批量插入新的标准
        return mapper.batchInsert(criteriaList) > 0;
    }
    
    /**
     * 根据评分项ID删除标准
     * 
     * @param scoreItemId 评分项ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByScoreItemId(Long scoreItemId) {
        if (scoreItemId == null) {
            return false;
        }
        return mapper.deleteByScoreItemId(scoreItemId) >= 0;
    }
    
    /**
     * 批量删除标准
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return mapper.deleteByIds(ids);
    }
    
    /**
     * 更新标准
     * 
     * @param criteria 标准
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCriteria(ScoreItemCriteria criteria) {
        if (criteria == null || criteria.getId() == null) {
            return false;
        }
        
        criteria.setUpdateTime(LocalDateTime.now());
        return mapper.updateByPrimaryKeySelective(criteria) > 0;
    }
    
    /**
     * 新增标准
     * 
     * @param criteria 标准
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean insertCriteria(ScoreItemCriteria criteria) {
        if (criteria == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        criteria.setCreateTime(now);
        criteria.setUpdateTime(now);
        
        return mapper.insertSelective(criteria) > 0;
    }
    
    /**
     * 根据ID删除标准
     * 
     * @param id 标准ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        if (id == null) {
            return false;
        }
        return mapper.deleteByPrimaryKey(id) > 0;
    }
}
