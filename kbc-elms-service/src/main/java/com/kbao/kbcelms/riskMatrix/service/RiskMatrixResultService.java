package com.kbao.kbcelms.riskMatrix.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskMatrix.entity.RiskMatrixResult;
import com.kbao.kbcelms.riskMatrix.dao.RiskMatrixResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 风险矩阵计算结果服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class RiskMatrixResultService extends BaseSQLServiceImpl<RiskMatrixResult, Long, RiskMatrixResultMapper> {
    
    /**
     * 根据矩阵ID查询结果列表
     * 
     * @param matrixId 矩阵ID
     * @return 结果列表
     */
    public List<RiskMatrixResult> getByMatrixId(Long matrixId) {
        return mapper.selectByMatrixId(matrixId);
    }
    
    /**
     * 根据企业ID查询结果列表
     * 
     * @param enterpriseId 企业ID
     * @return 结果列表
     */
    public List<RiskMatrixResult> getByEnterpriseId(Long enterpriseId) {
        return mapper.selectByEnterpriseId(enterpriseId);
    }
    
    /**
     * 根据矩阵ID和企业ID查询结果
     * 
     * @param matrixId 矩阵ID
     * @param enterpriseId 企业ID
     * @return 结果
     */
    public RiskMatrixResult getByMatrixIdAndEnterpriseId(Long matrixId, Long enterpriseId) {
        return mapper.selectByMatrixIdAndEnterpriseId(matrixId, enterpriseId);
    }
    
    /**
     * 保存计算结果
     * 
     * @param result 计算结果
     * @param currentUser 当前用户
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveResult(RiskMatrixResult result, String currentUser) {
        if (result == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 检查是否已存在相同矩阵和企业的结果
        RiskMatrixResult existing = mapper.selectByMatrixIdAndEnterpriseId(result.getMatrixId(), result.getEnterpriseId());
        
        if (existing != null) {
            // 更新现有记录
            result.setId(existing.getId());
            result.setUpdateTime(now);
            return mapper.updateByPrimaryKeySelective(result) > 0;
        } else {
            // 新增记录
            result.setCreateTime(now);
            result.setUpdateTime(now);
            result.setCreateUser(currentUser);
            return mapper.insertSelective(result) > 0;
        }
    }
    
    /**
     * 根据矩阵ID删除结果
     * 
     * @param matrixId 矩阵ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByMatrixId(Long matrixId) {
        if (matrixId == null) {
            return false;
        }
        return mapper.deleteByMatrixId(matrixId) >= 0;
    }
    
    /**
     * 根据企业ID删除结果
     * 
     * @param enterpriseId 企业ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByEnterpriseId(Long enterpriseId) {
        if (enterpriseId == null) {
            return false;
        }
        return mapper.deleteByEnterpriseId(enterpriseId) >= 0;
    }
    
    /**
     * 批量删除结果
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return mapper.deleteByIds(ids);
    }
    
    /**
     * 根据企业类型查询结果列表
     * 
     * @param enterpriseType 企业类型
     * @return 结果列表
     */
    public List<RiskMatrixResult> getByEnterpriseType(String enterpriseType) {
        return mapper.selectByEnterpriseType(enterpriseType);
    }
    
    /**
     * 根据风险等级查询结果列表
     * 
     * @param riskLevel 风险等级
     * @return 结果列表
     */
    public List<RiskMatrixResult> getByRiskLevel(String riskLevel) {
        return mapper.selectByRiskLevel(riskLevel);
    }
    
    /**
     * 更新结果
     * 
     * @param result 结果
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateResult(RiskMatrixResult result) {
        if (result == null || result.getId() == null) {
            return false;
        }
        
        result.setUpdateTime(LocalDateTime.now());
        return mapper.updateByPrimaryKeySelective(result) > 0;
    }
    
    /**
     * 新增结果
     * 
     * @param result 结果
     * @param currentUser 当前用户
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean insertResult(RiskMatrixResult result, String currentUser) {
        if (result == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        result.setCreateTime(now);
        result.setUpdateTime(now);
        result.setCreateUser(currentUser);
        
        return mapper.insertSelective(result) > 0;
    }
}
