package com.kbao.kbcelms.riskMatrix.dao;

import com.kbao.kbcelms.common.nosql.dao.TenantMongoDaoImpl;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixResultDTO;
import com.kbao.kbcelms.riskMatrix.model.RiskMatrixReportMongo;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 风险矩阵报告MongoDB DAO
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Repository
public class RiskMatrixReportDao extends TenantMongoDaoImpl<RiskMatrixReportMongo, String> {

    /**
     * 根据企业ID查询风险矩阵报告列表
     * 
     * @param enterpriseId 企业ID
     * @param tenantId 租户ID
     * @return 报告列表
     */
    public List<RiskMatrixReportMongo> findByEnterpriseId(Long enterpriseId, String tenantId) {
        Criteria criteria = Criteria.where("enterpriseId").is(enterpriseId)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        return this.find(query);
    }

    /**
     * 根据企业ID和报告状态查询风险矩阵报告列表
     * 
     * @param enterpriseId 企业ID
     * @param reportStatus 报告状态
     * @param tenantId 租户ID
     * @return 报告列表
     */
    public List<RiskMatrixReportMongo> findByEnterpriseIdAndStatus(Long enterpriseId, String reportStatus, String tenantId) {
        Criteria criteria = Criteria.where("enterpriseId").is(enterpriseId)
                .and("reportStatus").is(reportStatus)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        return this.find(query);
    }

    /**
     * 根据企业ID查询最新的风险矩阵报告
     * 
     * @param enterpriseId 企业ID
     * @param tenantId 租户ID
     * @return 最新报告
     */
    public RiskMatrixReportMongo findLatestByEnterpriseId(Long enterpriseId, String tenantId) {
        Criteria criteria = Criteria.where("enterpriseId").is(enterpriseId)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        query.with(org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Direction.DESC, "createTime"));
        query.limit(1);
        return this.findOne(query);
    }

    /**
     * 根据企业ID查询最新的风险矩阵
     *
     * @param enterpriseId 企业ID
     * @param tenantId 租户ID
     * @return 最新报告
     */
    public RiskMatrixReportMongo findRadarChartByEnterpriseId(Long enterpriseId, String tenantId) {
        Criteria criteria = Criteria.where("enterpriseId").is(enterpriseId)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        query.fields().include("radarChartDatas");
        query.with(org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Direction.DESC, "createTime"));
        query.limit(1);
        return this.findOne(query);
    }

    /**
     * 根据企业ID和企业类型查询风险矩阵报告列表
     * 
     * @param enterpriseId 企业ID
     * @param enterpriseType 企业类型
     * @param tenantId 租户ID
     * @return 报告列表
     */
    public List<RiskMatrixReportMongo> findByEnterpriseIdAndType(Long enterpriseId, String enterpriseType, String tenantId) {
        Criteria criteria = Criteria.where("enterpriseId").is(enterpriseId)
                .and("enterpriseType").is(enterpriseType)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        return this.find(query);
    }

    /**
     * 根据报告状态查询风险矩阵报告列表
     * 
     * @param reportStatus 报告状态
     * @param tenantId 租户ID
     * @return 报告列表
     */
    public List<RiskMatrixReportMongo> findByReportStatus(String reportStatus, String tenantId) {
        Criteria criteria = Criteria.where("reportStatus").is(reportStatus)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        return this.find(query);
    }

    /**
     * 根据创建人ID查询风险矩阵报告列表
     * 
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 报告列表
     */
    public List<RiskMatrixReportMongo> findByCreatorId(String creatorId, String tenantId) {
        Criteria criteria = Criteria.where("creatorId").is(creatorId)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        return this.find(query);
    }



    /**
     * 统计企业的风险矩阵报告数量
     * 
     * @param enterpriseId 企业ID
     * @param tenantId 租户ID
     * @return 报告数量
     */
    public long countByEnterpriseId(Long enterpriseId, String tenantId) {
        Criteria criteria = Criteria.where("enterpriseId").is(enterpriseId)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        return this.count(query);
    }
}
