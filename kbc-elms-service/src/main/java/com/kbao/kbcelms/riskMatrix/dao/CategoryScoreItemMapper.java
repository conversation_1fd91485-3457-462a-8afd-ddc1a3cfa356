package com.kbao.kbcelms.riskMatrix.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.riskMatrix.entity.CategoryScoreItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 类别与评分项关联表Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface CategoryScoreItemMapper extends BaseMapper<CategoryScoreItem, Long> {
    
    /**
     * 根据类别ID删除关联关系
     * 
     * @param categoryId 类别ID
     * @return 删除的记录数
     */
    int deleteByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 根据类别ID列表删除关联关系
     * 
     * @param categoryIds 类别ID列表
     * @return 删除的记录数
     */
    int deleteByCategoryIds(@Param("categoryIds") List<Long> categoryIds);
    
    /**
     * 根据评分项ID删除关联关系
     * 
     * @param scoreItemId 评分项ID
     * @return 删除的记录数
     */
    int deleteByScoreItemId(@Param("scoreItemId") Long scoreItemId);
    
    /**
     * 根据类别ID查询关联的评分项ID列表
     * 
     * @param categoryId 类别ID
     * @return 评分项ID列表
     */
    List<Long> selectScoreItemIdsByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 根据类别ID列表查询关联关系
     * 
     * @param categoryIds 类别ID列表
     * @return 关联关系列表
     */
    List<CategoryScoreItem> selectByCategoryIds(@Param("categoryIds") List<Long> categoryIds);
    
    /**
     * 批量插入关联关系
     * 
     * @param relations 关联关系列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("relations") List<CategoryScoreItem> relations);
}
