package com.kbao.kbcelms.util;

import com.kbao.kbcelms.user.vo.UserInfoVO;

/**
 * <AUTHOR>
 * @date 2025/8/8 10:28
 */
public class ElmsUserUtils {

    private static ThreadLocal<UserInfoVO> local = new ThreadLocal<>();


    public static void setUserInfo(UserInfoVO userInfo) {
        local.set(userInfo);
    }

    public static UserInfoVO getUserInfo() {
        return local.get();
    }

    public static void clearUserInfo() {
        local.remove();
    }
}
