package com.kbao.kbcelms.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 线程池工具类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Getter
@Slf4j
@Component
public class ThreadPoolUtils {

    /**
     * -- GETTER --
     *  获取通用异步线程池
     */
    @Autowired
    @Qualifier("asyncExecutor")
    private Executor asyncExecutor;

    /**
     * 并行执行任务列表
     * 
     * @param tasks 任务列表
     * @param executor 线程池
     * @param timeout 超时时间（秒）
     * @return 执行结果列表
     */
    public <T> List<T> executeParallel(List<Supplier<T>> tasks, Executor executor, long timeout) {
        if (tasks == null || tasks.isEmpty()) {
            return new ArrayList<>();
        }

        CompletableFuture<T>[] futures = tasks.stream()
                .map(task -> CompletableFuture.supplyAsync(task, executor))
                .toArray(CompletableFuture[]::new);

        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures);
            allOf.get(timeout, TimeUnit.SECONDS);

            List<T> results = new ArrayList<>();
            for (CompletableFuture<T> future : futures) {
                try {
                    results.add(future.get());
                } catch (Exception e) {
                    log.error("获取任务执行结果失败", e);
                    results.add(null);
                }
            }
            return results;

        } catch (TimeoutException e) {
            log.error("任务执行超时，超时时间: {}秒", timeout);
            // 取消未完成的任务
            for (CompletableFuture<T> future : futures) {
                future.cancel(true);
            }
            throw new RuntimeException("任务执行超时", e);
        } catch (Exception e) {
            log.error("并行任务执行失败", e);
            throw new RuntimeException("并行任务执行失败", e);
        }
    }

    /**
     * 并行处理数据列表
     * 
     * @param dataList 数据列表
     * @param processor 处理函数
     * @param executor 线程池
     * @param timeout 超时时间（秒）
     * @return 处理结果列表
     */
    public <T, R> List<R> processParallel(List<T> dataList, Function<T, R> processor, 
                                         Executor executor, long timeout) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Supplier<R>> tasks = dataList.stream()
                .map(data -> (Supplier<R>) () -> processor.apply(data))
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        return executeParallel(tasks, executor, timeout);
    }

    /**
     * 分批并行处理数据
     * 
     * @param dataList 数据列表
     * @param batchSize 批次大小
     * @param processor 处理函数
     * @param executor 线程池
     * @param timeout 超时时间（秒）
     * @return 处理结果列表
     */
    public <T, R> List<R> processBatchParallel(List<T> dataList, int batchSize, 
                                              Function<List<T>, List<R>> processor,
                                              Executor executor, long timeout) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        // 分批
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, dataList.size());
            batches.add(dataList.subList(i, end));
        }

        // 并行处理每个批次
        List<Supplier<List<R>>> tasks = batches.stream()
                .map(batch -> (Supplier<List<R>>) () -> processor.apply(batch))
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        List<List<R>> batchResults = executeParallel(tasks, executor, timeout);

        // 合并结果
        List<R> finalResults = new ArrayList<>();
        for (List<R> batchResult : batchResults) {
            if (batchResult != null) {
                finalResults.addAll(batchResult);
            }
        }

        return finalResults;
    }

    /**
     * 异步执行任务（不等待结果）
     * 
     * @param task 任务
     * @param executor 线程池
     */
    public void executeAsync(Runnable task, Executor executor) {
        CompletableFuture.runAsync(task, executor)
                .exceptionally(throwable -> {
                    log.error("异步任务执行失败", throwable);
                    return null;
                });
    }

    /**
     * 异步执行任务并返回Future
     * 
     * @param task 任务
     * @param executor 线程池
     * @return Future对象
     */
    public <T> CompletableFuture<T> submitAsync(Supplier<T> task, Executor executor) {
        return CompletableFuture.supplyAsync(task, executor);
    }

    /**
     * 等待所有Future完成
     * 
     * @param futures Future列表
     * @param timeout 超时时间（秒）
     */
    public static void waitForAll(List<CompletableFuture<?>> futures, long timeout) {
        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));
            allOf.get(timeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("等待任务完成失败", e);
            throw new RuntimeException("等待任务完成失败", e);
        }
    }
}
