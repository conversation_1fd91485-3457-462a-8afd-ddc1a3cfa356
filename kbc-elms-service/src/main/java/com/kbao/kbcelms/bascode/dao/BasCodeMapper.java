package com.kbao.kbcelms.bascode.dao;

import java.util.*;
import java.util.Map;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.bascode.entity.BasCode;import org.apache.ibatis.annotations.Param;
public interface BasCodeMapper  extends BaseMapper<BasCode, Integer> {

    public BasCode getByCode(@Param("code") String code);

    /**
     * 更新完整路径字段
     * @param basCode 需要更新的BasCode对象
     */
    void updateFullPath(BasCode basCode);
}