package com.kbao.kbcelms.roleauth.service;


import java.util.List;
import java.util.Map;

import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.roleauth.entity.RoleAuth;
import com.kbao.kbcelms.roleauth.dao.RoleAuthMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;


@Service
public class RoleAuthService extends BaseSQLServiceImpl<RoleAuth, Integer, RoleAuthMapper> {

    public List<RoleAuth> findByRoleId(Integer roleId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("roleId", roleId);
        return mapper.selectAll(params);
    }

    public void deleteByRoleId(Integer roleId) {
        mapper.deleteByRoleId(roleId);
    }
}
