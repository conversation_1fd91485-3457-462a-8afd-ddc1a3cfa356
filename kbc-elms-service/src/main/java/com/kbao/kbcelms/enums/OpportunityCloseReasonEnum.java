package com.kbao.kbcelms.enums;

/**
 * 机会关闭原因类型枚举
 * <AUTHOR>
 * @date 2025/1/27 15:13
 */
public enum OpportunityCloseReasonEnum {
    OPPORTUNITY_WON(1, "机会已成交"),
    OPPORTUNITY_FAILED(2, "机会推进失败"),
    INVALID_OPPORTUNITY(3, "无效机会");

    private final Integer code;
    private final String name;

    OpportunityCloseReasonEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    /**
     * 根据code获取枚举
     * @param code 原因类型编码
     * @return 对应的枚举
     */
    public static OpportunityCloseReasonEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OpportunityCloseReasonEnum reason : values()) {
            if (reason.getCode().equals(code)) {
                return reason;
            }
        }
        return null;
    }

    /**
     * 根据code获取name
     * @param code 原因类型编码
     * @return 原因类型名称
     */
    public static String getNameByCode(Integer code) {
        OpportunityCloseReasonEnum reason = getByCode(code);
        return reason != null ? reason.getName() : null;
    }

    /**
     * 检查code是否存在
     * @param code 原因类型编码
     * @return 是否存在
     */
    public static boolean containsCode(Integer code) {
        return getByCode(code) != null;
    }
} 