package com.kbao.kbcelms.enums;

/**
 * 机会状态枚举
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
public enum OpportunityStatusEnum {
    PENDING_SUBMIT(0, "待提交"),
    SUBMITTED(1, "已提交"),
    LOCKED(2, "锁定"),
    SUSPENDED(3, "暂停"),
    TERMINATED(4, "关闭");

    private final Integer code;
    private final String name;

    OpportunityStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    /**
     * 根据code获取枚举
     * @param code 状态编码
     * @return 对应的枚举
     */
    public static OpportunityStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OpportunityStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据code获取name
     * @param code 状态编码
     * @return 状态名称
     */
    public static String getNameByCode(Integer code) {
        OpportunityStatusEnum status = getByCode(code);
        return status != null ? status.getName() : null;
    }

    /**
     * 检查code是否存在
     * @param code 状态编码
     * @return 是否存在
     */
    public static boolean containsCode(Integer code) {
        return getByCode(code) != null;
    }
} 