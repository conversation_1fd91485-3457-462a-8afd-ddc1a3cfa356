//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.kbao.kbcelms.enums;

public enum ProcessDefineStatusEnum {
    TEMP(0, "草稿"),
    START(1, "启动"),
    STOP(2, "停止");

    private final Integer status;
    private final String msg;

    private ProcessDefineStatusEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }
    public Integer getStatus() {
        return this.status;
    }

    public String getMsg() {
        return this.msg;
    }
}
