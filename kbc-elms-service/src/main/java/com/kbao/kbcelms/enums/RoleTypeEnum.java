package com.kbao.kbcelms.enums;

/**
 * <AUTHOR>
 * @date 2025/7/22 17:18
 */
public enum RoleTypeEnum {

    OPPO_AGENT(0, "机会提交代理人"),
    BRANCH_GM(1, "分公司统筹"),
    HEAD_GM(2, "总公司统筹"),
    BRANCH_PM(3, "分公司项目经理"),
    HEAD_PM(4, "总公司项目经理"),
    BRANCH_EXPERT(5, "分公司专家"),
    HEAD_EXPERT(6, "总公司专家"),
    BRANCH_CLERK(7, "营业部内勤"),
    OTHER(99, "其他");

    private Integer code;

    private String name;

    RoleTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据code获取name
     * @param code 角色类型编码
     * @return 角色类型名称
     */
    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RoleTypeEnum roleType : RoleTypeEnum.values()) {
            if (roleType.getCode().equals(code)) {
                return roleType.getName();
            }
        }
        return null;
    }

    /**
     * 根据code获取枚举
     * @param code 角色类型编码
     * @return 角色类型枚举
     */
    public static RoleTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RoleTypeEnum roleType : RoleTypeEnum.values()) {
            if (roleType.getCode().equals(code)) {
                return roleType;
            }
        }
        return null;
    }
}
