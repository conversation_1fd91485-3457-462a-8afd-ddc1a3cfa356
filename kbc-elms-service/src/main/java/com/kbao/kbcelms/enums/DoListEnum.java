package com.kbao.kbcelms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机会配置枚举
 * 定义机会流程中的各种配置项
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DoListEnum {

    /**
     * 领取机会
     */
    RECEIVE_OPPORTUNITY("1", "领取机会", true),

    /**
     * 配置项目成员
     */
    CONFIGURE_TEAM_MEMBERS("2", "配置项目成员", true),

    /**
     * 配置项目分工/比例
     */
    CONFIGURE_TEAM_DIVISION("3", "配置项目分工/比例", true),

    /**
     * 确认分工/比例
     */
    CONFIRM_TEAM_DIVISION("4", "确认分工/比例", true),

    /**
     * 配置生态服务
     */
    CONFIGURE_ECOSYSTEM_SERVICE("5", "配置生态服务", true),

    /**
     * 维护投标信息
     */
    MAINTAIN_BID_INFO("6", "维护投标信息", true),

    /**
     * 上传标书文件
     */
    UPLOAD_BID_DOCUMENT("7", "上传标书文件", false),

    /**
     * 上传经济委托授权书文件
     */
    UPLOAD_ECONOMIC_AUTHORIZATION("8", "上传经济委托授权书文件", true),

    /**
     * 上传询价记录文件
     */
    UPLOAD_INQUIRY_RECORD("9", "上传询价记录文件", true),

    /**
     * 上传排分结果文件
     */
    UPLOAD_RANKING_RESULT("10", "上传排分结果文件", true),

    /**
     * 填写成交保单号
     */
    FILL_POLICY_NUMBER("11", "填写成交保单号", true),

    /**
     * 三次确认分工/比例
     */
    SECOND_CONFIRM_DIVISION("12", "二次确认分工/比例", true),

    /**
     * 关闭机会
     */
    CLOSE_OPPORTUNITY("13", "关闭机会", true),

    /**
     * 暂停机会
     */
    SUSPEND_OPPORTUNITY("14", "暂停机会", true),

    /**
     * 指派统筹
     */
    ASSIGN_COORDINATOR("15", "指派统筹", true),

    /**
     * 指派项目经理
     */
    ASSIGN_PROJECT_MANAGER("16", "指派项目经理", true);

    /**
     * 配置项值
     */
    private final String value;

    /**
     * 配置项名称
     */
    private final String name;

    /**
     * 是否必填
     */
    private final boolean required;

    /**
     * 根据值获取枚举
     *
     * @param value 配置项值
     * @return 对应的枚举
     */
    public static DoListEnum getByValue(String value) {
        for (DoListEnum config : values()) {
            if (config.getValue().equals(value)) {
                return config;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举
     *
     * @param name 配置项名称
     * @return 对应的枚举
     */
    public static DoListEnum getByName(String name) {
        for (DoListEnum config : values()) {
            if (config.getName().equals(name)) {
                return config;
            }
        }
        return null;
    }

    /**
     * 检查配置项是否必填
     *
     * @param value 配置项值
     * @return 是否必填
     */
    public static boolean isRequired(String value) {
        DoListEnum config = getByValue(value);
        return config != null && config.isRequired();
    }

    /**
     * 获取所有必填配置项
     *
     * @return 必填配置项数组
     */
    public static DoListEnum[] getRequiredConfigs() {
        return java.util.Arrays.stream(values())
                .filter(DoListEnum::isRequired)
                .toArray(DoListEnum[]::new);
    }

    /**
     * 获取所有非必填配置项
     *
     * @return 非必填配置项数组
     */
    public static DoListEnum[] getOptionalConfigs() {
        return java.util.Arrays.stream(values())
                .filter(config -> !config.isRequired())
                .toArray(DoListEnum[]::new);
    }
}
