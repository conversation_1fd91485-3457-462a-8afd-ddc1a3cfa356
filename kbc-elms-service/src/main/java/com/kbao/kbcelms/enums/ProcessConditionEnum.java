package com.kbao.kbcelms.enums;

/**
 * 流程条件枚举
 */
public enum ProcessConditionEnum {
    EMPLOYEE_WELFARE(1, "员工福利"),
    COMPREHENSIVE_PROTECTION(2, "综合保障");

    private final Integer condition;
    private final String msg;

    ProcessConditionEnum(Integer condition, String msg) {
        this.condition = condition;
        this.msg = msg;
    }

    public Integer getCondition() {
        return this.condition;
    }

    public String getMsg() {
        return this.msg;
    }
} 