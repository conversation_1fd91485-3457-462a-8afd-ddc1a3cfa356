package com.kbao.kbcelms.enums;

/**
 * 机会步骤枚举
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
public enum OpportunityStepEnum {
    FOLLOW_UP("1", "统筹跟进"),
    TEAM_UP("2", "立项组队"),
    RATIO_ALLOC("3", "分配比例"),
    BIDDING("4", "投标阶段"),
    AUTH("5", "客户授权"),
    INQUIRY("6", "询价阶段"),
    SCORING("7", "排分阶段"),
    DEAL("8", "成交出单"),
    SERVICE("9", "服务阶段"),
    NONE("", "");

    private final String value;
    private final String label;

    OpportunityStepEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return this.value;
    }

    public String getLabel() {
        return this.label;
    }

    /**
     * 根据value获取枚举
     * @param value 步骤值
     * @return 对应的枚举
     */
    public static OpportunityStepEnum getByValue(String value) {
        for (OpportunityStepEnum step : values()) {
            if (step.getValue().equals(value)) {
                return step;
            }
        }
        return NONE;
    }

    /**
     * 根据label获取枚举
     * @param label 步骤标签
     * @return 对应的枚举
     */
    public static OpportunityStepEnum getByLabel(String label) {
        for (OpportunityStepEnum step : values()) {
            if (step.getLabel().equals(label)) {
                return step;
            }
        }
        return NONE;
    }
}