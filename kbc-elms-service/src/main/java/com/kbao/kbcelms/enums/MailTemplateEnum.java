package com.kbao.kbcelms.enums;

import java.util.Map;

/**
 * 邮件模板枚举
 */
public enum MailTemplateEnum {

    //空模板
    NO_TEMPLATE("", "{}");

    private final String templateId; //统一消息平台备案后获得
    private final String content;

    private MailTemplateEnum(String key, String value) {
        this.templateId = key;
        this.content = value;
    }

    public static MailTemplateEnum getByTemplateId(String templateId) {
        MailTemplateEnum[] templates = MailTemplateEnum.values();
        for (MailTemplateEnum t : templates) {
            if (t.getTemplateId().equals(templateId)) {
                return t;
            }
        }
        return null;
    }

    public String getTemplateId() {
        return templateId;
    }

    public String getContent() {
        return content;
    }

    public String getContent(Map<String, String> params) {
        String smsContent = content;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (smsContent.contains("{" + key + "}")) {
                smsContent = smsContent.replace("{" + key + "}", value);
            }
        }
        return smsContent;
    }
}
