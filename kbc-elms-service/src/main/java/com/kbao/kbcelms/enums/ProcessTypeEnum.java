package com.kbao.kbcelms.enums;

/**
 * 流程类型枚举
 */
public enum ProcessTypeEnum {
    DEFAULT(1, "默认"),
    CUSTOM(2, "自定义");

    private final Integer type;
    private final String msg;

    ProcessTypeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return this.type;
    }

    public String getMsg() {
        return this.msg;
    }
} 