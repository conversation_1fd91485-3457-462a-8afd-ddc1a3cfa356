package com.kbao.kbcelms.enums;

/**
 * 执行事项枚举
 * <AUTHOR>
 * @date 2025/7/22 15:13
 */
public enum ExecutionItemEnum {
    ACCEPT_OPPORTUNITY("1", "领取机会", true),
    CONFIGURE_PROJECT_MEMBERS("2", "配置项目成员", true),
    CONFIGURE_PROJECT_DIVISION("3", "配置项目分工/比例", true),
    CONFIRM_DIVISION("4", "确认分工/比例", true),
    CONFIGURE_ECOSYSTEM_SERVICE("5", "配置生态服务", true),
    MAINTAIN_BIDDING_INFO("6", "维护投标信息", true),
    UPLOAD_BIDDING_DOCUMENT("7", "上传标书文件", false),
    UPLOAD_ECONOMIC_AUTHORIZATION("8", "上传经济委托授权书文件", true),
    UPLOAD_INQUIRY_RECORD("9", "上传询价记录文件", true),
    UPLOAD_SCORING_RESULT("10", "上传排分结果文件", true),
    FILL_POLICY_NUMBER("11", "填写成交保单号", true),
    SECOND_CONFIRM_DIVISION("12", "二次确认分工/比例", true),
    CLOSE_OPPORTUNITY("13", "关闭机会", true),
    SUSPEND_OPPORTUNITY("14", "暂停机会", true),
    ASSIGN_COORDINATOR("15", "指派统筹", true),
    ASSIGN_PROJECT_MANAGER("16", "指派项目经理", true);

    private final String value;
    private final String name;
    private final boolean required;

    ExecutionItemEnum(String value, String name, boolean required) {
        this.value = value;
        this.name = name;
        this.required = required;
    }

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return this.name;
    }

    public boolean isRequired() {
        return this.required;
    }

    /**
     * 根据value获取枚举
     * @param value 执行事项值
     * @return 对应的枚举
     */
    public static ExecutionItemEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (ExecutionItemEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据value获取name
     * @param value 执行事项值
     * @return 执行事项名称
     */
    public static String getNameByValue(String value) {
        ExecutionItemEnum item = getByValue(value);
        return item != null ? item.getName() : null;
    }

    /**
     * 根据value获取required
     * @param value 执行事项值
     * @return 是否必填
     */
    public static boolean isRequiredByValue(String value) {
        ExecutionItemEnum item = getByValue(value);
        return item != null ? item.isRequired() : false;
    }

    /**
     * 检查value是否存在
     * @param value 执行事项值
     * @return 是否存在
     */
    public static boolean containsValue(String value) {
        return getByValue(value) != null;
    }

    /**
     * 获取所有必填的执行事项
     * @return 必填执行事项列表
     */
    public static ExecutionItemEnum[] getRequiredItems() {
        return java.util.Arrays.stream(values())
            .filter(ExecutionItemEnum::isRequired)
            .toArray(ExecutionItemEnum[]::new);
    }

    /**
     * 获取所有非必填的执行事项
     * @return 非必填执行事项列表
     */
    public static ExecutionItemEnum[] getOptionalItems() {
        return java.util.Arrays.stream(values())
            .filter(item -> !item.isRequired())
            .toArray(ExecutionItemEnum[]::new);
    }
} 