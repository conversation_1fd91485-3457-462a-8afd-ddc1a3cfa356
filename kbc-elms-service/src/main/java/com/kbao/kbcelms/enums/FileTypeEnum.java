package com.kbao.kbcelms.enums;

/**
 * 文件类型枚举
 */
public enum FileTypeEnum {

    RESULT("result","排分结果文件",true),
    INQUIRY("inquiry","询价文件",true),
    BID("bid","标书文件",true),
    AUTH("auth","授权文件",true),
    UNKNOWN("unknown", "未知", true);


    private final String key;
    private final String value;
    private final Boolean isInternet;

    FileTypeEnum(String key, String value, Boolean isInternet) {
        this.key = key;
        this.value = value;
        this.isInternet = isInternet;
    }

    public static FileTypeEnum getByKey(String type) {
        FileTypeEnum[] types = FileTypeEnum.values();
        for (FileTypeEnum t : types) {
            if (t.getKey().equals(type)) {
                return t;
            }
        }
        return UNKNOWN;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public Boolean getIsInternet() {
        return isInternet;
    }
}
