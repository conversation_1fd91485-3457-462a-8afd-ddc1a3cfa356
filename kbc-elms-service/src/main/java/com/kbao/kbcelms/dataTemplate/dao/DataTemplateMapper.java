package com.kbao.kbcelms.dataTemplate.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.dataTemplate.bean.DataReqVo;
import com.kbao.kbcelms.dataTemplate.entity.DataTemplate;
import com.kbao.kbcelms.dataTemplate.model.DataTemplateField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Name
 * @since 2024-06-02
 */
@Mapper
public interface DataTemplateMapper extends BaseMapper<DataTemplate, Integer> {

    int isExistTemplateCode(@Param("bizCode") String bizCode, @Param("id") Integer id);

    int isExistTable(@Param("tableName") String tableName);

    int isExistData(@Param("tableName") String tableName);

    int ddlDropTable(@Param("tableName") String tableName);

    /**
     * 动态建表
     */
    int ddlCreateTable(@Param("tableName") String tableName,
                       @Param("fieldList") List<DataTemplateField> fieldList,
                       @Param("indexFields") List<String> indexFields,
                       @Param("tableComment") String tableComment);

    int ddlChangeTable(@Param("tableName") String tableName,
                        @Param("addFields") List<DataTemplateField> addFields,
                        @Param("addIndexFields") List<String> addIndexFields,
                        @Param("delIndexFields") List<String> delIndexFields);

    List<Map> getDataList(DataReqVo req);

    DataTemplate selectByBizCode(@Param("bizCode") String bizCode);

} 