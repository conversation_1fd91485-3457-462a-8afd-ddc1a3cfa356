package com.kbao.kbcelms.operationlog.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.common.nosql.service.TenantMongoServiceImpl;
import com.kbao.kbcelms.operationlog.dao.OperationLogDao;
import com.kbao.kbcelms.operationlog.entity.OperationLog;
import com.kbao.kbcelms.operationlog.dto.OperationLogQuery;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.IDUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.data.mongodb.core.query.Query;

/**
 * 操作日志服务类，提供与操作日志相关的业务逻辑操作，
 * 继承自 BaseMongoServiceImpl 类，可使用其提供的数据库操作方法。
 * <AUTHOR>
 */
@Service
public class OperationLogService  extends TenantMongoServiceImpl<OperationLog, String, OperationLogDao> {
    /**
     * 保存操作日志
     * @param operationLog 操作日志对象
     */
    public void saveOperationLog(OperationLog operationLog) {
        initLog(operationLog);
        this.save(operationLog);
    }

    //初始化日志
    public void initLog(OperationLog operationLog){
        operationLog.setId(IDUtils.generateBizId("OPLOG"));
        operationLog.setOperatorId(BscUserUtils.getUserId());
        operationLog.setOperatorName(BscUserUtils.getUser().getUser().getNickName());
        operationLog.setOperationTime(DateUtils.getCurrentDate());
        operationLog.setTenantId(BscUserUtils.getUserId());
    }

    /**
     * 翻页查询操作日志
     * @param requestPage
     * @return
     */
    public PageInfo<OperationLog> pageOperationLog(PageRequest<OperationLogQuery> requestPage) {
        Query query = new Query();
        Criteria criteria = new Criteria();

        // 获取查询参数
        OperationLogQuery param = requestPage.getParam();
        if (EmptyUtils.isNotEmpty(param)) {
            // 按opportunityId查询（判空）
            if (EmptyUtils.isNotEmpty(param.getOpportunityId())) {
                criteria.and("opportunityId").is(param.getOpportunityId());
            }

            // 按businessType查询（判空）
            if (EmptyUtils.isNotEmpty(param.getBusinessType())) {
                criteria.and("businessType").is(param.getBusinessType());
            }

            // 按operationType查询（判空）
            if (EmptyUtils.isNotEmpty(param.getOperationType())) {
                criteria.and("operationType").is(param.getOperationType());
            }

            // 按operatorId查询（判空）
            if (EmptyUtils.isNotEmpty(param.getOperatorId())) {
                criteria.and("operatorId").is(param.getOperatorId());
            }

            // 按changeRemark查询（判空）
            if (EmptyUtils.isNotEmpty(param.getChangeRemark())) {
                criteria.and("changeRemark").regex(param.getChangeRemark());
            }

            // 按operationTime查询（判空）
            if (EmptyUtils.isNotEmpty(param.getOperationStartTime())) {
                criteria.and("operationTime").gte(param.getOperationStartTime());
            }

            // 按operationTime查询（判空）
            if (EmptyUtils.isNotEmpty(param.getOperationEndTime())) {
                criteria.and("operationTime").lte(param.getOperationEndTime());
            }

            if(EmptyUtils.isNotEmpty(param.getOperationResult())){
                criteria.and("operationResult").is(param.getOperationResult());
            }
        }

        query.addCriteria(criteria);

        // 构建排序条件（按操作时间和创建时间降序）
        query.with(Sort.by(Sort.Order.desc("operationTime")));

        // 设置分页参数
        Pagination<OperationLog> pagination = new Pagination<>();
        pagination.setPageNum(requestPage.getPageNum());
        pagination.setPageSize(requestPage.getPageSize());

        return this.page(query, pagination);
    }
}