package com.kbao.kbcelms.userorg.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.userorg.entity.UserOrg;
import org.apache.ibatis.annotations.Param;

public interface UserOrgMapper extends BaseMapper<UserOrg, Integer> {

    UserOrg findUserOrgByCondition(@Param("userId") String userId, @Param("tenantId") String tenantId, @Param("organCode") String orgCode);

    void deleteByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);
}