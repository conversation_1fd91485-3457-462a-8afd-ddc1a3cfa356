package com.kbao.kbcelms.bsc;

import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.tool.util.SignUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class BscUtil {

    public static void setFeignRequestHeader(){
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("tenantId", ElmsContext.getTenantId());
        FeignRequestHeader.Header.set(headerMap);
    }

    public static void setFeignHeaderByUser(){
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("tenantId", ElmsContext.getTenantId());
        headerMap.put("token", ElmsContext.getUser().getToken());
        FeignRequestHeader.Header.set(headerMap);
    }

}
