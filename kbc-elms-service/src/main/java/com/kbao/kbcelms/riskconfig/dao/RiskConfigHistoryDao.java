package com.kbao.kbcelms.riskconfig.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.riskconfig.entity.RiskConfigHistory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 风险配置历史数据访问层
 * <AUTHOR>
 * @date 2025-08-11
 */
@Repository
public class RiskConfigHistoryDao extends BaseMongoDaoImpl<RiskConfigHistory, String> {

    /**
     * 根据配置ID查询历史记录
     */
    public List<RiskConfigHistory> findByConfigId(String configId, int pageNum, int pageSize) {
        Criteria criteria = Criteria.where("configId").is(configId);
        Query query = new Query(criteria);
        
        // 按创建时间倒序排列
        query.with(Sort.by(Sort.Direction.DESC, "createdTime"));
        
        // 分页
        query.skip((long) (pageNum - 1) * pageSize).limit(pageSize);
        
        return find(query);
    }

    /**
     * 统计配置历史记录总数
     */
    public long countByConfigId(String configId) {
        Criteria criteria = Criteria.where("configId").is(configId);
        Query query = new Query(criteria);
        return count(query);
    }

    /**
     * 根据版本号查询历史记录
     */
    public RiskConfigHistory findByConfigIdAndVersion(String configId, String version) {
        Criteria criteria = Criteria.where("configId").is(configId)
                .and("version").is(version);
        Query query = new Query(criteria);
        return findOne(query);
    }

    /**
     * 获取配置的最新版本号
     */
    public String getLatestVersion(String configId) {
        Criteria criteria = Criteria.where("configId").is(configId);
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.DESC, "createdTime"));
        query.fields().include("version");
        query.limit(1);
        
        RiskConfigHistory latest = findOne(query);
        return latest != null ? latest.getVersion() : null;
    }

    /**
     * 清理过期历史记录
     */
    public void cleanupOldHistories(Date cutoffDate) {
        Criteria criteria = Criteria.where("createdTime").lt(cutoffDate);
        Query query = new Query(criteria);
        remove(query);
    }

    /**
     * 根据变更类型统计
     */
    public long countByChangeType(String changeType, Date startTime, Date endTime) {
        Criteria criteria = Criteria.where("changeType").is(changeType);
        
        if (startTime != null) {
            criteria.and("createdTime").gte(startTime);
        }
        if (endTime != null) {
            criteria.and("createdTime").lte(endTime);
        }
        
        Query query = new Query(criteria);
        return count(query);
    }
}
