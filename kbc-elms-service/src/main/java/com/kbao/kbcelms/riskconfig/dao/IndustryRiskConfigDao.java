package com.kbao.kbcelms.riskconfig.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.riskconfig.entity.IndustryRiskConfig;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 行业风险配置数据访问层
 * <AUTHOR>
 * @date 2025-08-11
 */
@Repository
public class IndustryRiskConfigDao extends BaseMongoDaoImpl<IndustryRiskConfig, String> {

    /**
     * 根据行业编码查询配置
     */
    public IndustryRiskConfig findByIndustryCode(String industryCode, String tenantId) {
        Criteria criteria = Criteria.where("industryCode").is(industryCode)
                .and("deleted").is(false);
        
        if (StringUtils.hasText(tenantId)) {
            criteria.and("tenantId").is(tenantId);
        }
        
        Query query = new Query(criteria);
        return findOne(query);
    }

    /**
     * 检查一级行业编码是否存在
     *
     * @param industryLevel1Code 一级行业编码
     * @param excludeId 排除的ID（用于更新时排除自身）
     * @param tenantId 租户ID
     * @return true-存在，false-不存在
     */
    public boolean existsByIndustryLevel1Code(String industryLevel1Code, String excludeId, String tenantId) {
        // 参数验证
        if (!StringUtils.hasText(industryLevel1Code)) {
            return false;
        }

        try {
            Criteria criteria = Criteria.where("industryLevel1Code").is(industryLevel1Code.trim())
                    .and("deleted").is(false);

            // 排除指定ID（用于更新场景）
            if (StringUtils.hasText(excludeId)) {
                criteria.and("id").ne(excludeId.trim());
            }

            // 租户隔离
            if (StringUtils.hasText(tenantId)) {
                criteria.and("tenantId").is(tenantId.trim());
            }

            Query query = new Query(criteria);
            long count = count(query);

            return count > 0;
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("检查一级行业编码是否存在时发生错误: " + e.getMessage());
            // 发生异常时返回true，避免重复数据
            return true;
        }
    }

    /**
     * 检查一级行业编码是否存在（简化版本，不排除任何ID）
     *
     * @param industryLevel1Code 一级行业编码
     * @param tenantId 租户ID
     * @return true-存在，false-不存在
     */
    public boolean existsByIndustryLevel1Code(String industryLevel1Code, String tenantId) {
        return existsByIndustryLevel1Code(industryLevel1Code, null, tenantId);
    }

    /**
     * 检查一级行业编码是否存在（最简版本，使用当前租户）
     *
     * @param industryLevel1Code 一级行业编码
     * @return true-存在，false-不存在
     */
    public boolean existsByIndustryLevel1Code(String industryLevel1Code) {
        return existsByIndustryLevel1Code(industryLevel1Code, null, null);
    }

    /**
     * 根据一级行业编码查询配置
     */
    public IndustryRiskConfig findByIndustryLevel1Code(String industryLevel1Code, String tenantId) {
        Criteria criteria = Criteria.where("industryLevel1Code").is(industryLevel1Code)
                .and("deleted").is(false);

        if (StringUtils.hasText(tenantId)) {
            criteria.and("tenantId").is(tenantId);
        }

        Query query = new Query(criteria);
        return findOne(query);
    }

    /**
     * 分页查询配置列表
     */
    public List<IndustryRiskConfig> findByPage(String industryLevel1Code, String industryLevel1Name,
                                               String riskLevel, String status, String keyword,
                                               String tenantId, Date createdTimeStart, Date createdTimeEnd,
                                               String sortField, String sortDirection,
                                               int pageNum, int pageSize) {
        Criteria criteria = Criteria.where("deleted").is(false);

        // 一级行业编码
        if (StringUtils.hasText(industryLevel1Code)) {
            criteria.and("industryLevel1Code").is(industryLevel1Code);
        }

        // 一级行业名称模糊查询
        if (StringUtils.hasText(industryLevel1Name)) {
            criteria.and("industryLevel1Name").regex(industryLevel1Name, "i");
        }
        
        // 风险等级
        if (StringUtils.hasText(riskLevel)) {
            criteria.and("riskLevel").is(riskLevel);
        }
        
        // 状态
        if (StringUtils.hasText(status)) {
            criteria.and("status").is(status);
        }
        
        // 租户ID
        if (StringUtils.hasText(tenantId)) {
            criteria.and("tenantId").is(tenantId);
        }
        
        // 创建时间范围
        if (createdTimeStart != null || createdTimeEnd != null) {
            Criteria timeCriteria = Criteria.where("createdTime");
            if (createdTimeStart != null) {
                timeCriteria.gte(createdTimeStart);
            }
            if (createdTimeEnd != null) {
                timeCriteria.lte(createdTimeEnd);
            }
            criteria.andOperator(timeCriteria);
        }
        
        // 关键词搜索（支持一级行业名称、二级行业名称、矩阵描述、风险类型等）
        if (StringUtils.hasText(keyword)) {
            Criteria keywordCriteria = new Criteria().orOperator(
                Criteria.where("industryLevel1Name").regex(keyword, "i"),
                Criteria.where("industryLevel2Name").regex(keyword, "i"),
                Criteria.where("matrixDesc").regex(keyword, "i"),
                Criteria.where("matrixConfig.rowData.type").regex(keyword, "i"),
                Criteria.where("matrixConfig.rowData.description").regex(keyword, "i"),
                Criteria.where("toolsConfig.rowData.toolName").regex(keyword, "i"),
                Criteria.where("toolsConfig.rowData.description").regex(keyword, "i")
            );
            criteria.andOperator(keywordCriteria);
        }
        
        Query query = new Query(criteria);
        
        // 排序
        if (StringUtils.hasText(sortField)) {
            Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
            query.with(Sort.by(direction, sortField));
        } else {
            query.with(Sort.by(Sort.Direction.DESC, "createdTime"));
        }
        
        // 分页
        query.skip((long) (pageNum - 1) * pageSize).limit(pageSize);
        
        return find(query);
    }

    /**
     * 统计查询结果总数
     */
    public long countByPage(String industryLevel1Code, String industryLevel1Name,
                           String riskLevel, String status, String keyword,
                           String tenantId, Date createdTimeStart, Date createdTimeEnd) {
        Criteria criteria = Criteria.where("deleted").is(false);

        // 一级行业编码
        if (StringUtils.hasText(industryLevel1Code)) {
            criteria.and("industryLevel1Code").is(industryLevel1Code);
        }

        // 一级行业名称模糊查询
        if (StringUtils.hasText(industryLevel1Name)) {
            criteria.and("industryLevel1Name").regex(industryLevel1Name, "i");
        }
        
        // 风险等级
        if (StringUtils.hasText(riskLevel)) {
            criteria.and("riskLevel").is(riskLevel);
        }
        
        // 状态
        if (StringUtils.hasText(status)) {
            criteria.and("status").is(status);
        }
        
        // 租户ID
        if (StringUtils.hasText(tenantId)) {
            criteria.and("tenantId").is(tenantId);
        }
        
        // 创建时间范围
        if (createdTimeStart != null || createdTimeEnd != null) {
            Criteria timeCriteria = Criteria.where("createdTime");
            if (createdTimeStart != null) {
                timeCriteria.gte(createdTimeStart);
            }
            if (createdTimeEnd != null) {
                timeCriteria.lte(createdTimeEnd);
            }
            criteria.andOperator(timeCriteria);
        }
        
        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            Criteria keywordCriteria = new Criteria().orOperator(
                Criteria.where("industryName").regex(keyword, "i"),
                Criteria.where("matrixDesc").regex(keyword, "i"),
                Criteria.where("matrixConfig.rowData.type").regex(keyword, "i"),
                Criteria.where("matrixConfig.rowData.description").regex(keyword, "i"),
                Criteria.where("toolsConfig.rowData.toolName").regex(keyword, "i"),
                Criteria.where("toolsConfig.rowData.description").regex(keyword, "i")
            );
            criteria.andOperator(keywordCriteria);
        }
        
        Query query = new Query(criteria);
        return count(query);
    }

    /**
     * 根据风险等级统计数量
     */
    public long countByRiskLevel(String riskLevel, String tenantId) {
        Criteria criteria = Criteria.where("riskLevel").is(riskLevel)
                .and("deleted").is(false);
        
        if (StringUtils.hasText(tenantId)) {
            criteria.and("tenantId").is(tenantId);
        }
        
        Query query = new Query(criteria);
        return count(query);
    }

    /**
     * 获取所有行业编码列表
     */
    public List<IndustryRiskConfig> findAllIndustries(String tenantId) {
        Criteria criteria = Criteria.where("deleted").is(false);
        
        if (StringUtils.hasText(tenantId)) {
            criteria.and("tenantId").is(tenantId);
        }
        
        Query query = new Query(criteria);
        query.fields().include("industryCode", "industryName", "industryLevel", "status");
        query.with(Sort.by(Sort.Direction.ASC, "industryCode"));
        
        return find(query);
    }

    /**
     * 批量删除（软删除）
     */
    public void batchDelete(List<String> ids, String userId) {
        Criteria criteria = Criteria.where("id").in(ids);
        Query query = new Query(criteria);

        // 构建更新操作
        Update updateObj = new Update()
                .set("deleted", true)
                .set("updatedBy", userId)
                .set("updatedTime", new Date());

        update(query, updateObj);
    }
}
