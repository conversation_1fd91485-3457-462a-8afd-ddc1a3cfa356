package com.kbao.kbcelms.divisionratio.service;


import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.common.nosql.service.TenantMongoServiceImpl;
import com.kbao.kbcelms.divisionratio.dao.DivisionRatioDao;
import com.kbao.kbcelms.divisionratio.model.DivisionRatio;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.IDUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class DivisionRatioService extends TenantMongoServiceImpl<DivisionRatio, String, DivisionRatioDao> {
    /**
     * 新增
     * @param divisionRatio
     * @return
     */
    public DivisionRatio addDivisionRatio(DivisionRatio divisionRatio) {
        String ratioId = IDUtils.generateBizId("RATIO");
        divisionRatio.setRatioId(ratioId);
        divisionRatio.setCreateId(BscUserUtils.getUserId());
        divisionRatio.setUpdateId(BscUserUtils.getUserId());
        divisionRatio.setCreateTime(DateUtils.getCurrentDate());
        divisionRatio.setUpdateTime(DateUtils.getCurrentDate());
        save(divisionRatio);
        return divisionRatio;
    }

    /**
     * 更新
     * @param divisionRatio
     * @return
     */
    public DivisionRatio updateDivisionRatio(DivisionRatio divisionRatio) {
        DivisionRatio existing = findById(divisionRatio.getId());
        if (existing == null) {
            throw new RuntimeException("不存在该记录");
        }
        existing.setName(divisionRatio.getName());
        existing.setRatio(divisionRatio.getRatio());
        existing.setNumber(divisionRatio.getNumber());
        existing.setStatus(divisionRatio.getStatus());
        existing.setUpdateId(BscUserUtils.getUserId());
        existing.setUpdateTime(DateUtils.getCurrentDate());
        update(existing);
        return divisionRatio;
    }

    /**
     * 删除
     * @param id
     */
    public void deleteDivisionRatio(String id) {
        DivisionRatio existing = findById(id);
        if (existing == null) {
            throw new RuntimeException("不存在该记录");
        }
        remove(id);
    }

    /**
     * 批量删除
     * @param ids
     */
    public void deleteDivisionRatios(List<String> ids) {
        for (String id : ids) {
            deleteDivisionRatio(id);
        }
    }

    /**
     * 分页查询
     * @param pageRequest
     * @return
     */
    public PageInfo<DivisionRatio> pageDivisionRatio(PageRequest<DivisionRatio> pageRequest) {
        Query query = new Query();

        // 构建查询条件
        if(EmptyUtils.isNotEmpty(pageRequest.getParam())){
            DivisionRatio ratio = pageRequest.getParam();

            if(EmptyUtils.isNotEmpty(ratio.getName())){
                query.addCriteria(Criteria.where("name").regex(ratio.getName()));
            }
            if(EmptyUtils.isNotEmpty(ratio.getStatus())){
                query.addCriteria(Criteria.where("status").is(ratio.getStatus()));
            }
        }

        // 构建排序条件
        query.with(Sort.by(Sort.Direction.DESC, "updateTime"));

        Pagination<DivisionRatio> pagination = new Pagination<>();
        pagination.setPageNum(pageRequest.getPageNum());
        pagination.setPageSize(pageRequest.getPageSize());

        return page(query, pagination);
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    public DivisionRatio getDivisionRatio(String id) {
        return findById(id);
    }

    /**
     * 查询启用的比例
     * @return
     */
    public List<DivisionRatio> getDivisionRatioList() {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is("1"));
        return find(query);
    }
}
