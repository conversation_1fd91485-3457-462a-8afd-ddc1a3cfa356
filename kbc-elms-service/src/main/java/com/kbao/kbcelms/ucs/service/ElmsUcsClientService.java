package com.kbao.kbcelms.ucs.service;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.PlatformConfig;
import com.kbao.kbcucs.agent.model.AgentBaseVO;
import com.kbao.kbcucs.agent.model.AgentFullInfoVO;
import com.kbao.kbcucs.client.AgentApiClientService;
import com.kbao.kbcucs.client.AgentWebClientService;
import com.kbao.kbcucs.client.model.GetByAgentCodeReq;
import com.kbao.kbcucs.client.nonuser.AgentApiV2ClientService;
import com.kbao.kbcucs.client.nonuser.AgentWebV2ClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ElmsUcsClientService {
    @Autowired
    protected PlatformConfig platformConfig;

    protected boolean isWeb() {
        return platformConfig.isWeb();
    }


    @Autowired
    AgentWebV2ClientService agentWebV2ClientService;

    @Autowired
    AgentApiV2ClientService agentApiV2ClientService;

    @Autowired
    private AgentWebClientService agentWebClientService;

    private AgentApiClientService agentApiClientService;

    public Result<AgentBaseVO> getAgentInfoV2(GetByAgentCodeReq req){
        if (isWeb()) {
            return agentWebV2ClientService.getAgentInfo(req);
        }else{
            return agentApiV2ClientService.getAgentInfo(req);
        }
    }

    public Result<AgentFullInfoVO> getAgentFullInfo(String agentCode){
        if (isWeb()) {
            return agentWebClientService.getAgentFullInfo(agentCode);
        }else{
            return agentApiClientService.getAgentFullInfo(agentCode);
        }
    }
}
