package com.kbao.kbcelms.enterprise.base.service;

import com.alibaba.fastjson.JSONObject;import com.kbao.kbcelms.enterprise.base.bean.EnterpriseSearchVo;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.query.config.model.EnterpriseQueryConfig;
import com.kbao.kbcelms.enterprise.query.config.service.EnterpriseQueryConfigService;
import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseQueryRecord;import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseCreateRecordService;import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseQueryRecordService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcucs.context.RequestContext;
import com.kbao.kbcucs.user.model.UserInfoResp;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class EnterpriseApiService {
    @Autowired
    private TianyanchaService tianyanchaService;
    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;
    @Autowired
    private EnterpriseQueryConfigService enterpriseQueryConfigService;
    @Autowired
    private EnterpriseQueryRecordService enterpriseQueryRecordService;
    @Autowired
    private EnterpriseCreateRecordService enterpriseCreateRecordService;


    /**
     * 企业验真接口
     * @return 验真结果
     * errorCode: 1：当日查询次数已用完, 2-本月查询次数已用完, 4-未查询到数据
     */
    public Map<String, Object> verify(EnterpriseSearchVo vo) {
        Map<String, Object> result = new HashMap<>();
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();
        // 获取用户的查询限制配置
        EnterpriseQueryConfig config = enterpriseQueryConfigService.getQueryConfigByAgentCode(userInfo.getAgentCode());
        // 检查当日查询次数
        long todayCount = enterpriseQueryRecordService.getTodayQueryCount(userInfo.getAgentCode());
        if (todayCount >= config.getDailyLimit()) {
            result.put("errorCode", 1);
            result.put("dailyLimit", config.getDailyLimit());
            result.put("monthlyLimit", config.getMonthlyLimit());
            return result;
        }
        // 检查本月查询次数
        long monthCount = enterpriseQueryRecordService.getMonthQueryCount(userInfo.getAgentCode());
        if (monthCount >= config.getMonthlyLimit()) {
            result.put("errorCode", 2);
            result.put("monthlyLimit", config.getMonthlyLimit());
            return result;
        }
        //todo 检查行业限制

        boolean hasData = true;
        EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.queryByFullName(vo.getName());
        if (basicInfo == null
            || LocalDateTime.fromDateFields(basicInfo.getUpdateTime()).plusYears(1).isBefore(LocalDateTime.now())) {
            hasData = false;
            basicInfo = tianyanchaService.syncEnterpriseAllInfo(vo.getName());
        }
        
        // 记录验真查询日志
        String queryRecordId;
        GenAgentEnterprise enterprise = null;
        if (basicInfo == null) {
            queryRecordId = enterpriseQueryRecordService.recordQueryLog(vo.getName(), userInfo, null, hasData, false);
            result.put("errorCode", 4);
        } else {
            enterprise = genAgentEnterpriseService.toGenAgentEnterprise(vo.getAgentEnterpriseId(), basicInfo);
            queryRecordId = enterpriseQueryRecordService.recordQueryLog(vo.getName(), userInfo, enterprise, hasData, false);
            enterprise.setQueryRecordId(queryRecordId);
            result.put("data", enterprise);
        }
        if (vo.getAgentEnterpriseId() == null) {
            enterpriseCreateRecordService.recordCreateLog(vo.getName(), userInfo, null, basicInfo, hasData, queryRecordId, false);
        }
        return result;
    }

    public JSONObject getVerifyData(String queryRecordId) {
        EnterpriseQueryRecord queryRecord = enterpriseQueryRecordService.findById(queryRecordId);
        if (queryRecord == null) {
            return null;
        }
        return queryRecord.getResultData();
    }
}
