package com.kbao.kbcelms.enterprise.type.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;
import org.springframework.data.domain.Sort;import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 企业类型数据访问层
 * <AUTHOR>
 * @date 2025-07-28
 */
@Repository
public class EnterpriseTypeDao extends BaseMongoDaoImpl<EnterpriseType, String> {
    
    /**
     * 根据编码查询企业类型
     * @param code 企业类型编码
     * @return 企业类型
     */
    public EnterpriseType findByCode(String code) {
        Query query = new Query(Criteria.where("code").is(code));
        return mongoTemplate.findOne(query, EnterpriseType.class);
    }
    
    /**
     * 根据编码查询企业类型（排除指定ID）
     * @param code 企业类型编码
     * @param excludeId 排除的ID
     * @return 企业类型
     */
    public EnterpriseType findByCodeExcludeId(String code, String excludeId) {
        Criteria criteria = Criteria.where("code").is(code);
        if (StringUtils.hasText(excludeId)) {
            criteria.and("id").ne(excludeId);
        }
        Query query = new Query(criteria);
        return mongoTemplate.findOne(query, EnterpriseType.class);
    }
}
