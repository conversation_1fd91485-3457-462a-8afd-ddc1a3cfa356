package com.kbao.kbcelms.enterprise.base.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseShareholder;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 企业股东信息DAO
 * <AUTHOR>
 * @date 2025-07-31
 */
@Repository
public class EnterpriseShareholderDao extends BaseMongoDaoImpl<EnterpriseShareholder, String> {

    /**
     * 根据统一社会信用代码查询企业股东信息
     * @param creditCode 统一社会信用代码
     * @param tenantId 租户ID
     * @return 企业股东信息列表
     */
    public List<EnterpriseShareholder> findByCreditCode(String creditCode, String tenantId) {
        Criteria criteria = Criteria.where("creditCode").is(creditCode)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        return this.find(query);
    }
    
    /**
     * 根据统一社会信用代码删除企业股东信息
     * @param creditCode 统一社会信用代码
     * @param tenantId 租户ID
     */
    public void deleteByCreditCode(String creditCode, String tenantId) {
        Criteria criteria = Criteria.where("creditCode").is(creditCode)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        this.remove(query);
    }
}
