package com.kbao.kbcelms.enterprise.base.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;import java.util.List;

/**
 * 企业基本信息DAO
 * <AUTHOR>
 * @date 2025-07-31
 */
@Repository
public class EnterpriseBasicInfoDao extends BaseMongoDaoImpl<EnterpriseBasicInfo, String> {

    /**
     * 根据统一社会信用代码查询企业基本信息
     * @param creditCode 统一社会信用代码
     * @param tenantId 租户ID
     * @return 企业基本信息
     */
    public EnterpriseBasicInfo findByCreditCode(String creditCode, String tenantId) {
        Criteria criteria = Criteria.where("creditCode").is(creditCode)
                .and("tenantId").is(tenantId);
        Query query = new Query().addCriteria(criteria);
        return this.findOne(query);
    }
    
    /**
     * 根据企业名称查询企业基本信息
     * @return 企业基本信息
     */
    public List<EnterpriseBasicInfo> searchByName(String name) {
        String tenantId = ElmsContext.getTenantId();
        Criteria criteria = Criteria.where("name").regex(name)
                .and("tenantId").is(tenantId);
        Query query = Query.query(criteria).limit(10);
        query.fields().include("name", "creditCode");
        return this.find(query);
    }

    public EnterpriseBasicInfo queryByFullName(String name) {
        String tenantId = ElmsContext.getTenantId();
        Criteria criteria = Criteria.where("name").is(name)
                .and("tenantId").is(tenantId);
        Query query = Query.query(criteria);
        return this.findOne(query);
    }
}
