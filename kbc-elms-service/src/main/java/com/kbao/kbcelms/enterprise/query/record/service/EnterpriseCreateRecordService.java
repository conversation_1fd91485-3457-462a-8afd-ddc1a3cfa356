package com.kbao.kbcelms.enterprise.query.record.service;

import com.alibaba.fastjson.JSON;import com.alibaba.fastjson.JSONObject;import com.kbao.commons.snowflake.IdWorker;import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;import com.kbao.kbcelms.enterprise.query.record.dao.EnterpriseCreateRecordDao;
import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseCreateRecord;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;import com.kbao.kbcucs.user.model.UserInfoResp;import org.springframework.data.mongodb.core.query.Criteria;import org.springframework.data.mongodb.core.query.Query;import org.springframework.stereotype.Service;import java.util.Date;

/**
 * 企业创建信息记录业务逻辑层
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
public class EnterpriseCreateRecordService extends BaseMongoServiceImpl<EnterpriseCreateRecord, String, EnterpriseCreateRecordDao> {


    public String recordCreateLog(String enterpriseName, UserInfoResp userInfo, GenAgentEnterprise enterprise, EnterpriseBasicInfo basicInfo
        , boolean hasData, String queryRecordId, boolean isBlocked) {
        Date currentTime = new Date();
        EnterpriseCreateRecord record = new EnterpriseCreateRecord();
        record.setId(IdWorker.get32UUID());
        record.setAgentCode(userInfo.getAgentCode());
        record.setAgentName(userInfo.getAgentName());
        record.setInputEnterpriseName(enterpriseName);
        record.setHasData(hasData);
        boolean isVerify = queryRecordId != null;
        record.setIsVerify(isVerify);
        record.setQueryRecordId(queryRecordId);
        record.setIsBlocked(isBlocked);
        if (enterprise != null) {
            JSONObject inputDate = JSONObject.parseObject(JSON.toJSONString(enterprise));
            record.setInputData(inputDate);
        }
        JSONObject thirdPartyData = new JSONObject();
        if (basicInfo != null) {
            thirdPartyData.put("name", basicInfo.getName());
            thirdPartyData.put("creditCode", basicInfo.getCreditCode());
            thirdPartyData.put("minCategoryCode", basicInfo.getMinCategoryCode());
            thirdPartyData.put("districtCode", basicInfo.getDistrictCode());
            thirdPartyData.put("city", basicInfo.getCity());
            thirdPartyData.put("latestBusinessIncome", basicInfo.getLatestBusinessIncome());
            thirdPartyData.put("staffNumRange", basicInfo.getStaffNumRange());
        }
        record.setCreateTime(currentTime);
        // 设置机构和营业部信息（需要从用户信息中获取）
        record.setTenantId(ElmsContext.getTenantId());
        this.dao.save(record);
        return record.getId();
    }

    public EnterpriseCreateRecord findByQueryId(String recordQueryId) {
        Query query = Query.query(Criteria.where("recordQueryId").is(recordQueryId));
        return this.dao.findOne(query);
    }
}
