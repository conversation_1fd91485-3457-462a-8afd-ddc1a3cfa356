package com.kbao.kbcelms.enterprise.query.record.service;

import com.alibaba.fastjson.JSON;import com.alibaba.fastjson.JSONObject;import com.github.pagehelper.PageInfo;import com.kbao.commons.snowflake.IdWorker;import com.kbao.commons.web.PageRequest;import com.kbao.kbcbsc.model.Pagination;import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;import com.kbao.kbcelms.enterprise.query.record.bean.EnterpriseCreateRecordVO;import com.kbao.kbcelms.enterprise.query.record.dao.EnterpriseCreateRecordDao;
import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseCreateRecord;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;import com.kbao.kbcucs.user.model.UserInfoResp;import com.kbao.tool.util.SysLoginUtils;import org.springframework.data.domain.Sort;import org.springframework.data.mongodb.core.query.Criteria;import org.springframework.data.mongodb.core.query.Query;import org.springframework.stereotype.Service;import org.springframework.util.StringUtils;import java.util.Date;

/**
 * 企业创建信息记录业务逻辑层
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
public class EnterpriseCreateRecordService extends BaseMongoServiceImpl<EnterpriseCreateRecord, String, EnterpriseCreateRecordDao> {

    /**
     * 分页查询企业创建记录
     * @param pageRequest 分页请求参数
     * @return 分页结果
     */
    public PageInfo<EnterpriseCreateRecord> pageCreateRecords(PageRequest<EnterpriseCreateRecordVO> pageRequest) {
        String tenantId = SysLoginUtils.getUser().getTenantId();

        // 构建查询条件
        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);

        EnterpriseCreateRecordVO param = pageRequest.getParam();
        if (param != null) {
            // 代理人姓名模糊查询
            if (StringUtils.hasText(param.getAgentName())) {
                criteria.and("agentName").regex(param.getAgentName(), "i");
            }

            // 代理人编码模糊查询
            if (StringUtils.hasText(param.getAgentCode())) {
                criteria.and("agentCode").regex(param.getAgentCode(), "i");
            }

            // 企业名称模糊查询
            if (StringUtils.hasText(param.getEnterpriseName())) {
                criteria.and("inputEnterpriseName").regex(param.getEnterpriseName(), "i");
            }

            // 所属行业查询 - 从inputData.categoryName中查询
            if (StringUtils.hasText(param.getCategoryName())) {
                criteria.and("inputData.categoryName").regex(param.getCategoryName(), "i");
            }

            // 城市查询 - 从inputData.city中查询
            if (StringUtils.hasText(param.getCity())) {
                criteria.and("inputData.city").regex(param.getCity(), "i");
            }

            // 时间范围查询
            if (param.getStartTime() != null && param.getEndTime() != null) {
                criteria.and("createTime").gte(param.getStartTime()).lte(param.getEndTime());
            } else if (param.getStartTime() != null) {
                criteria.and("createTime").gte(param.getStartTime());
            } else if (param.getEndTime() != null) {
                criteria.and("createTime").lte(param.getEndTime());
            }
        }

        Query query = new Query(criteria);

        // 按创建时间倒序排列
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));

        // 分页查询
        Pagination<EnterpriseCreateRecord> pagination = new Pagination<>(pageRequest.getPageNum(), pageRequest.getPageSize(), "createTime desc");
        return super.page(query, pagination);
    }

    public String recordCreateLog(String enterpriseName, UserInfoResp userInfo, GenAgentEnterprise enterprise, EnterpriseBasicInfo basicInfo
        , boolean hasData, String queryRecordId, boolean isBlocked) {
        Date currentTime = new Date();
        EnterpriseCreateRecord record = new EnterpriseCreateRecord();
        record.setId(IdWorker.get32UUID());
        record.setAgentCode(userInfo.getAgentCode());
        record.setAgentName(userInfo.getAgentName());
        record.setInputEnterpriseName(enterpriseName);
        record.setHasData(hasData);
        boolean isVerify = queryRecordId != null;
        record.setIsVerify(isVerify);
        record.setQueryRecordId(queryRecordId);
        record.setIsBlocked(isBlocked);
        if (enterprise != null) {
            JSONObject inputDate = JSONObject.parseObject(JSON.toJSONString(enterprise));
            record.setInputData(inputDate);
        }
        JSONObject thirdPartyData = new JSONObject();
        if (basicInfo != null) {
            thirdPartyData.put("name", basicInfo.getName());
            thirdPartyData.put("creditCode", basicInfo.getCreditCode());
            thirdPartyData.put("minCategoryCode", basicInfo.getMinCategoryCode());
            thirdPartyData.put("districtCode", basicInfo.getDistrictCode());
            thirdPartyData.put("city", basicInfo.getCity());
            thirdPartyData.put("latestBusinessIncome", basicInfo.getLatestBusinessIncome());
            thirdPartyData.put("staffNumRange", basicInfo.getStaffNumRange());
        }
        record.setCreateTime(currentTime);
        // 设置机构和营业部信息（需要从用户信息中获取）
        record.setTenantId(ElmsContext.getTenantId());
        this.dao.save(record);
        return record.getId();
    }

    public EnterpriseCreateRecord findByQueryId(String recordQueryId) {
        Query query = Query.query(Criteria.where("recordQueryId").is(recordQueryId));
        return this.dao.findOne(query);
    }
}
