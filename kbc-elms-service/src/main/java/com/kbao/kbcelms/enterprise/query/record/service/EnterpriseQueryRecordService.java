package com.kbao.kbcelms.enterprise.query.record.service;

import com.alibaba.fastjson.JSON;import com.alibaba.fastjson.JSONObject;import com.github.pagehelper.PageInfo;import com.kbao.commons.snowflake.IdWorker;import com.kbao.commons.web.PageRequest;import com.kbao.kbcbsc.model.Pagination;import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;import com.kbao.kbcelms.enterprise.query.config.bean.QueryUsageStatisticsVO;import com.kbao.kbcelms.enterprise.query.record.bean.EnterpriseQueryRecordVO;
import com.kbao.kbcelms.enterprise.query.record.dao.EnterpriseQueryRecordDao;
import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseQueryRecord;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;import com.kbao.kbcucs.user.model.UserInfoResp;import com.kbao.tool.util.SysLoginUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.*;import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;import java.time.LocalTime;import java.time.ZoneId;import java.util.Date;import java.util.List;
import java.util.Map;import java.util.stream.Collectors;

/**
 * 企业查询记录业务逻辑层
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class EnterpriseQueryRecordService extends BaseMongoServiceImpl<EnterpriseQueryRecord, String, EnterpriseQueryRecordDao> {

    /**
     * 分页查询企业查询记录
     * @return 分页结果
     */
    public PageInfo<EnterpriseQueryRecord> pageQueryRecords(PageRequest<EnterpriseQueryRecordVO> pageRequest) {

        String tenantId = SysLoginUtils.getUser().getTenantId();

        // 构建查询条件
        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(tenantId);
        EnterpriseQueryRecordVO param = pageRequest.getParam();
        if (StringUtils.hasText(param.getAgentName())) {
            criteria.and("agentName").regex(param.getAgentName(), "i");
        }
        
        if (StringUtils.hasText(param.getEnterpriseName())) {
            criteria.orOperator(
                Criteria.where("inputEnterpriseName").regex(param.getEnterpriseName(), "i"),
                Criteria.where("enterpriseName").regex(param.getEnterpriseName(), "i")
            );
        }

        Query query = new Query(criteria);

        // 按创建时间倒序排列
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));

        // 分页查询
        Pagination<EnterpriseQueryRecord> pagination = new Pagination<>(pageRequest.getPageNum(), pageRequest.getPageSize(), "queryTime desc");
        return super.page(new Query(criteria), pagination);
    }

    /**
     * 从查询记录表统计使用情况
     */
    public PageInfo<QueryUsageStatisticsVO> getUsageStatistics(String tenantId, String agentName, String agentCode, int pageNum, int pageSize) {
        // 构建匹配条件
        Criteria matchCriteria = new Criteria();
        matchCriteria.and("tenantId").is(tenantId);

        if (StringUtils.hasText(agentName)) {
            matchCriteria.and("agentName").regex(agentName, "i");
        }

        if (StringUtils.hasText(agentCode)) {
            matchCriteria.and("agentCode").regex(agentCode, "i");
        }

        // 获取今天和本月的时间范围
        Date[] todayRange = getTodayRange();
        Date[] monthRange = getThisMonthRange();

        // 聚合查询
        MatchOperation matchOperation = Aggregation.match(matchCriteria);
        GroupOperation groupOperation = Aggregation.group("agentCode", "agentName", "legalName", "tradingCenterName")
                .count().as("totalCount")
                .sum(ConditionalOperators.when(
                    Criteria.where("queryTime").gte(todayRange[0]).lte(todayRange[1])
                ).then(1).otherwise(0)).as("todayUsage")
                .sum(ConditionalOperators.when(
                    Criteria.where("queryTime").gte(monthRange[0]).lte(monthRange[1])
                ).then(1).otherwise(0)).as("monthUsage");

        // 先获取总数
        Aggregation countAggregation = Aggregation.newAggregation(matchOperation, groupOperation);
        AggregationResults<Map> countResults = dao.getMongoTemplate()
                .aggregate(countAggregation, "enterprise_query_record", Map.class);
        long total = countResults.getMappedResults().size();

        // 添加排序（按工号正序）
        SortOperation sortOperation = Aggregation.sort(Sort.Direction.ASC, "_id.agentCode");

        // 添加分页
        SkipOperation skipOperation = Aggregation.skip((long) (pageNum - 1) * pageSize);
        LimitOperation limitOperation = Aggregation.limit(pageSize);

        Aggregation aggregation = Aggregation.newAggregation(
            matchOperation, groupOperation, sortOperation, skipOperation, limitOperation
        );

        AggregationResults<JSONObject> results = dao.getMongoTemplate()
                .aggregate(aggregation, "enterprise_query_record", JSONObject.class);

        List<QueryUsageStatisticsVO> list = results.getMappedResults().stream().map(this::convertToStatisticsVO)
                .collect(Collectors.toList());

        // 构建分页结果
        PageInfo<QueryUsageStatisticsVO> pageInfo = new PageInfo<>();
        pageInfo.setList(list);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((int) Math.ceil((double) total / pageSize));
        return pageInfo;
    }


    /**
     * 获取今天的时间范围
     */
    private Date[] getTodayRange() {
        LocalDate today = LocalDate.now();
        Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfDay = Date.from(today.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        return new Date[]{startOfDay, endOfDay};
    }

    /**
     * 获取本月的时间范围
     */
    private Date[] getThisMonthRange() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        LocalDate lastDayOfMonth = today.withDayOfMonth(today.lengthOfMonth());

        Date startOfMonth = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfMonth = Date.from(lastDayOfMonth.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        return new Date[]{startOfMonth, endOfMonth};
    }



    /**
     * 获取用户当日查询次数
     * @param agentCode 顾问工号
     * @return 当日查询次数
     */
    public long getTodayQueryCount(String agentCode) {
        String tenantId = ElmsContext.getTenantId();
        Date[] todayRange = getTodayRange();
        Criteria criteria = Criteria.where("tenantId").is(tenantId)
            .and("agentCode").is(agentCode)
            .and("queryTime").gte(todayRange[0]).lte(todayRange[1])
            .and("isVerified").is(true);
        Query query = new Query(criteria);
        return this.dao.count(query);
    }

    /**
     * 获取用户本月查询次数
     * @param agentCode 顾问工号
     * @return 本月查询次数
     */
    public long getMonthQueryCount(String agentCode) {
        String tenantId = ElmsContext.getTenantId();
        Date[] monthRange = getThisMonthRange();
        Criteria criteria = Criteria.where("tenantId").is(tenantId)
            .and("agentCode").is(agentCode)
            .and("queryTime").gte(monthRange[0]).lte(monthRange[1])
            .and("isVerified").is(true);
        Query query = new Query(criteria);
        return this.dao.count(query);
    }

    /**
     * 记录查询日志
     * @param enterpriseName 输入的企业名称
     */
    public String recordQueryLog(String enterpriseName, UserInfoResp userInfo, GenAgentEnterprise enterprise
        , boolean hasData, boolean isBlocked) {
        Date currentTime = new Date();
        EnterpriseQueryRecord record = new EnterpriseQueryRecord();
        record.setId(IdWorker.get32UUID());
        record.setAgentCode(userInfo.getAgentCode());
        record.setAgentName(userInfo.getAgentName());
        record.setInputEnterpriseName(enterpriseName);
        record.setVerifyTime(currentTime);
        record.setHasData(hasData);
        record.setIsBlocked(isBlocked);
        if (enterprise != null) {
            JSONObject resultData = JSONObject.parseObject(JSON.toJSONString(enterprise));
            record.setResultData(resultData);
        }
        // 设置机构和营业部信息（需要从用户信息中获取）
        record.setLegalName(userInfo.getLegalName());
        record.setLegalCode(userInfo.getLegalCode());
        record.setTradingCenterName(userInfo.getTradingCenterName());
        record.setTradingCenterCode(userInfo.getTradingCenterCode());
        record.setTenantId(ElmsContext.getTenantId());
        this.dao.save(record);
        return record.getId();
    }

    /**
     * 转换聚合结果为统计VO
     */
    private QueryUsageStatisticsVO convertToStatisticsVO(JSONObject result) {
        QueryUsageStatisticsVO vo = new QueryUsageStatisticsVO();

        JSONObject id = result.getJSONObject("_id");
        vo.setAgentCode(id.getString("agentCode"));
        vo.setAgentName(id.getString("agentName"));
        vo.setLegalName(id.getString("legalName"));
        vo.setTradingCenterName(id.getString("tradingCenterName"));

        vo.setTodayUsage(result.getLong("todayUsage"));
        vo.setMonthUsage(result.getLong("monthUsage"));

        return vo;
    }
}
