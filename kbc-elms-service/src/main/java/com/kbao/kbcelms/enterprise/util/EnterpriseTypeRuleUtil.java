package com.kbao.kbcelms.enterprise.util;

import com.kbao.kbcelms.enterprise.type.model.EnterpriseTypeRule;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业类型规则工具类
 * <AUTHOR>
 * @date 2025-07-28
 */
public class EnterpriseTypeRuleUtil {
    
    /**
     * 生成员工规模范围展示文本
     * @return 员工规模范围文本
     */
    public static String generateEmployeeRangeText(EnterpriseTypeRule rule) {
        return generateRangeText(rule, "人");
    }

    /**
     * 生成营收规模范围展示文本
     * @return 营收规模范围文本
     */
    public static String generateRevenueRangeText(EnterpriseTypeRule rule) {
        return generateRangeText(rule, "元");
    }
    
    /**
     * 生成范围展示文本
     * @param unit 单位
     * @return 范围展示文本，格式：>=最小值<最大值
     */
    private static String generateRangeText(EnterpriseTypeRule rule, String unit) {
        Long minValue = rule.getMinValue();
        Long maxValue = rule.getMaxValue();

        StringBuilder sb = new StringBuilder();

        // 处理最小值
        if (minValue != null) {
            sb.append("≥").append(formatValue(minValue, unit));
        }

        // 处理最大值
        if (maxValue != null) {
            if (sb.length() > 0) {
                sb.append("");
            }
            sb.append("<").append(formatValue(maxValue, unit));
        }

        return sb.length() > 0 ? sb.toString() : "-";
    }

    /**
     * 格式化数值显示，支持万、亿的自然转换
     * @param value 数值
     * @param unit 单位
     * @return 格式化后的文本
     */
    private static String formatValue(Long value, String unit) {
        if ("元".equals(unit)) {
            // 营收规模自动转换单位
            if (value >= 100000000L) { // 1亿
                DecimalFormat df = new DecimalFormat("#.##");
                double yiValue = value / 100000000.0;
                return df.format(yiValue) + "亿元";
            } else if (value >= 10000L) { // 1万
                DecimalFormat df = new DecimalFormat("#.##");
                double wanValue = value / 10000.0;
                return df.format(wanValue) + "万元";
            } else {
                return value + "元";
            }
        } else {
            // 人员规模直接显示
            return value + unit;
        }
    }

}
