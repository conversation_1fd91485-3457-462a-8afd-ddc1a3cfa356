package com.kbao.kbcelms.enterprise.type.service;

import com.kbao.kbcelms.enterprise.type.bean.EnterpriseScaleEnumVO;
import com.kbao.kbcelms.enterprise.type.bean.ScaleEnumItem;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业类型服务
 * <AUTHOR>
 */
@Service
public class EnterpriseTypeService {
    
    /**
     * 获取企业规模枚举
     * @return 企业规模枚举VO
     */
    public EnterpriseScaleEnumVO getEnterpriseScaleEnum() {
        List<ScaleEnumItem> employeeScales = new ArrayList<>();
        employeeScales.add(new ScaleEnumItem("1", "1-10人"));
        employeeScales.add(new ScaleEnumItem("2", "11-50人"));
        employeeScales.add(new ScaleEnumItem("3", "51-100人"));
        employeeScales.add(new ScaleEnumItem("4", "101-300人"));
        employeeScales.add(new ScaleEnumItem("5", "301-500人"));
        employeeScales.add(new ScaleEnumItem("6", "500人以上"));
        
        List<ScaleEnumItem> revenueScales = new ArrayList<>();
        revenueScales.add(new ScaleEnumItem("1", "100万以下"));
        revenueScales.add(new ScaleEnumItem("2", "100万-500万"));
        revenueScales.add(new ScaleEnumItem("3", "500万-1000万"));
        revenueScales.add(new ScaleEnumItem("4", "1000万-5000万"));
        revenueScales.add(new ScaleEnumItem("5", "5000万-1亿"));
        revenueScales.add(new ScaleEnumItem("6", "1亿以上"));
        
        return new EnterpriseScaleEnumVO(employeeScales, revenueScales);
    }
    
    /**
     * 获取企业类型映射
     * @return 企业类型映射
     */
    public Map<String, String> getEnterpriseTypeMap() {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("1", "小微企业");
        typeMap.put("2", "中小企业");
        typeMap.put("3", "中型企业");
        typeMap.put("4", "大型企业");
        return typeMap;
    }
}
