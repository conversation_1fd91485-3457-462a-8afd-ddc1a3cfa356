package com.kbao.kbcelms.enterprise.type.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.tool.util.SysLoginUtils;
import com.kbao.kbcelms.enterprise.type.dao.EnterpriseTypeDao;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseTypeRule;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseTypeEnumVO;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseScaleEnumVO;
import com.kbao.kbcelms.enterprise.type.bean.ScaleEnumItem;
import com.kbao.kbcelms.enterprise.util.EnterpriseTypeRuleUtil;
import org.springframework.data.domain.Sort;import org.springframework.data.mongodb.core.query.Criteria;import org.springframework.data.mongodb.core.query.Query;import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 企业类型业务逻辑层
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class EnterpriseTypeService extends BaseMongoServiceImpl<EnterpriseType, String, EnterpriseTypeDao> {
    
    /**
     * 分页查询企业类型列表
     * @param page 分页查询参数
     * @return 分页结果
     */
    public PageInfo<EnterpriseType> page(PageRequest<EnterpriseType> page) {
        EnterpriseType queryParam = page.getParam();
        Criteria criteria = new Criteria();
        if (StringUtils.hasText(queryParam.getName())) {
            criteria.and("name").regex(queryParam.getName(), "i"); // 忽略大小写的模糊查询
        }
        if (StringUtils.hasText(queryParam.getCode())) {
            criteria.and("code").is(queryParam.getCode());
        }
        Pagination<EnterpriseType> pagination = new Pagination<>(page.getPageNum(), page.getPageSize(), "priority");
        PageInfo<EnterpriseType> pageInfo = super.page(new Query(criteria), pagination);
        // 为每个企业类型生成展示文本
        if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            pageInfo.getList().forEach(this::enrichEnterpriseTypeDisplayText);
        }
        return pageInfo;
    }

    /**
     * 创建企业类型
     * @param enterpriseType 企业类型信息
     * @return 创建的企业类型
     */
    public EnterpriseType add(EnterpriseType enterpriseType) {
        // 校验编码唯一性
        validateCodeUnique(enterpriseType.getCode(), null);
        // 设置创建信息
        Date now = new Date();
        String currentUserId = SysLoginUtils.getUser().getUserId();
        String tenantId = ElmsContext.getTenantId();
        enterpriseType.setCreateTime(now);
        enterpriseType.setUpdateTime(now);
        enterpriseType.setCreateId(currentUserId);
        enterpriseType.setUpdateId(currentUserId);
        enterpriseType.setTenantId(tenantId);
        return dao.save(enterpriseType);
    }

    /**
     * 更新企业类型
     * @param enterpriseType 企业类型信息
     */
    public void updateEnterpriseType(EnterpriseType enterpriseType) {
        // 校验编码唯一性
        validateCodeUnique(enterpriseType.getCode(), enterpriseType.getId());

        // 先查询现有数据
        EnterpriseType existingType = dao.findById(enterpriseType.getId());
        if (existingType == null) {
            throw new BusinessException("企业类型不存在");
        }
        // 只更新需要更新的字段，保留创建信息
        existingType.setName(enterpriseType.getName());
        existingType.setCode(enterpriseType.getCode());
        existingType.setDescription(enterpriseType.getDescription());
        existingType.setPriority(enterpriseType.getPriority());
        existingType.setRules(enterpriseType.getRules());

        // 设置更新信息
        Date now = new Date();
        String currentUserId = SysLoginUtils.getUser().getUserId();
        existingType.setUpdateTime(now);
        existingType.setUpdateId(currentUserId);

        dao.saveOrUpdate(existingType);
    }

    /**
     * 校验编码唯一性
     * @param code 编码
     * @param excludeId 排除的ID
     */
    private void validateCodeUnique(String code, String excludeId) {
        EnterpriseType existingType = dao.findByCodeExcludeId(code, excludeId);
        if (existingType != null) {
            throw new BusinessException("企业类型编码已存在");
        }
    }


    public List<EnterpriseType> getEnterpriseTypes() {
        String tenantId = ElmsContext.getTenantId();
        Criteria criteria = Criteria.where("tenantId").is(tenantId);
        Query query = new Query(criteria).with(Sort.by(Sort.Direction.ASC, "priority"));
        return dao.find(query);
    }

    /**
     * 查询企业类型枚举列表
     * @return 企业类型枚举列表，按priority排序
     */
    public List<EnterpriseTypeEnumVO> getEnterpriseTypeEnum() {
        List<EnterpriseType> enterpriseTypes = this.getEnterpriseTypes();
        if (CollectionUtils.isEmpty(enterpriseTypes)) {
            return new ArrayList<>();
        }
        return enterpriseTypes.stream()
                .map(type -> new EnterpriseTypeEnumVO(type.getName(), type.getCode()))
                .collect(Collectors.toList());
    }

    public Map<String,String> getEnterpriseTypeMap() {
        List<EnterpriseType> enterpriseTypes = this.getEnterpriseTypes();
        if (CollectionUtils.isEmpty(enterpriseTypes)) {
            return new HashMap<>();
        }
        return enterpriseTypes.stream()
                .collect(Collectors.toMap(EnterpriseType::getCode, EnterpriseType::getName));
    }

    /**
     * 查询企业规模枚举列表（人员规模和营收规模）
     * @return 企业规模枚举列表，按priority排序
     */
    public EnterpriseScaleEnumVO getEnterpriseScaleEnum() {
        List<EnterpriseType> enterpriseTypes = this.getEnterpriseTypes();

        List<ScaleEnumItem> employeeScales = new ArrayList<>();
        List<ScaleEnumItem> revenueScales = new ArrayList<>();

        for (EnterpriseType type : enterpriseTypes) {
            List<EnterpriseTypeRule> rules = type.getRules();
            if (CollectionUtils.isEmpty(rules)) {
                continue;
            }
            Map<String, EnterpriseTypeRule> ruleMap = rules.stream()
                .collect(Collectors.toMap(EnterpriseTypeRule::getField, Function.identity()));
            // 处理人员规模
            EnterpriseTypeRule employeeRule = ruleMap.get("employeeCount");
            if (employeeRule != null) {
                String employeeText = EnterpriseTypeRuleUtil.generateEmployeeRangeText(employeeRule);
                employeeScales.add(new ScaleEnumItem(type.getCode(), employeeText));
            }
            // 处理营收规模
            EnterpriseTypeRule revenueRule = ruleMap.get("revenue");
            if (revenueRule != null) {
                String revenueText = EnterpriseTypeRuleUtil.generateRevenueRangeText(revenueRule);
                revenueScales.add(new ScaleEnumItem(type.getCode(), revenueText));
            }
        }
        return new EnterpriseScaleEnumVO(employeeScales, revenueScales);
    }


    /**
     * 为企业类型生成展示文本
     * @param enterpriseType 企业类型
     */
    private void enrichEnterpriseTypeDisplayText(EnterpriseType enterpriseType) {
        List<EnterpriseTypeRule> rules = enterpriseType.getRules();
        Map<String, EnterpriseTypeRule> ruleMap = rules.stream()
            .collect(Collectors.toMap(EnterpriseTypeRule::getField, Function.identity()));
        EnterpriseTypeRule employeeCountRule = ruleMap.get("employeeCount");
        EnterpriseTypeRule revenueRule = ruleMap.get("revenue");
        enterpriseType.setEmployeeRangeText(EnterpriseTypeRuleUtil.generateEmployeeRangeText(employeeCountRule));
        enterpriseType.setRevenueRangeText(EnterpriseTypeRuleUtil.generateRevenueRangeText(revenueRule));
    }
}
