package com.kbao.kbcelms.enterprise.query.record.dao;

import com.alibaba.fastjson.JSON;import com.alibaba.fastjson.JSONObject;import com.kbao.commons.snowflake.IdWorker;import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseCreateRecord;
import com.kbao.kbcelms.enterprise.query.record.model.EnterpriseQueryRecord;import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;import com.kbao.kbcucs.user.model.UserInfoResp;import org.springframework.stereotype.Repository;import java.util.Date;

/**
 * 企业创建信息记录数据访问层
 * <AUTHOR>
 * @date 2025-08-22
 */
@Repository
public class EnterpriseCreateRecordDao extends BaseMongoDaoImpl<EnterpriseCreateRecord, String> {

}
