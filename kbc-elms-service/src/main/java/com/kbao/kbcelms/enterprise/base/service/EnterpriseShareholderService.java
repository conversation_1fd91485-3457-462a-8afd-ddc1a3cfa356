package com.kbao.kbcelms.enterprise.base.service;

import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcelms.enterprise.base.dao.EnterpriseShareholderDao;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseShareholder;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 企业股东信息业务逻辑层
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class EnterpriseShareholderService extends BaseMongoServiceImpl<EnterpriseShareholder, String, EnterpriseShareholderDao> {
    
    /**
     * 根据统一社会信用代码查询企业股东信息
     * @param creditCode 统一社会信用代码
     * @return 企业股东信息列表
     */
    public List<EnterpriseShareholder> findByCreditCode(String creditCode) {
        String tenantId = ElmsContext.getTenantId();
        return dao.findByCreditCode(creditCode, tenantId);
    }
    
    /**
     * 批量保存企业股东信息
     * @param creditCode 统一社会信用代码
     * @param shareholderList 股东信息列表
     */
    public void batchSave(String creditCode, List<EnterpriseShareholder> shareholderList) {
        String tenantId = ElmsContext.getTenantId();
        Date now = new Date();

        // 检查是否有现有数据
        List<EnterpriseShareholder> existingList = dao.findByCreditCode(creditCode, tenantId);
        boolean isUpdate = existingList != null && !existingList.isEmpty();

        // 先删除现有数据
        if (isUpdate) {
            dao.deleteByCreditCode(creditCode, tenantId);
        }

        // 批量保存新数据
        if (shareholderList != null && !shareholderList.isEmpty()) {
            for (EnterpriseShareholder shareholder : shareholderList) {
                shareholder.setCreditCode(creditCode);
                shareholder.setTenantId(tenantId);

                if (isUpdate) {
                    // 如果是更新，保持原有的创建时间逻辑，但设置更新时间
                    shareholder.setCreateTime(now); // 由于删除重建，这里还是设置为当前时间
                    shareholder.setUpdateTime(now);
                } else {
                    // 新建记录
                    shareholder.setCreateTime(now);
                    shareholder.setUpdateTime(now);
                }

                dao.save(shareholder);
            }
        }
    }
}
