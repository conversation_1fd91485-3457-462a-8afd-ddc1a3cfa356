package com.kbao.kbcelms.common.aspect;

import com.kbao.kbcelms.common.annotation.SyncOpportunityLogTime;
import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunitylog.model.OpportunityLog;
import com.kbao.kbcelms.opportunitylog.service.OpportunityLogService;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
/**
 * 用于同步OpportunityLog时间到OpportunityDetail的切面
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class OpportunityLogSyncAspect {

    @Autowired
    private OpportunityLogService opportunityLogService;

    @Autowired
    private OpportunityDetailService opportunityDetailService;

    /**
     * 拦截添加了@SyncOpportunityLogTime注解的方法，在执行后同步日志时间
     */
    @AfterReturning(
            pointcut = "@annotation(syncOpportunityLogTime)",
            returning = "result"
    )
    public void syncLogTimeAfterMethod(JoinPoint joinPoint, SyncOpportunityLogTime syncOpportunityLogTime, Object result) {
        try {
            // 1. 获取操作的OpportunityLog对象或opportunityId
            String opportunityId = getOpportunityIdFromParamsOrResult(joinPoint.getArgs(), result);
            if (EmptyUtils.isEmpty(opportunityId)) {
                log.warn("无法获取opportunityId，同步日志时间失败");
                return;
            }

            Date updateTime = null;
            // 2. 获取最新日志的updateTime
            OpportunityLog latestLog = opportunityLogService.getLatestOpportunityLogByOpportunityId(opportunityId);
            if(latestLog != null && latestLog.getUpdateTime() != null) {
                updateTime = latestLog.getUpdateTime();
            }

            // 3. 更新OpportunityDetail的logTime
            opportunityDetailService.updateLogTimeByOpportunityId(opportunityId, updateTime);
        } catch (Exception e) {
            log.error("同步日志时间到OpportunityDetail失败", e);
            // 不抛出异常，避免影响原业务流程
        }
    }

    /**
     * 从方法参数或返回值中提取opportunityId
     */
    private String getOpportunityIdFromParamsOrResult(Object[] args, Object result) {
        // 处理新增/修改方法（返回值为OpportunityLog）
        if (result instanceof OpportunityLog) {
            return ((OpportunityLog) result).getOpportunityId();
        }

        // 处理删除方法（参数可能包含OpportunityLog或opportunityId）
        for (Object arg : args) {
            if (arg instanceof OpportunityLog) {
                return ((OpportunityLog) arg).getOpportunityId();
            } else if (arg instanceof String && ((String) arg).startsWith("OP")) {
                return arg.toString();
            }
        }
        return null;
    }
}