package com.kbao.kbcelms.common.service;

import com.kbao.kbcelms.bascode.service.BasCodeService;
import com.kbao.kbcelms.bascode.vo.BaseCodeTreeVO;
import com.kbao.kbcelms.common.enums.CascadeDictEnum;
import com.kbao.kbcelms.common.enums.SimpleDictEnum;
import com.kbao.kbcelms.common.vo.CascadeDictItem;
import com.kbao.kbcelms.common.vo.DictItem;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseScaleEnumVO;
import com.kbao.kbcelms.enterprise.type.bean.ScaleEnumItem;
import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;
// import com.kbao.kbcelms.industry.service.IndustryService;
// import com.kbao.kbcelms.industry.vo.IndustryTreeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通用字典服务
 * <AUTHOR>
 */
@Service
public class CommonDictService {
    
    @Autowired
    private EnterpriseTypeService enterpriseTypeService;
    
    @Autowired
    private BasCodeService basCodeService;
    
    // @Autowired
    // private IndustryService industryService;
    
    /**
     * 获取普通字典
     * @param enumCode 字典编码
     * @return 字典列表
     */
    public List<DictItem> getSimpleDict(String enumCode) {
        if (!SimpleDictEnum.isSupported(enumCode)) {
            throw new IllegalArgumentException("不支持的字典类型: " + enumCode);
        }
        
        switch (enumCode) {
            case "employeeScales":
                return getEmployeeScales();
            case "revenueScales":
                return getRevenueScales();
            default:
                throw new IllegalArgumentException("不支持的字典类型: " + enumCode);
        }
    }
    
    /**
     * 获取级联字典
     * @param enumCode 字典编码
     * @return 级联字典列表
     */
    public List<CascadeDictItem> getCascadeDict(String enumCode) {
        if (!CascadeDictEnum.isSupported(enumCode)) {
            throw new IllegalArgumentException("不支持的字典类型: " + enumCode);
        }
        
        switch (enumCode) {
            case "basCode":
                return getBasCodeTree();
            case "industry":
                // TODO: 实现行业树查询
                return new ArrayList<>();
            default:
                throw new IllegalArgumentException("不支持的字典类型: " + enumCode);
        }
    }
    
    /**
     * 获取员工规模字典
     */
    private List<DictItem> getEmployeeScales() {
        EnterpriseScaleEnumVO scaleEnum = enterpriseTypeService.getEnterpriseScaleEnum();
        if (scaleEnum == null || scaleEnum.getEmployeeScales() == null) {
            return new ArrayList<>();
        }
        
        return scaleEnum.getEmployeeScales().stream()
                .map(item -> new DictItem(item.getCode(), item.getValue()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取营收规模字典
     */
    private List<DictItem> getRevenueScales() {
        EnterpriseScaleEnumVO scaleEnum = enterpriseTypeService.getEnterpriseScaleEnum();
        if (scaleEnum == null || scaleEnum.getRevenueScales() == null) {
            return new ArrayList<>();
        }
        
        return scaleEnum.getRevenueScales().stream()
                .map(item -> new DictItem(item.getCode(), item.getValue()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取基础地区代码树
     */
    private List<CascadeDictItem> getBasCodeTree() {
        List<BaseCodeTreeVO> baseCodeTree = basCodeService.getBaseCodeTree();
        if (baseCodeTree == null) {
            return new ArrayList<>();
        }
        
        return baseCodeTree.stream()
                .map(this::convertBaseCodeToCascadeDict)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取行业分类树
     * TODO: 待实现
     */
    // private List<CascadeDictItem> getIndustryTree() {
    //     List<IndustryTreeVO> industryTree = industryService.getIndustryTree();
    //     if (industryTree == null) {
    //         return new ArrayList<>();
    //     }
    //
    //     return industryTree.stream()
    //             .map(this::convertIndustryToCascadeDict)
    //             .collect(Collectors.toList());
    // }
    
    /**
     * 转换BaseCodeTreeVO为CascadeDictItem
     */
    private CascadeDictItem convertBaseCodeToCascadeDict(BaseCodeTreeVO baseCode) {
        CascadeDictItem item = new CascadeDictItem(baseCode.getCode(), baseCode.getName());
        
        if (baseCode.getChildren() != null && !baseCode.getChildren().isEmpty()) {
            List<CascadeDictItem> children = baseCode.getChildren().stream()
                    .map(this::convertBaseCodeToCascadeDict)
                    .collect(Collectors.toList());
            item.setChildren(children);
        }
        
        return item;
    }
    
    /**
     * 转换IndustryTreeVO为CascadeDictItem
     * TODO: 待实现
     */
    // private CascadeDictItem convertIndustryToCascadeDict(IndustryTreeVO industry) {
    //     CascadeDictItem item = new CascadeDictItem(industry.getCode(), industry.getName());
    //
    //     if (industry.getChildren() != null && !industry.getChildren().isEmpty()) {
    //         List<CascadeDictItem> children = industry.getChildren().stream()
    //                 .map(this::convertIndustryToCascadeDict)
    //                 .collect(Collectors.toList());
    //         item.setChildren(children);
    //     }
    //
    //     return item;
    // }
}
