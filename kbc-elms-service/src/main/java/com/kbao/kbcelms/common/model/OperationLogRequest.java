package com.kbao.kbcelms.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: OperationLogRequest
 * @Description: 操作日志通用请求包装类
 * @Author: luobb
 * @Date: 2025/8/4 10:00
 * @Version: 1.0
 */
@Data
@ApiModel(description = "操作日志通用请求包装类")
public class OperationLogRequest<T> {
    @ApiModelProperty(value = "业务数据", required = true)
    private T data;

    @ApiModelProperty(value = "变更备注（可选）")
    private String changeRemark;
}