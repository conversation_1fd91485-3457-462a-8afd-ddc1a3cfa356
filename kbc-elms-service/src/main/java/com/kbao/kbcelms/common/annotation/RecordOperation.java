package com.kbao.kbcelms.common.annotation;

import java.lang.annotation.*;

/**
 * 操作记录注解，用于标记需要记录操作的方法
 * @luobb
 *
 * 示例如下：
 * @RecordOperation(
 *             businessType = "OpportunityTeam",
 *             operationType = "OPPORTUNITY_CHANGE_MANAGER",
 *             changeHandler = "OpportunityManagerChangeHandler",
 *             needChangeRemark = true,
 *             remarkParamIndex = 4  // 变更说明参数索引
 *     )
 *  public void changeManager(List<OpportunityTeamMember> members,String updateId,String tenantId,String changeRemark)
 *
 * 其中：
 * businessType：业务类型，一般记录操作对象，如OpportunityTeam(机会项目成员)
 * operationType：操作类型，参考如OPPORTUNITY_CHANGE_MANAGER(机会项目负责人变更)
 * changeHandler：变更处理器Bean名称
 * needChangeRemark：是否需要变更说明
 * remarkParamIndex：变更说明参数索引，当needChangeRemark=true时有效
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RecordOperation {
    /**
     * 业务类型,一般记录操作对象,如OpportunityTeam(机会项目成员)
     */
    String businessType();

    /**
     * 操作类型,参考如OPPORTUNITY_CHANGE_MANAGER(机会项目负责人变更)
     */
    String operationType();

    /**
     * 机会ID参数索引
     */
    int opportunityIdParamIndex() default 0;

    /**
     * 变更处理器Bean名称
     */
    String changeHandler();

    /**
     * 是否需要变更说明
     */
    boolean needChangeRemark() default false;

    /**
     * 变更说明参数索引，当needChangeRemark=true时有效
     */
    int remarkParamIndex() default -1;
}