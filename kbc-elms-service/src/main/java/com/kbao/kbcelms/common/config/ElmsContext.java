package com.kbao.kbcelms.common.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public final class ElmsContext {

	private static InheritableThreadLocal<UserInfo> userMap = new InheritableThreadLocal<>();

	private static InheritableThreadLocal<String> tenantId = new InheritableThreadLocal<String>();

	public static UserInfo getUser(){
		return userMap.get();
	}

	public static void setUser(UserInfo userInfo){
		userMap.set(userInfo);
	}

	public static String getTenantId() {
		return tenantId.get();
	}

	public static void setTenantId(String tId) {
		tenantId.set(tId);
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class UserInfo {
		private String userId;
		private String userName;
		private String nickName;
		private String tenantId;
		private String token;
		private String agentCode;
		private String agentName;
		private String phone;
        protected String legalCode;
        protected String legalName;
        protected String salesCenterCode;
        protected String salesCenterName;
	}
}