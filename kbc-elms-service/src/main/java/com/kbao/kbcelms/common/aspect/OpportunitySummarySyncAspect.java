package com.kbao.kbcelms.common.aspect;

import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunitysummary.model.OpportunitySummary;
import com.kbao.kbcelms.opportunitysummary.service.OpportunitySummaryService;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 机会总结操作同步切面 - 增删改时更新summaryTime
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class OpportunitySummarySyncAspect {

    @Autowired
    private OpportunityDetailService opportunityDetailService;

    @Autowired
    private OpportunitySummaryService opportunitySummaryService;

    /**
     * 定义切入点：匹配OpportunitySummaryService的增删改方法
     */
    @Pointcut("execution(* com.kbao.kbcelms.opportunitysummary.service.OpportunitySummaryService.add*(..)) || " +
            "execution(* com.kbao.kbcelms.opportunitysummary.service.OpportunitySummaryService.update*(..)) || " +
            "execution(* com.kbao.kbcelms.opportunitysummary.service.OpportunitySummaryService.delete*(..))")
    public void syncSummaryTimePointcut() {}

    /**
     * 后置通知：在目标方法执行后调用更新方法
     */
    @AfterReturning(pointcut = "syncSummaryTimePointcut()", returning = "result")
    public void afterSummaryOperation(JoinPoint joinPoint, Object result) {
        // 1. 获取操作的OpportunityLog对象或opportunityId
        String opportunityId = getOpportunityIdFromParamsOrResult(joinPoint.getArgs(), result);
        if (EmptyUtils.isEmpty(opportunityId)) {
            log.warn("无法获取opportunityId，同步日志时间失败");
            return;
        }

        Date updateTime = null;
        // 2. 获取最新日志的updateTime
        OpportunitySummary latestSummary = opportunitySummaryService.getLatestOpportunitySummaryByOpportunityId(opportunityId);
        if(latestSummary != null && latestSummary.getUpdateTime() != null) {
            updateTime = latestSummary.getUpdateTime();
        }

        // 3. 更新OpportunityDetail的logTime
        opportunityDetailService.updateSummaryTimeByOpportunityId(opportunityId, updateTime);
    }

    /**
     * 从方法参数或返回值中提取opportunityId
     */
    private String getOpportunityIdFromParamsOrResult(Object[] args, Object result) {
        // 处理新增/修改方法（返回值为OpportunityLog）
        if (result instanceof OpportunitySummary) {
            return ((OpportunitySummary) result).getOpportunityId();
        }

        // 处理删除方法（参数可能包含OpportunityLog或opportunityId）
        for (Object arg : args) {
            if (arg instanceof OpportunitySummary) {
                return ((OpportunitySummary) arg).getOpportunityId();
            } else if (arg instanceof String && ((String) arg).startsWith("OP")) {
                return arg.toString();
            }
        }
        return null;
    }
}