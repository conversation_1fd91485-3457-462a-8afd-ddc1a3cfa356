package com.kbao.kbcelms.common.handler;

import com.kbao.kbcelms.operationlog.entity.OperationLog;

import java.util.List;
import java.util.Map;

/**
 * 操作变更处理器接口，各业务模块可自定义实现
 * @luobb
 */
public interface OperationChangeHandler {

    /**
     * 获取变更前数据
     */
    Object getBeforeData(Object[] args);

    /**
     * 获取变更后数据
     */
    Object getAfterData(Object[] args, Object result);

    /**
     * 填充日志内容
     */
    List<OperationLog> fillLogContent(OperationLog log, Object beforeData, Object afterData);

    /**
     * 比较数据差异，生成变更内容
     */
    Map<String, Object> compareChanges(Object beforeData, Object afterData);
}