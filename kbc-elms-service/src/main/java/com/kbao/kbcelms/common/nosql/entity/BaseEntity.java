package com.kbao.kbcelms.common.nosql.entity;

import com.kbao.kbcucs.context.RequestContext;
import com.kbao.tool.util.DateUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础实体类，封装公共的创建人、创建时间字段及自动赋值逻辑
 * 所有需要这些字段的实体类都可以继承此类
 * @Author: luobb
 */
public abstract class BaseEntity implements Serializable {

    /**
     * 创建人
     */
    protected String creatorId;

    /**
     * 创建日期
     */
    protected Date createTime;

    /**
     * 实体保存前自动赋值
     */
    public void prePersist() {
        // 避免重复赋值（如果已经手动设置过则不覆盖）
        if (this.createTime == null) {
            this.createTime = DateUtils.getCurrentDate();
        }
        if (this.creatorId == null) {
            this.creatorId = RequestContext.UserId.get();
        }
    }

    // Getter和Setter方法
    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
