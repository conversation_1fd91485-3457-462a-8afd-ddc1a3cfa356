package com.kbao.kbcelms.common.enums;

/**
 * <AUTHOR>
 * @Description 表单配置分类枚举
 * @Date 2025-08-15
 */
public enum FormConfigTypeEnum {
    
    /**
     * 企业信息
     */
    ENTERPRISE_INFO("1", "企业信息"),
    
    /**
     * 员福配置
     */
    EMPLOYEE_WELFARE("2", "员福配置"),
    
    /**
     * 企业补充信息
     */
    ENTERPRISE_SUPPLEMENT("3", "企业补充信息");
    
    private final String code;
    private final String name;
    
    FormConfigTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 枚举
     */
    public static FormConfigTypeEnum getByCode(String code) {
        for (FormConfigTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
    
    /**
     * 根据编码获取名称
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(String code) {
        FormConfigTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getName() : "未知";
    }
    
    /**
     * 验证编码是否有效
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
