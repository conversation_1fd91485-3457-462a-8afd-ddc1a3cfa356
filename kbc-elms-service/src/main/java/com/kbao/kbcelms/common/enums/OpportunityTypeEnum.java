package com.kbao.kbcelms.common.enums;

/**
 * <AUTHOR>
 * @Description 机会类型枚举
 * @Date 2025-08-19
 */
public enum OpportunityTypeEnum {

    /**
     * 员服
     */
    EMPLOYEE_WELFARE("1", "员服"),

    /**
     * 综合
     */
    COMPREHENSIVE("2", "综合");

    private final String code;
    private final String name;

    OpportunityTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 枚举
     */
    public static OpportunityTypeEnum getByCode(String code) {
        for (OpportunityTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(String code) {
        OpportunityTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getName() : "未知";
    }

    /**
     * 验证编码是否有效
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为员服类型
     * @param code 编码
     * @return 是否为员服类型
     */
    public static boolean isEmployeeWelfare(String code) {
        return EMPLOYEE_WELFARE.getCode().equals(code);
    }

    /**
     * 判断是否为综合类型
     * @param code 编码
     * @return 是否为综合类型
     */
    public static boolean isComprehensive(String code) {
        return COMPREHENSIVE.getCode().equals(code);
    }
}
