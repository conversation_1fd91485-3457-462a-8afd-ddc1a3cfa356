package com.kbao.kbcelms.common.aspect;


import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcelms.common.annotation.RecordOperation;
import com.kbao.kbcelms.common.handler.OperationChangeHandler;
import com.kbao.kbcelms.operationlog.entity.OperationLog;
import com.kbao.kbcelms.operationlog.service.OperationLogService;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import com.kbao.tool.util.EmptyUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;
import java.util.List;

/**
 * 操作日志切面
 * @luobb
 */
@Aspect
@Component
public class OperationLogAspect {

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private OpportunityService opportunityService;

    @Autowired
    private ApplicationContext applicationContext;

    @Around("@annotation(com.kbao.kbcelms.common.annotation.RecordOperation)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名和注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        RecordOperation annotation = method.getAnnotation(RecordOperation.class);

        // 获取方法参数
        Object[] args = joinPoint.getArgs();

        // 获取变更处理器
        OperationChangeHandler changeHandler = applicationContext.getBean(annotation.changeHandler(), OperationChangeHandler.class);

        // 获取变更前数据
        Object beforeData = changeHandler.getBeforeData(args);

        // 获取变更说明
        String changeRemark = null;
        if (annotation.needChangeRemark() && annotation.remarkParamIndex() >= 0
                && args.length > annotation.remarkParamIndex()) {
            Object remarkObj = args[annotation.remarkParamIndex()];
            if (remarkObj != null) {
                changeRemark = remarkObj.toString();
            }
        }

        // 获取机会Id
        String opportunityId = null;
        if (annotation.opportunityIdParamIndex() >= 0 && args.length > annotation.opportunityIdParamIndex()) {
            Object opportunityIdObj = args[annotation.opportunityIdParamIndex()];
            if (opportunityIdObj != null) {
                opportunityId = opportunityIdObj.toString();
            }
        }

        // 初始化操作结果
        String operationResult = "SUCCESS";
        String resultMessage = "操作成功";
        Object result = null;

        try {
            // 执行原方法
            result = joinPoint.proceed();
        } catch (Exception e) {
            // 捕获异常，记录失败结果
            operationResult = "FAIL";
            resultMessage = e.getMessage();
            throw e;
        } finally {
            // 获取变更后数据
            Object afterData = changeHandler.getAfterData(args, result);

            // 创建操作日志
            OperationLog log = new OperationLog();
            log.setBusinessType(annotation.businessType());
            log.setOperationType(annotation.operationType());

            // 设置操作结果
            log.setOperationResult(operationResult);
            log.setResultMessage(resultMessage);

            // 初始化日志
            operationLogService.initLog(log);

            //如果有机会Id,则设置
            if(EmptyUtils.isNotEmpty(opportunityId)){
                log.setOperatorId(opportunityId);
            }

            // 设置变更说明
            log.setChangeRemark(changeRemark);

            // 通过变更处理器填充日志内容
            // 需要填充机会项目名称
            // 如是批量操作,则返回多个日志
            List<OperationLog> logs = changeHandler.fillLogContent(log, beforeData, afterData);

            // 保存操作日志
            if(EmptyUtils.isNotEmpty(logs)){
               for(OperationLog logItem : logs){
                   logItem.setId(IdWorker.getIdStr());
                   // 判断并自动填充机会名称
                   if(EmptyUtils.isNotEmpty(logItem.getOpportunityId()) && EmptyUtils.isEmpty(logItem.getOpportunityName())){
                       Opportunity opportunity = opportunityService.selectByPrimaryKey(logItem.getOpportunityId());
                       logItem.setOpportunityName(opportunity.getOpportunityName());
                   }
                   operationLogService.save(logItem);
               }
            }
        }

        return result;
    }
}