package com.kbao.kbcelms.formula.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.formula.entity.FormulaVariable;
import com.kbao.kbcelms.formula.vo.FormulaVariableVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公式变量Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface FormulaVariableMapper extends BaseMapper<FormulaVariable, Long> {

    /**
     * 根据公式ID查询变量列表
     *
     * @param formulaId 公式ID
     * @return 变量列表
     */
    List<FormulaVariableVO> selectByFormulaId(@Param("formulaId") Long formulaId);

    /**
     * 根据公式ID删除变量
     *
     * @param formulaId 公式ID
     * @return 影响行数
     */
    int deleteByFormulaId(@Param("formulaId") Long formulaId);

    /**
     * 批量插入变量
     *
     * @param variables 变量列表
     * @return 影响行数
     */
    int batchInsert(@Param("variables") List<FormulaVariable> variables);

    /**
     * 检查变量名称是否存在
     *
     * @param formulaId 公式ID
     * @param name 变量名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkNameExists(@Param("formulaId") Long formulaId, @Param("name") String name, @Param("excludeId") Long excludeId);
}
