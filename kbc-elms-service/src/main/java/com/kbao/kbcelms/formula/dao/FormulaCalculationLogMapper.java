package com.kbao.kbcelms.formula.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.formula.entity.FormulaCalculationLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公式计算记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface FormulaCalculationLogMapper extends BaseMapper<FormulaCalculationLog, Long> {

    /**
     * 根据公式ID查询计算记录
     *
     * @param formulaId 公式ID
     * @param limit 限制数量
     * @return 计算记录列表
     */
    List<FormulaCalculationLog> selectByFormulaId(@Param("formulaId") Long formulaId, @Param("limit") Integer limit);

    /**
     * 根据用户ID查询计算记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 计算记录列表
     */
    List<FormulaCalculationLog> selectByUserId(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 统计公式使用次数
     *
     * @param formulaId 公式ID
     * @return 使用次数
     */
    int countByFormulaId(@Param("formulaId") Long formulaId);

    /**
     * 清理过期记录
     *
     * @param days 保留天数
     * @return 影响行数
     */
    int deleteExpiredLogs(@Param("days") Integer days);

    /**
     * 统计所有计算记录数量
     *
     * @return 总数
     */
    int countAll();
}
