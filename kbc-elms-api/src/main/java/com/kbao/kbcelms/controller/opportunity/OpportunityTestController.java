package com.kbao.kbcelms.controller.opportunity;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.opportunity.service.OpportunityTestService;
import com.kbao.kbcelms.opportunity.vo.OpportunityCreateRequestVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityCreateResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机会测试控制器
 * 专门用于处理机会相关的测试功能
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/opportunity/test")
@Api(tags = "机会测试接口")
public class OpportunityTestController {

    @Autowired
    private OpportunityTestService opportunityTestService;

    @LogAnnotation(module = "机会测试", recordRequestParam = true, action = "新增机会", desc = "新增机会并设置为已提交状态")
    @ApiOperation(value = "新增机会", notes = "新增机会，自动根据agentCode获取机构信息，设置企业ID为4，状态为已提交")
    @PostMapping("/create")
    public Result<OpportunityCreateResponseVO> createOpportunity(@RequestBody OpportunityCreateRequestVO request) {
        OpportunityCreateResponseVO response = opportunityTestService.createOpportunity(request);
        return Result.succeed(response, "机会创建成功");
    }
}
