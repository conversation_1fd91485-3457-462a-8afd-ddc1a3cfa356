package com.kbao.kbcelms.controller.riskMatrix;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireRequest;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireVO;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixReportRequest;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixResultDTO;
import com.kbao.kbcelms.riskMatrix.model.RiskMatrixReportMongo;
import com.kbao.kbcelms.riskMatrix.service.RiskMatrixReportService;
import com.kbao.kbcelms.riskMatrix.service.RiskMatrixService;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: kbc-elms
 * @description: 风险信息
 * @author: husw
 * @create: 2025-08-14 15:35
 **/
@Slf4j
@RestController
@RequestMapping("/api/h5/questionnaire")
@Api(tags = "H5端风险信息")
public class RiskMatrixApiController extends BaseController {

    @Autowired
    private RiskMatrixService riskMatrixService;

    @Autowired
    private RiskMatrixReportService riskMatrixReportService;

    @ApiOperation(value = "根据企业类型查询问卷", notes = "根据当前登录用户的企业类型查询对应的问卷详情")
    @PostMapping("/loadRisMatrixReport")
    @LogAnnotation(module = "H5端问卷管理", recordRequestParam = true, action = "查询", desc = "根据企业类型查询问卷")
    public Result<List<RiskMatrixResultDTO>> getByEnterpriseType(@RequestBody @Validated RiskMatrixReportRequest request) {
        List<RiskMatrixResultDTO> resultDTOS = riskMatrixService.loadRisMatrixReport(request.getEnterpriseId().longValue(), request.getDtType());
        return Result.succeed(resultDTOS, "查询成功");
    }

    @GetMapping("/report/{enterpriseId}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "获取最新风险矩阵报告")
    public Result<RiskMatrixReportMongo> getLatestReport(@PathVariable Long enterpriseId) {
        try {
            RiskMatrixReportMongo result = riskMatrixReportService.getRadarChartByEnterpriseId(enterpriseId);
            if (result == null) {
                return Result.failed("未找到该企业的风险矩阵报告");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("获取最新风险矩阵报告失败，企业ID：{}", enterpriseId, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

}
