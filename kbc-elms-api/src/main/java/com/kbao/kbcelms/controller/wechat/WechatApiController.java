package com.kbao.kbcelms.controller.wechat;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.wechat.response.SignatureVO;
import com.kbao.kbcelms.wechat.UrlReq;
import com.kbao.kbcelms.wechat.WxAuthService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/noauth/base")
public class WechatApiController extends BaseController {
    @Autowired
    private WxAuthService wxAuthService;

    @LogAnnotation(module = "无认证接口管理", action = "其他", desc = "获取微信签字")
    @ApiOperation(value = "获取微信签字", notes = "获取微信签字")
    @PostMapping("/wechat/signature")
    public Result<SignatureVO> signature(@RequestBody @Valid UrlReq url) {
        SignatureVO signatureVO = wxAuthService.getSignature(url.getUrl());
        return Result.succeedWith(signatureVO);
    }

    @ApiOperation(value = "微信后台授权", notes = "微信后台授权")
    @GetMapping("/wechat/auth")
    public void wechatAuth(@RequestParam("redirectUrl") String redirectUrl){
        wxAuthService.wechatAuth(redirectUrl, request, response);
    }
}
