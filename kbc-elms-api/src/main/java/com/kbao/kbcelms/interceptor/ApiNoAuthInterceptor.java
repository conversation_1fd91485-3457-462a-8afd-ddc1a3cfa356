package com.kbao.kbcelms.interceptor;

import com.kbao.kbcbsc.interceptor.TenantInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class ApiNoAuthInterceptor extends TenantInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        return super.preHandle(request, response, handler);
    }
}
