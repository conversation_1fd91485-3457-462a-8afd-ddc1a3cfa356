package com.kbao.kbcelms.interceptor;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.common.config.ElmsContext;import com.kbao.kbcucs.service.UserInfoAccessService;
import com.kbao.kbcucs.context.RequestContext;
import com.kbao.kbcucs.interceptor.UcsAccessInterceptorAdapter;
import com.kbao.kbcucs.user.model.UserInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

@Slf4j
@Component
public class ApiUserAuthInterceptor extends UcsAccessInterceptorAdapter {
    @Autowired
    private UserInfoAccessService userInfoAccessService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("token");
        String tenantId = request.getHeader("tenantId");
        UserInfoResp userInfoResp = null;
        if (StringUtils.isNotEmpty(token) && StringUtils.isNotEmpty(tenantId)) {
            if(!super.preHandle(request, response, handler)){
                return false;
            }
            userInfoResp = RequestContext.UserInfoResp.get();
        } else {
            if (StringUtils.isEmpty(token)) {
                token = request.getParameter("token");
            }
            if (StringUtils.isEmpty(tenantId)) {
                tenantId = request.getParameter("tenantId");
            }
            if (StringUtils.isEmpty(token) || StringUtils.isEmpty(tenantId)) {
                response.setHeader("Content-Type", "application/json;charset=UTF-8");
                PrintWriter writer = response.getWriter();
                writer.write(JSONObject.toJSONString(Result.failedWith(null, ResultStatusEnum.UNAUTHORIZED.getStatus(), "登录已失效")));
                return false;
            }
            RequestContext.Token.set(token);
            userInfoResp = userInfoAccessService.getUserInfoByToken(token, tenantId);
            if (userInfoResp != null) {
                RequestContext.UserInfoResp.set(userInfoResp);
                RequestContext.UserId.set(userInfoResp.getId());
            }
        }
        if(userInfoResp == null){
            response.setHeader("Content-Type", "application/json;charset=UTF-8");
            PrintWriter writer = response.getWriter();
            writer.write(JSONObject.toJSONString(Result.failedWith(null, ResultStatusEnum.UNAUTHORIZED.getStatus(), "登录已失效")));
            return false;
        }
        ElmsContext.UserInfo user = new ElmsContext.UserInfo();
        BeanUtil.copyProperties(userInfoResp, user);
        user.setUserId(userInfoResp.getId());
        user.setToken(RequestContext.Token.get());
        ElmsContext.setUser(user);
        ElmsContext.setTenantId(userInfoResp.getTenantId());
        return true;
    }

}
